# Spring
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${nacos.url}
        username: nacos
        password: ${nacos.password}
        # 命名空间
        namespace: platform-${spring.profiles.active}
        group: ${nacos.group:DEFAULT_GROUP}
      config:
        # 配置中心地址
        server-addr: ${nacos.url}
        # 配置文件格式
        file-extension: yml
        username: nacos
        password: ${nacos.password}
        # 命名空间
        namespace: platform-${spring.profiles.active}
        group: ${nacos.group:DEFAULT_GROUP}
        # 共享配置
        shared-configs:
          - { dataId: common-application.yml,refresh: true }
          - { dataId: common-cache.yml,refresh: true }
          - { dataId: common-datasource.yml,refresh: true }
          - { dataId: common-mq.yml,refresh: true }

