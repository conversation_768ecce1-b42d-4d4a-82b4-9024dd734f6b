# Spring
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${nacos.url}
        username: ${nacos.username}
        password: ${nacos.password}
        # 命名空间
        namespace: ${discovery.namespace}
        group: ${nacos.group:DEFAULT_GROUP}
      config:
        # 配置中心地址
        server-addr: ${nacos.url}
        # 配置文件格式
        file-extension: yml
        username: ${nacos.username}
        password: ${nacos.password}
        # 命名空间
        namespace: ${config.namespace}
        group: ${nacos.group:DEFAULT_GROUP}
        # 共享配置
        shared-configs:
          - { dataId: common-application.yml,refresh: true }
          - { dataId: common-cache.yml,refresh: true }
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            dataId: sentinel-platform-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
