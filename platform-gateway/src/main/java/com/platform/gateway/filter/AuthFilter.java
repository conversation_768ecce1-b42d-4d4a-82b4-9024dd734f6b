package com.platform.gateway.filter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.config.properties.JwtProperties;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.common.core.constant.TokenConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.utils.JwtUtils;
import com.platform.common.core.utils.ServletUtils;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.domain.response.SysApiResp;
import com.platform.system.api.domain.response.SysMenuResp;
import com.platform.system.api.model.LoginUser;
import com.platform.system.api.model.SysApiDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.PatternMatchUtils;
import org.springframework.web.server.ServerWebExchange;
import com.platform.gateway.config.properties.IgnoreWhiteProperties;
import io.jsonwebtoken.Claims;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 网关鉴权
 *
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered
{
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Autowired
    private CacheClient redisService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain)
    {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();

        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites()))
        {
            return chain.filter(exchange);
        }
        String token = getToken(request);
        if (StringUtils.isEmpty(token))
        {
            return unauthorizedResponse(exchange, ExceptionEnum.TOKEN_NULL);
        }
        LoginUser loginUser = null;
        try{
            loginUser = TokenUtils.checkToken(token);
        }catch (ServiceException e){
            return ServletUtils.webFluxResponseWriter(exchange.getResponse(), e.getMessage(), e.getCode());
        }

        //从缓存中获取菜单的集合
        String apiStr = redisService.getCacheObject(CacheConstants.SYSTEM_API);
        List<SysApiResp> apiList = JSONUtil.toList(apiStr, SysApiResp.class);
        //判断当前请求路径是否配置在API权限中，如配置了，则进行权限过滤校验
        boolean flagApi = apiList.stream()
                .anyMatch(x -> PatternMatchUtils.simpleMatch(x.getPerms(), url));
        List<SysMenuResp> menuList = redisService.getCacheList(CacheConstants.SYSTEM_MENU);
        boolean flagMenu = false;
        if (ObjectUtil.isNotNull(menuList)){
            //判断当前请求路径是否配置在菜单中，如配置了，则进行权限过滤校验
            flagMenu = menuList.stream()
                    .anyMatch(x -> PatternMatchUtils.simpleMatch(x.getPerms(), url));
        }
        if (flagApi || flagMenu){
            //配置了权限校验
            //序列化用户信息对象
            List<SysApiDTO> permissionList = loginUser.getPermissions();
            String requestMethod = request.getMethod().name();
            if (!hasPermi(permissionList, url, requestMethod)){
                log.info("API接口地址[{}]无访问权限", url);
                //当前用户没有访问权限
                return unauthorizedResponse(exchange, ExceptionEnum.NO_PERMISSION);
            }
        }

        Claims claims = JwtUtils.parseToken(token);
        String userkey = JwtUtils.getUserKey(claims);
        String userid = JwtUtils.getUserId(claims);
        String username = JwtUtils.getUserName(claims);
        // 设置用户信息到请求
        addHeader(mutate, SecurityConstants.USER_KEY, userkey);
        addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userid);
        addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
        // 内部请求来源参数清除
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value)
    {
        if (value == null)
        {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name)
    {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, ExceptionEnum exceptionEnum)
    {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), exceptionEnum.getMsg(), exceptionEnum.getCode());
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request)
    {
        String token = request.getHeaders().getFirst(JwtProperties.tokenHeader);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    @Override
    public int getOrder()
    {
        return -200;
    }

    /**
     * @Description: 判断是否拥有此权限
     * @author: tr
     * @Date: 2024/2/19 17:31
     * @param: [authorities, permission]
     * @returnValue: boolean
     */
    private boolean hasPermi(List<SysApiDTO> authorities, String url, String requestMethod)
    {
        //判断接口的请求方式是否一致，同时，判断接口的url是否一致
        return authorities.stream()
                .anyMatch(x -> StrUtil.equals(x.getRequestMethod(), requestMethod) &&
                        PatternMatchUtils.simpleMatch(x.getPerms(), url));
    }
}