package com.platform.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 网关启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class PlatFormGatewayApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(PlatFormGatewayApplication.class, args);
        System.out.println("网关服务启动成功");
    }
}
