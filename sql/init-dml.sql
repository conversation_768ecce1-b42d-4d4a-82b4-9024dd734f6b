
INSERT INTO sys_app (id, name,code,icon,`type`,url,order_num,frame_flag,public_key,private_key,remark,status,del_flag,create_by,create_time,update_by,update_time) VALUES

    (3, '应用支撑平台','platform','hndx-app/test/index_content_brace_1711986808002.png','1','user/org/department',1,0,'4tubkmxoes7aupfs','izct9mjwu9mgnu2a4v2n199dl2gsxk3y',NULL,'0','0','admin','2024-03-29 14:29:39','admin','2024-04-07 17:21:39');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(41, '1', 0, '/0/', '用户管理', NULL, NULL, '0', '0', 'admin', '2024-03-26 18:16:33', 'admin', '2024-04-07 17:28:45');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(42, '1', 41, '/0/41/', '组织架构管理', NULL, NULL, '0', '0', 'admin', '2024-03-26 18:16:45', 'admin', '2024-04-07 17:28:37');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(47, '1', 0, '/0/', '地址表（含省客支）', NULL, NULL, '0', '0', 'admin', '2024-03-26 18:26:44', 'admin', '2024-03-26 18:26:44');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(48, '2', 47, '/0/47/', '查询', '/system/country/list', '查询', '0', '0', 'admin', '2024-03-26 18:26:58', 'message', '2024-03-27 17:12:19');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(56, '1', 41, '/0/41/', '角色管理', NULL, NULL, '0', '0', 'admin', '2024-04-07 17:28:55', 'admin', '2024-04-07 17:28:55');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(57, '1', 41, '/0/41/', '角色组管理', NULL, NULL, '0', '0', 'admin', '2024-04-07 17:29:04', 'admin', '2024-04-07 17:29:04');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(58, '1', 42, '/0/41/42/', '部门管理', NULL, NULL, '0', '0', 'admin', '2024-04-07 17:29:25', 'admin', '2024-04-07 17:29:25');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(59, '1', 42, '/0/41/42/', '机构管理', NULL, NULL, '0', '0', 'admin', '2024-04-07 17:29:33', 'admin', '2024-04-07 17:29:33');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(60, '1', 42, '/0/41/42/', '成员管理', NULL, NULL, '0', '0', 'admin', '2024-04-07 17:29:53', 'admin', '2024-04-07 17:29:53');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(61, '1', 0, '/0/', '系统设置', NULL, NULL, '0', '0', 'admin', '2024-04-07 19:13:20', 'admin', '2024-04-07 19:13:20');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(62, '1', 61, '/0/61/', '应用管理', NULL, NULL, '0', '0', 'admin', '2024-04-07 19:13:42', 'admin', '2024-04-07 19:13:42');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(63, '2', 62, '/0/61/62/', '查询', 'system:app:page', '', '0', '0', 'admin', '2024-04-07 19:16:51', 'admin', '2024-04-07 19:24:41');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(65, '1', 0, '/0/', '系统监控', NULL, NULL, '0', '0', 'admin', '2024-04-07 19:24:56', 'admin', '2024-04-07 19:24:56');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(66, '1', 65, '/0/65/', '在线用户', NULL, NULL, '0', '0', 'admin', '2024-04-07 19:25:10', 'admin', '2024-04-07 19:25:10');

INSERT INTO sys_api

(id, `type`, parent_id, parent_id_path, name, perms, remark, status, del_flag, create_by, create_time, update_by, update_time)

VALUES(67, '2', 57, '/0/41/57/', '查询', '/system/roleGroup/list', NULL, '0', '0', 'admin', '2024-04-07 20:10:32', 'admin', '2024-04-07 20:10:32');



INSERT INTO sys_config

(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)

VALUES(1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2023-12-08 14:19:52', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');

INSERT INTO sys_config

(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)

VALUES(2, '用户管理-账号初始密码', 'sys.user.initPassword', 'Yz123456!@#', 'Y', 'admin', '2023-12-08 14:19:52', 'admin', '2024-01-18 14:37:58', '初始化密码 123456');

INSERT INTO sys_config

(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)

VALUES(3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2023-12-08 14:19:52', '', NULL, '深色主题theme-dark，浅色主题theme-light');

INSERT INTO sys_config

(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)

VALUES(4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2023-12-08 14:19:52', '', NULL, '是否开启注册用户功能（true开启，false关闭）');

INSERT INTO sys_config

(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)

VALUES(5, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2023-12-08 14:19:52', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');



INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2023-12-08 14:19:52', 'gongxiang2024', '2024-02-01 15:36:28', '性别男');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '性别女');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '性别未知');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '显示菜单');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '隐藏菜单');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '正常状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '停用状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '正常状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '停用状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '默认分组');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '系统分组');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '系统默认是');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '系统默认否');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '通知');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '公告');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '正常状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '关闭状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '其他操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '新增操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '修改操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '删除操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '授权操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '导出操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '导入操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '强退操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '生成操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '清空操作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '正常状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '停用状态');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(30, 0, '默认', 'default', 'link_device_auth_mode', NULL, 'default', 'N', '0', 'admin', '2021-10-21 17:56:52', '', '2021-09-17 18:40:13', '设备用户名+设备密码');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(31, 1, 'SSL/TLS', 'SSL/TLS', 'link_device_auth_mode', NULL, 'default', 'N', '0', 'admin', '2021-10-21 17:59:10', '', '2021-09-17 18:40:13', 'SSL/TLS认证');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(32, 0, '127.0.0.1:11883', '127.0.0.1:11883', 'link_device_connector', NULL, 'default', 'N', '0', 'admin', '2021-10-21 18:11:26', 'tinker', '2021-09-17 18:40:13', '本地默认节点');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(33, 0, '启用', 'ENABLE', 'link_device_status', NULL, 'success', 'N', '0', 'admin', '2021-10-22 16:28:13', 'admin', '2021-09-17 18:40:13', '设备启用');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(34, 1, '禁用', 'DISABLE', 'link_device_status', NULL, 'danger', 'N', '0', 'admin', '2021-10-22 16:28:31', 'admin', '2021-09-17 18:40:13', '设备禁用');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(35, 1, '在线', 'ONLINE', 'link_device_connect_status', '', 'success', 'N', '0', 'admin', '2021-10-22 16:35:53', 'admin', '2021-09-17 18:40:13', '设备在线');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(36, 2, '离线', 'OFFLINE', 'link_device_connect_status', '', 'warning', 'N', '0', 'admin', '2021-10-22 16:36:23', 'admin', '2021-09-17 18:40:13', '设备离线');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(37, 0, '未连接', 'INIT', 'link_device_connect_status', '', 'info', 'N', '0', 'admin', '2021-10-22 16:37:15', 'admin', '2021-09-17 18:40:13', '设备未连接');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(38, 0, '是', '0', 'link_device_is_will', NULL, 'primary', 'N', '0', 'admin', '2021-10-22 16:40:55', 'admin', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(39, 0, '否', '1', 'link_device_is_will', NULL, 'warning', 'N', '0', 'admin', '2021-10-22 16:41:02', 'admin', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(40, 0, 'mqtt', 'MQTT', 'link_device_protocol_type', NULL, 'default', 'N', '0', 'admin', '2021-10-22 16:43:48', 'mqtts', '2021-09-17 18:40:13', 'mqtt');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(41, 1, 'coap', 'COAP', 'link_device_protocol_type', NULL, 'default', 'N', '0', 'admin', '2021-10-22 16:44:02', 'mqtts', '2021-09-17 18:40:13', 'coap');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(42, 2, 'modbus', 'MODBUS', 'link_device_protocol_type', NULL, 'default', 'N', '0', 'admin', '2021-10-22 16:44:15', 'mqtts', '2021-09-17 18:40:13', 'modbus');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(43, 3, 'http', 'HTTP', 'link_device_protocol_type', NULL, 'default', 'N', '0', 'admin', '2021-10-22 16:44:35', 'mqtts', '2021-09-17 18:40:13', 'http');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(44, 0, '普通设备', 'COMMON', 'link_device_device_type', NULL, 'default', 'N', '0', 'admin', '2021-10-22 16:57:26', 'mqtts', '2021-09-17 18:40:13', '普通设备（无子设备也无父设备）');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(45, 1, '网关设备', 'GATEWAY', 'link_device_device_type', NULL, 'default', 'N', '0', 'admin', '2021-10-22 16:57:44', 'mqtts', '2021-09-17 18:40:13', '网关设备(可挂载子设备)');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(47, 0, 'tinker', 'tinker', 'link_application_type', NULL, 'default', 'N', '0', 'tinker', '2021-12-28 13:47:34', 'admin', '2021-09-17 18:40:13', 'tinker');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(48, 1, '218.78.103.93:11883', '218.78.103.93:11883', 'link_device_connector', NULL, 'default', 'N', '0', 'tinker', '2021-12-28 13:49:09', 'tinker', '2021-09-17 18:40:13', '物联网测试环境');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(49, 0, 'Default', 'default', 'link_product_device_type', NULL, 'default', 'N', '0', 'admin', '2022-02-09 16:51:26', '', '2021-09-17 18:40:13', '默认');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(50, 0, '普通产品', 'COMMON', 'link_product_type', NULL, 'default', 'N', '0', 'admin', '2022-02-09 18:02:38', '', '2021-09-17 18:40:13', '普通产品，需直连设备。');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(51, 1, '网关产品', 'GATEWAY', 'link_product_type', NULL, 'default', 'N', '0', 'admin', '2022-02-09 18:02:55', 'admin', '2021-09-17 18:40:13', '网关产品，可挂载子设备。');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(52, 0, '非必填', '0', 'link_product_isRequired', NULL, 'default', 'N', '0', 'admin', '2022-03-25 15:51:10', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(53, 0, '必填', '1', 'link_product_isRequired', NULL, 'default', 'N', '0', 'admin', '2022-03-25 15:51:19', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(54, 0, '允许', 'allow', 'link_casbinRule_v3', NULL, 'success', 'N', '0', 'admin', '2022-06-16 18:03:46', 'admin', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(56, 0, '拒绝', 'deny', 'link_casbinRule_v3', NULL, 'warning', 'N', '0', 'admin', '2022-06-16 18:05:08', 'admin', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(58, 0, '发布', 'PUBLISH', 'link_casbinRule_v2', NULL, 'primary', 'N', '0', 'admin', '2022-06-16 18:13:15', 'admin', '2021-09-17 18:40:13', '发布动作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(59, 0, '订阅', 'SUBSCRIBE', 'link_casbinRule_v2', NULL, 'info', 'N', '0', 'admin', '2022-06-16 18:21:09', 'admin', '2021-09-17 18:40:13', '订阅动作');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(60, 0, '上线', 'ONLINE', 'link_device_action_type', NULL, 'success', 'N', '0', 'admin', '2022-06-17 17:45:24', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(61, 0, '离线', 'OFFLINE', 'link_device_action_type', NULL, 'warning', 'N', '0', 'admin', '2022-06-17 17:45:49', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(62, 0, '是', 'true', 'link_deviceInfo_shadow_enable', NULL, 'primary', 'N', '0', 'admin', '2022-06-21 11:28:26', 'admin', '2021-09-17 18:40:13', '支持设备影子');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(63, 0, '否', 'false', 'link_deviceInfo_shadow_enable', NULL, 'danger', 'N', '0', 'admin', '2022-06-21 11:28:46', 'admin', '2021-09-17 18:40:13', '不支持设备影子');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(64, 0, '启用', '0', 'business_data_status', NULL, 'success', 'N', '0', 'admin', '2022-06-21 14:26:47', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(65, 0, '停用', '1', 'business_data_status', NULL, 'danger', 'N', '0', 'admin', '2022-06-21 14:27:03', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(66, 0, 'string', 'string', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:42:46', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(67, 0, 'binary', 'binary', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:42:59', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(68, 0, 'int', 'int', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:43:09', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(69, 0, 'bool', 'bool', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:43:18', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(70, 0, 'decimal', 'decimal', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:43:28', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(71, 0, 'timestamp', 'timestamp', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:43:58', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(72, 0, 'json', 'json', 'link_product_datatype', NULL, 'default', 'N', '0', 'admin', '2022-06-24 18:44:08', '', '2021-09-17 18:40:13', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(73, 0, 'java', 'java', 'link_protocol_voice', NULL, 'default', 'N', '0', 'admin', '2022-07-04 11:17:07', '', '2021-09-17 18:40:13', 'java');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(74, 3, '协议管理', 'PROTOCOL', 'sys_job_group', NULL, 'default', 'N', '0', 'admin', '2022-07-11 15:48:55', 'admin', '2021-09-17 18:40:13', '协议管理');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(75, 4, '规则触发器', 'RULE_TRIGGER', 'sys_job_group', NULL, 'default', 'N', '0', 'admin', '2022-07-18 18:22:09', 'admin', '2021-09-17 18:40:13', '设备联动规则触发器');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(76, 5, '设备管理', 'LINK_DEVICE', 'sys_job_group', NULL, 'default', 'N', '0', 'admin', '2022-07-29 14:36:31', 'admin', '2021-09-17 18:40:13', '设备管理');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(79, 4, '测试性别', '3', 'sys_user_sex', NULL, 'success', 'N', '0', 'gongxiang2024', '2024-02-22 15:13:02', 'gongxiang2024', '2024-02-22 15:13:11', '测试性别');

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(110, 0, '审批流程', 'leave', 'sys_process_category', NULL, 'default', 'N', '0', 'admin', '2023-11-12 11:17:56', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(111, 0, '通用流程', 'common', 'sys_process_category', NULL, 'default', 'N', '0', 'admin', '2023-11-12 11:18:08', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(112, 0, '正常', '0', 'common_status', NULL, 'success', 'N', '0', 'admin', '2023-11-18 22:01:08', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(113, 1, '禁用', '1', 'common_status', NULL, 'danger', 'N', '0', 'admin', '2023-11-18 22:01:21', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(114, 0, '任务监听', '1', 'sys_listener_type', NULL, 'default', 'N', '0', 'admin', '2023-11-25 11:47:26', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(115, 2, '执行监听', '2', 'sys_listener_type', NULL, 'default', 'N', '0', 'admin', '2023-11-25 11:47:37', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(116, 0, 'JAVA类', '1', 'sys_listener_value_type', NULL, 'default', 'N', '0', 'admin', '2023-11-25 11:48:55', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(117, 0, '表达式', '2', 'sys_listener_value_type', NULL, 'default', 'N', '0', 'admin', '2023-11-25 11:49:05', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(118, 0, '代理表达式', '3', 'sys_listener_value_type', NULL, 'default', 'N', '0', 'admin', '2023-11-25 11:49:16', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(121, 1, '派出所', '1', 'org_type', NULL, NULL, 'N', '0', 'admin', '2024-03-18 16:19:51', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(122, 2, '教育局', '2', 'org_type', NULL, NULL, 'N', '0', 'admin', '2024-03-18 16:20:00', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(123, 3, '街道办事处', '3', 'org_type', NULL, NULL, 'N', '0', 'admin', '2024-03-18 16:26:52', 'admin', '2024-03-22 10:14:02', NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(124, 1, '省级单位', '4', 'org_type', NULL, NULL, 'N', '0', 'admin', '2024-03-22 10:14:25', '', NULL, NULL);

INSERT INTO sys_dict_data

(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)

VALUES(125, 5, '二级机构', '5', 'org_type', NULL, NULL, 'N', '0', 'admin', '2024-03-22 10:22:53', '', NULL, NULL);



INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(1, '用户性别', 'sys_user_sex', '0', 'admin', '2023-12-08 14:19:51', 'gongxiang2024', '2024-01-30 11:00:59', '用户性别列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(2, '菜单状态', 'sys_show_hide', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '菜单状态列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(3, '系统开关', 'sys_normal_disable', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '系统开关列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(4, '任务状态', 'sys_job_status', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '任务状态列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(5, '任务分组', 'sys_job_group', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '任务分组列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(6, '系统是否', 'sys_yes_no', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '系统是否列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(7, '通知类型', 'sys_notice_type', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '通知类型列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(8, '通知状态', 'sys_notice_status', '0', 'admin', '2023-12-08 14:19:51', '', NULL, '通知状态列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(9, '操作类型', 'sys_oper_type', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '操作类型列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(10, '系统状态', 'sys_common_status', '0', 'admin', '2023-12-08 14:19:52', '', NULL, '登录状态列表');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(11, '设备认证方式', 'link_device_auth_mode', '0', 'admin', '2021-10-21 17:52:30', '', NULL, '设备管理鉴权方式');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(12, '设备连接实例', 'link_device_connector', '0', 'admin', '2021-10-21 18:10:18', '', NULL, '设备连接实例');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(13, '设备状态', 'link_device_status', '0', 'admin', '2021-10-22 16:27:28', 'mqtts', '2021-10-25 15:48:58', '设备状态');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(14, '连接状态', 'link_device_connect_status', '0', 'admin', '2021-10-22 16:35:11', '', NULL, '设备连接状态

');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(15, '是否遗言', 'link_device_is_will', '0', 'admin', '2021-10-22 16:40:39', '', NULL, '设备是否有遗言');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(16, '产品协议类型', 'link_device_protocol_type', '0', 'admin', '2021-10-22 16:43:24', '', NULL, '产品协议类型 ：mqtt || coap || modbus || http');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(17, '设备类型', 'link_device_device_type', '0', 'admin', '2021-10-22 16:54:12', 'admin', '2021-10-22 16:56:47', '设备类型0-普通设备（无子设备也无父设备）1-网关设备(可挂载子设备)2-子设备(归属于某个网关设备)。');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(18, '集成应用类型', 'link_application_type', '0', 'tinker', '2021-12-28 13:41:22', 'tinker', '2021-12-28 13:42:28', '集成应用');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(19, '产品设备类型', 'link_product_device_type', '0', 'admin', '2022-02-09 16:50:14', 'admin', '2022-02-09 17:53:25', '产品设备类型，支持英文大小写、数字、下划线和中划线');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(20, '产品类型', 'link_product_type', '0', 'admin', '2022-02-09 17:52:26', '', NULL, '支持以下两种产品类型：

?0：普通产品，需直连设备。

?1：网关产品，可挂载子设备。

');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(21, '是否必填', 'link_product_isRequired', '0', 'admin', '2022-03-25 15:39:40', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(22, 'CAS策略类型', 'link_casbinRule_v3', '0', 'admin', '2022-06-16 18:02:31', '', NULL, 'CAS策略类型：允许||拒绝');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(23, 'CAS策略动作', 'link_casbinRule_v2', '0', 'admin', '2022-06-16 18:11:05', '', NULL, '认证动作');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(24, '设备动作类型', 'link_device_action_type', '0', 'admin', '2022-06-17 17:43:34', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(25, '设备影子状态', 'link_deviceInfo_shadow_enable', '0', 'admin', '2022-06-21 11:27:39', '', NULL, '是否支设备影子');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(26, '业务数据状态', 'business_data_status', '0', 'admin', '2022-06-21 14:25:45', '', NULL, '业务数据状态标识（0启用  1停用）');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(27, '指示数据类型', 'link_product_datatype', '0', 'admin', '2022-06-24 18:41:39', '', NULL, '产品模型指示数据类型');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(28, '协议语言', 'link_protocol_voice', '0', 'admin', '2022-07-04 11:16:20', 'admin', '2022-07-04 17:09:29', '协议管理-支持的协议脚本语言');

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(29, '测试', 'sys_test_type', '1', 'gongxiang2024', '2024-02-22 15:07:15', 'gongxiang2024', '2024-02-22 15:07:58', NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(100, '流程分类', 'sys_process_category', '0', 'admin', '2023-11-12 11:17:38', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(102, '监听类型', 'sys_listener_type', '0', 'admin', '2023-11-18 22:03:07', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(103, '监听值类型', 'sys_listener_value_type', '0', 'admin', '2023-11-18 22:03:39', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(104, '监听属性', 'sys_listener_event_type', '0', 'admin', '2023-11-18 22:04:29', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(105, '通用状态', 'common_status', '0', 'admin', '2023-11-18 22:00:02', '', NULL, NULL);

INSERT INTO sys_dict_type

(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)

VALUES(108, '组织机构类型', 'org_type', '0', 'admin', '2024-03-18 15:50:11', '', NULL, NULL);



INSERT INTO sys_user

(user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark)

VALUES(1, 103, 'admin', '应用支撑平台', '00', NULL, NULL, '1', '', '$2a$10$MFLDN5cH7QtjSLp9tUr99OYua0hadvyZEWTP77AMbqKMb/18111SW', '0', '0', '127.0.0.1', '2023-12-08 14:19:48', 'admin', '2023-12-08 14:19:48', '', NULL, '管理员');



INSERT INTO sys_role

(role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark)

VALUES(1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2023-12-08 14:19:48', '', NULL, '超级管理员');



INSERT INTO sys_user_role

(user_id, role_id, org_id)

VALUES(1, 1, NULL);



INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2, 3, '1', '系统监控', 0, 3, 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'icon-park-outline:monitor-two', 'admin', '2023-12-08 14:19:48', 'admin', '2024-04-07 13:06:55', '系统监控目录');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2005, 3, '1', '用户管理', 0, 1, 'user', NULL, '', 1, 0, 'M', '0', '0', '', 'ooui:user-avatar', 'admin', '2023-12-13 17:40:26', 'admin', '2024-04-03 17:03:39', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2006, 3, '1', '组织架构管理', 2005, 1, 'org', '', NULL, 1, 0, 'M', '0', '0', '', 'lucide:users', 'admin', '2023-12-13 17:50:55', 'admin', '2023-12-15 11:23:50', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2007, 3, '1', '成员管理', 2006, 2, 'member', 'system/user/index', NULL, 1, 0, 'C', '0', '0', 'system:user:page', 'icon-park-outline:file-staff', 'admin', '2023-12-13 17:53:54', 'message', '2024-02-22 09:01:17', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2008, 3, '1', '部门管理', 2006, 1, 'department', 'system/dept/index', NULL, 1, 0, 'C', '0', '0', 'system:dept:list', 'mingcute:department-line', 'admin', '2023-12-13 18:02:10', 'admin', '2023-12-22 11:01:09', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2009, 3, '1', '角色管理', 2005, 2, 'role', 'system/role/index', NULL, 1, 0, 'C', '0', '0', 'system:role:page', 'carbon:user-settings', 'admin', '2023-12-13 18:31:32', 'message', '2024-02-22 09:00:39', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2011, 3, '1', '系统设置', 0, 2, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'ci:settings-future', 'admin', '2023-12-13 18:40:07', 'admin', '2024-04-07 17:42:15', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2012, 3, '1', '日志管理', 2011, 3, 'log', '', NULL, 1, 0, 'M', '0', '0', '', 'carbon:blog', 'admin', '2023-12-13 18:42:00', 'admin', '2023-12-15 11:33:33', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2013, 3, '1', '登录日志', 2012, 1, 'login', 'system/logininfor/index', NULL, 1, 0, 'C', '0', '0', 'system:logininfor:list', 'iconoir:log-in', 'admin', '2023-12-13 18:43:58', 'admin', '2023-12-22 11:03:05', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2014, 3, '1', '操作日志', 2012, 2, 'operation', 'system/operlog/index', NULL, 1, 0, 'C', '0', '0', 'system:operlog:list', 'iconoir:log-out', 'admin', '2023-12-13 18:45:16', 'admin', '2023-12-22 11:03:28', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2015, 3, '1', 'PC菜单管理', 2011, 1, 'menu', 'system/menu/index', '{"type": 1}', 1, 0, 'C', '0', '0', 'system:menu:list', 'iconoir:menu', 'admin', '2023-12-13 18:46:53', 'admin', '2024-04-02 15:28:48', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2016, 3, '1', '字典管理', 2011, 2, 'dict', 'system/dict/index', NULL, 1, 0, 'C', '0', '0', 'system:dict:list', 'carbon:data-quality-definition', 'admin', '2023-12-13 18:48:34', 'admin', '2023-12-15 15:31:04', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2017, 3, '1', '在线用户', 2, 1, 'online', 'monitor/online/index', NULL, 1, 0, 'C', '0', '0', 'monitor:online:list', 'carbon:user-online', 'admin', '2023-12-14 10:11:31', 'admin', '2023-12-15 14:56:48', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2018, 3, '1', '缓存监控', 2, 2, 'cache', 'monitor/cache/index', NULL, 1, 0, 'C', '0', '0', 'monitor:cache:list', 'ic:round-cached', 'admin', '2023-12-14 10:12:28', 'admin', '2023-12-15 14:58:06', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2026, 3, '1', '用户查询', 2007, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:page', NULL, 'admin', '2023-12-22 11:11:08', 'message', '2024-03-06 11:01:24', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2027, 3, '1', '用户新增', 2007, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:add', NULL, 'admin', '2023-12-22 11:11:30', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2028, 3, '1', '用户修改', 2007, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:edit', NULL, 'admin', '2023-12-22 11:11:47', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2029, 3, '1', '用户删除', 2007, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:remove', NULL, 'admin', '2023-12-22 11:12:02', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2030, 3, '1', '用户导出', 2007, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:export', NULL, 'admin', '2023-12-22 11:12:20', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2031, 3, '1', '用户导入', 2007, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:import', NULL, 'admin', '2023-12-22 11:12:35', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2032, 3, '1', '重置密码', 2007, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:resetPwd', NULL, 'admin', '2023-12-22 11:12:51', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2033, 3, '1', '角色查询', 2009, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:query', NULL, 'admin', '2023-12-22 11:16:48', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2034, 3, '1', '角色新增', 2009, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:add', NULL, 'admin', '2023-12-22 11:17:03', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2035, 3, '1', '角色修改', 2009, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:edit', NULL, 'admin', '2023-12-22 11:17:18', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2036, 3, '1', '角色删除', 2009, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:remove', NULL, 'admin', '2023-12-22 11:17:33', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2037, 3, '1', '角色导出', 2009, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:export', NULL, 'admin', '2023-12-22 11:17:47', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2038, 3, '1', '菜单查询', 2015, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:query', NULL, 'admin', '2023-12-22 11:20:58', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2039, 3, '1', '菜单新增', 2015, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:add', NULL, 'admin', '2023-12-22 11:21:13', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2040, 3, '1', '菜单修改', 2015, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:edit', NULL, 'admin', '2023-12-22 11:21:29', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2041, 3, '1', '菜单删除', 2015, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:remove', NULL, 'admin', '2023-12-22 11:21:42', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2042, 3, '1', '部门查询', 2008, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:query', NULL, 'admin', '2023-12-22 11:24:54', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2043, 3, '1', '部门新增', 2008, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:add', NULL, 'admin', '2023-12-22 11:25:06', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2044, 3, '1', '部门修改', 2008, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:edit', NULL, 'admin', '2023-12-22 11:25:19', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2045, 3, '1', '部门删除', 2008, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:remove', NULL, 'admin', '2023-12-22 11:25:34', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2046, 3, '1', '字典查询', 2016, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dict:query', NULL, 'admin', '2023-12-22 11:26:05', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2047, 3, '1', '字典新增', 2016, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dict:add', NULL, 'admin', '2023-12-22 11:26:20', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2048, 3, '1', '字典修改', 2016, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dict:edit', NULL, 'admin', '2023-12-22 11:26:33', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2049, 3, '1', '字典删除', 2016, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dict:remove', NULL, 'admin', '2023-12-22 11:26:45', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2050, 3, '1', '字典导出', 2016, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dict:export', NULL, 'admin', '2023-12-22 11:26:58', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2051, 3, '1', '操作查询', 2014, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:operlog:query', NULL, 'admin', '2023-12-22 11:27:30', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2052, 3, '1', '操作删除', 2014, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:operlog:remove', NULL, 'admin', '2023-12-22 11:27:46', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2053, 3, '1', '日志导出', 2014, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:operlog:export', NULL, 'admin', '2023-12-22 11:27:59', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2054, 3, '1', '登录查询', 2013, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:logininfor:query', NULL, 'admin', '2023-12-22 11:28:15', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2055, 3, '1', '登录删除', 2013, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:logininfor:remove', NULL, 'admin', '2023-12-22 11:28:32', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(2056, 3, '1', '日志导出', 2013, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:logininfor:export', NULL, 'admin', '2023-12-22 11:29:00', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(3048, 3, '1', '角色组管理', 2005, 2, 'role-group', 'system/role/group/index', NULL, 1, 0, 'C', '0', '0', '', 'mingcute:group-2-line', 'gongxiang2024', '2024-02-28 14:46:26', 'gongxiang2024', '2024-02-28 14:49:46', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(3052, 3, '1', '强退', 2017, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', NULL, 'message', '2024-03-05 17:23:35', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(3054, 3, '1', '机构管理', 2006, 1, 'organ', 'system/organ/index', '', 1, 0, 'C', '0', '0', '', 'material-symbols:contact-emergency-outline', 'homer', '2024-03-06 11:03:04', 'admin', '2024-03-25 14:56:56', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(3056, 3, '1', 'API权限管理', 2011, 2, 'api-manage', 'system/api-manage/index', NULL, 1, 0, 'C', '0', '0', '', 'ic:outline-hdr-auto', 'homer', '2024-03-07 18:07:01', 'message', '2024-03-15 10:11:36', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(3072, 3, '1', '查询', 3056, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:list', NULL, 'message', '2024-03-15 10:11:56', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(3073, 3, '1', '新增', 3056, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:save', NULL, 'message', '2024-03-15 10:12:13', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6013, 3, '1', '角色查询API权限', 3056, 5, '', NULL, '', 1, 0, 'F', '0', '0', 'system:api:listByRoleId', NULL, 'message', '2024-03-25 14:07:41', 'message', '2024-03-25 14:34:07', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6014, 3, '1', '新增', 3048, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:save', NULL, 'message', '2024-03-25 14:11:36', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6015, 3, '1', '修改', 3048, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:update', NULL, 'message', '2024-03-25 14:12:39', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6016, 3, '1', '删除', 3048, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:delete', NULL, 'message', '2024-03-25 14:13:46', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6017, 3, '1', '查询', 3048, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:page', NULL, 'message', '2024-03-25 14:14:15', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6018, 3, '1', '保存角色', 3048, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:saveRole', NULL, 'message', '2024-03-25 14:14:38', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6019, 3, '1', '查询角色', 3048, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:listRoleByRoleGroupId', NULL, 'message', '2024-03-25 14:15:00', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6020, 3, '1', '新增', 3054, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:org:save', NULL, 'message', '2024-03-25 14:20:04', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6021, 3, '1', '修改', 3054, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:org:update', NULL, 'message', '2024-03-25 14:20:38', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6022, 3, '1', '删除', 3054, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:org:delete', NULL, 'message', '2024-03-25 14:21:00', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6023, 3, '1', '查询', 3054, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:org:page', NULL, 'message', '2024-03-25 14:21:22', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6024, 3, '1', '查询单个信息', 3054, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:org:getById', NULL, 'message', '2024-03-25 14:21:46', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6025, 3, '1', '授权角色', 3056, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:saveRoleAndApi', NULL, 'message', '2024-03-25 14:32:26', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6026, 3, '1', '批量授权角色', 3056, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:saveRoleAndApiBatch', NULL, 'message', '2024-03-25 14:32:53', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6027, 3, '1', '移除授权', 3056, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:removeRoleAndApi', NULL, 'message', '2024-03-25 14:33:18', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6028, 3, '1', '修改', 3056, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:update', NULL, 'message', '2024-03-25 14:34:00', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6029, 3, '1', '删除', 3056, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:api:delete', NULL, 'message', '2024-03-25 14:34:34', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6030, 3, '1', '查询单个角色组', 3048, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:getById', NULL, 'message', '2024-03-25 14:48:59', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6034, 3, '1', '角色组查询', 3054, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:roleGroup:list', NULL, 'message', '2024-03-25 17:08:42', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6035, 3, '1', '查询角色', 2007, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:listRoleByOrgIds', NULL, 'message', '2024-03-25 17:33:47', '', NULL, '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6036, 3, '1', '应用管理', 2011, 1, 'application', 'system/app/index', '', 1, 0, 'C', '0', '0', 'system:app:index', 'mdi:application', 'admin', '2024-03-28 15:50:06', 'admin', '2024-03-28 18:00:56', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6040, 3, '1', '新增', 6036, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:app:save', NULL, 'message', '2024-04-01 19:32:14', 'message', '2024-04-01 19:32:14', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6041, 3, '1', '修改', 6036, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:app:update', NULL, 'message', '2024-04-01 19:32:34', 'message', '2024-04-01 19:32:34', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6042, 3, '1', '删除', 6036, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:app:delete', NULL, 'message', '2024-04-01 19:32:52', 'message', '2024-04-01 19:32:52', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6043, 3, '1', '查询', 6036, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:app:page', NULL, 'message', '2024-04-01 19:33:23', 'message', '2024-04-01 19:33:23', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6044, 3, '1', '查询单个应用', 6036, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:app:getById', NULL, 'message', '2024-04-01 19:33:45', 'message', '2024-04-01 19:33:45', '');

INSERT INTO sys_menu

(menu_id, app_id, `type`, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)

VALUES(6046, 3, '1', 'H5菜单管理', 2011, 1, 'h5-menu', 'system/menu/index', '{"type": 2}', 1, 0, 'C', '0', '0', '', 'iconoir:menu', 'admin', '2024-04-02 15:29:51', 'admin', '2024-04-02 15:31:25', '');
