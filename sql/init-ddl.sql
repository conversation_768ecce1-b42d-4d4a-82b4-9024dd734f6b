-- support_platform_test.sys_api definition

CREATE TABLE `sys_api` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                           `type` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型（1-目录，2-API权限）',
                           `parent_id` bigint(20) DEFAULT NULL COMMENT '父ID',
                           `parent_id_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父ID路径',
                           `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API名称',
                           `perms` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API权限字符或路径',
                           `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                           `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
                           `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                           `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API权限配置表';


-- support_platform_test.sys_app definition

CREATE TABLE `sys_app` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                           `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用名称',
                           `code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用编码',
                           `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标地址',
                           `type` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型（1-PC端，2-APP端）',
                           `url` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '跳转地址',
                           `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
                           `frame_flag` int(1) DEFAULT '1' COMMENT '外链标识（0否 1是）',
                           `public_key` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公钥',
                           `private_key` varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '私钥',
                           `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                           `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
                           `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                           `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用配置表';


-- support_platform_test.sys_app_key_pair definition

CREATE TABLE `sys_app_key_pair` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `org_id` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织ID',
                                    `org_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织名称',
                                    `app_id` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用编号',
                                    `app_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用名称',
                                    `app_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'openapi验签时分配的appkey',
                                    `app_secret` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'openapi验签时分配的appsecret',
                                    `status` char(1) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态（0正常 1停用）',
                                    `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '删除标志（0代表存在 2代表删除）',
                                    `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='appkey密钥管理表';


-- support_platform_test.sys_config definition

CREATE TABLE `sys_config` (
                              `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
                              `config_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '参数名称',
                              `config_key` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '参数键名',
                              `config_value` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '参数键值',
                              `config_type` char(1) COLLATE utf8mb4_unicode_ci DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
                              `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                              PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参数配置表';


-- support_platform_test.sys_country_address definition

CREATE TABLE `sys_country_address` (
                                       `id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `pcode` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `pcode_path` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_level` int(11) DEFAULT NULL,
                                       `region_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_city_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_offcial_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_full_name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_name` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_shortname` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_offcialshortname` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_name1` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_name2` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_citycode` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_zipcode` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_pinyin` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_pinyin_firstchar` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_lng` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_lat` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_sort` int(11) DEFAULT NULL,
                                       `region_remark` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_attr1` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_attr2` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `region_attr3` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `data_status` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `version` int(11) DEFAULT NULL,
                                       `create_time` timestamp(6) NULL DEFAULT NULL,
                                       `creator_accountcode` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `creator_name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `update_time` timestamp(6) NULL DEFAULT NULL,
                                       `updater_accountcode` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                       `updater_name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- support_platform_test.sys_deploy_form definition

CREATE TABLE `sys_deploy_form` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `form_id` bigint(20) DEFAULT NULL COMMENT '表单主键',
                                   `deploy_id` varchar(50) DEFAULT NULL COMMENT '流程实例主键',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6215 DEFAULT CHARSET=utf8mb4 COMMENT='流程实例关联表单';


-- support_platform_test.sys_dept definition

CREATE TABLE `sys_dept` (
                            `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
                            `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门id',
                            `ancestors` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '祖级列表',
                            `dept_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '部门名称',
                            `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
                            `leader` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '负责人',
                            `phone` varchar(11) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
                            `email` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
                            `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
                            `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=221 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';


-- support_platform_test.sys_dict_data definition

CREATE TABLE `sys_dict_data` (
                                 `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
                                 `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
                                 `dict_label` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典标签',
                                 `dict_value` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典键值',
                                 `dict_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典类型',
                                 `css_class` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
                                 `list_class` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表格回显样式',
                                 `is_default` char(1) COLLATE utf8mb4_unicode_ci DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
                                 `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                 `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典数据表';


-- support_platform_test.sys_dict_type definition

CREATE TABLE `sys_dict_type` (
                                 `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
                                 `dict_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典名称',
                                 `dict_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典类型',
                                 `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                 `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`dict_id`),
                                 UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典类型表';


-- support_platform_test.sys_expression definition

CREATE TABLE `sys_expression` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表单主键',
                                  `name` varchar(50) DEFAULT NULL COMMENT '表达式名称',
                                  `expression` varchar(255) DEFAULT NULL COMMENT '表达式内容',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人员',
                                  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人员',
                                  `status` tinyint(2) DEFAULT '0' COMMENT '状态',
                                  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='流程表达式';


-- support_platform_test.sys_form definition

CREATE TABLE `sys_form` (
                            `form_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表单主键',
                            `form_name` varchar(50) DEFAULT NULL COMMENT '表单名称',
                            `form_content` longtext COMMENT '表单内容',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `create_by` bigint(20) DEFAULT NULL COMMENT '创建人员',
                            `update_by` bigint(20) DEFAULT NULL COMMENT '更新人员',
                            `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`form_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3181 DEFAULT CHARSET=utf8mb4 COMMENT='流程表单';


-- support_platform_test.sys_job definition

CREATE TABLE `sys_job` (
                           `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
                           `job_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务名称',
                           `job_group` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
                           `invoke_target` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '调用目标字符串',
                           `cron_expression` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'cron执行表达式',
                           `misfire_policy` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
                           `concurrent` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
                           `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1暂停）',
                           `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                           `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注信息',
                           PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务调度表';


-- support_platform_test.sys_job_log definition

CREATE TABLE `sys_job_log` (
                               `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
                               `job_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
                               `job_group` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务组名',
                               `invoke_target` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '调用目标字符串',
                               `job_message` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志信息',
                               `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
                               `exception_info` varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '异常信息',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务调度日志表';


-- support_platform_test.sys_listener definition

CREATE TABLE `sys_listener` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表单主键',
                                `name` varchar(128) DEFAULT NULL COMMENT '名称',
                                `type` char(2) DEFAULT NULL COMMENT '监听类型',
                                `event_type` varchar(32) DEFAULT NULL COMMENT '事件类型',
                                `value_type` char(2) DEFAULT NULL COMMENT '值类型',
                                `value` varchar(255) DEFAULT NULL COMMENT '执行内容',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `create_by` bigint(20) DEFAULT NULL COMMENT '创建人员',
                                `update_by` bigint(20) DEFAULT NULL COMMENT '更新人员',
                                `status` tinyint(2) DEFAULT '0' COMMENT '状态',
                                `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='流程监听';


-- support_platform_test.sys_logininfor definition

CREATE TABLE `sys_logininfor` (
                                  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
                                  `user_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户账号',
                                  `ipaddr` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '登录IP地址',
                                  `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
                                  `msg` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '提示信息',
                                  `access_time` datetime DEFAULT NULL COMMENT '访问时间',
                                  PRIMARY KEY (`info_id`),
                                  KEY `idx_sys_logininfor_s` (`status`),
                                  KEY `idx_sys_logininfor_lt` (`access_time`)
) ENGINE=InnoDB AUTO_INCREMENT=5165 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统访问记录';


-- support_platform_test.sys_menu definition

CREATE TABLE `sys_menu` (
                            `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
                            `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID',
                            `type` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型（1-PC端，2-APP端）',
                            `menu_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
                            `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
                            `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
                            `path` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
                            `component` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
                            `query` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由参数',
                            `is_frame` int(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
                            `is_cache` int(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
                            `menu_type` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
                            `visible` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
                            `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
                            `perms` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限标识',
                            `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单图标',
                            `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
                            PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6056 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';


-- support_platform_test.sys_notice definition

CREATE TABLE `sys_notice` (
                              `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
                              `notice_title` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
                              `notice_type` char(1) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
                              `notice_content` longblob COMMENT '公告内容',
                              `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
                              `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                              PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知公告表';


-- support_platform_test.sys_oper_log definition

CREATE TABLE `sys_oper_log` (
                                `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
                                `title` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '模块标题',
                                `business_type` int(2) DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
                                `method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '方法名称',
                                `request_method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求方式',
                                `operator_type` int(1) DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
                                `oper_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '操作人员',
                                `dept_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '部门名称',
                                `oper_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求URL',
                                `oper_ip` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主机地址',
                                `oper_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '操作地点',
                                `oper_param` varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求参数',
                                `json_result` varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '返回参数',
                                `status` int(1) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
                                `error_msg` varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '错误消息',
                                `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
                                `cost_time` bigint(20) DEFAULT '0' COMMENT '消耗时间',
                                PRIMARY KEY (`oper_id`),
                                KEY `idx_sys_oper_log_bt` (`business_type`),
                                KEY `idx_sys_oper_log_s` (`status`),
                                KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=16408 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志记录';


-- support_platform_test.sys_org definition

CREATE TABLE `sys_org` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                           `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构（主体）名称',
                           `org_type` int(2) DEFAULT NULL COMMENT '组织机构类型',
                           `parent_id` bigint(20) DEFAULT NULL COMMENT '父级机构Id',
                           `contacts_person` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人',
                           `contacts_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
                           `contacts_way` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位联系方式(座机)',
                           `dz_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所在地址名称',
                           `dz_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所在地址code',
                           `dz_region_level` int(2) DEFAULT NULL COMMENT '所在地址区划级别',
                           `dz_detail_address` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
                           `role_group_id` bigint(20) DEFAULT NULL COMMENT '角色组Id',
                           `gu_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管辖范围编码',
                           `gu_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管辖范围全称',
                           `gu_region_level` int(2) DEFAULT NULL COMMENT '管辖范围区划级别',
                           `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
                           `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                           `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                           `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织机构表';


-- support_platform_test.sys_post definition

CREATE TABLE `sys_post` (
                            `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
                            `post_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位编码',
                            `post_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位名称',
                            `post_sort` int(4) NOT NULL COMMENT '显示顺序',
                            `status` char(1) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态（0正常 1停用）',
                            `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位信息表';


-- support_platform_test.sys_receive_log definition

CREATE TABLE `sys_receive_log` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `receive_params` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '接收参数',
                                   `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                   `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                   `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                   `create_time` datetime DEFAULT NULL,
                                   `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                   `update_time` datetime DEFAULT NULL,
                                   `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1242270 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接收日志表';


-- support_platform_test.sys_role definition

CREATE TABLE `sys_role` (
                            `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
                            `role_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
                            `role_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色权限字符串',
                            `role_sort` int(4) NOT NULL COMMENT '显示顺序',
                            `data_scope` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
                            `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
                            `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
                            `status` char(1) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
                            `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3014 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';


-- support_platform_test.sys_role_api definition

CREATE TABLE `sys_role_api` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
                                `api_id` bigint(20) DEFAULT NULL COMMENT 'API权限ID',
                                `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1777233286664212484 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和API权限关系表';


-- support_platform_test.sys_role_app definition

CREATE TABLE `sys_role_app` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
                                `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID',
                                `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色与应用关系表';


-- support_platform_test.sys_role_dept definition

CREATE TABLE `sys_role_dept` (
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
                                 PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和部门关联表';


-- support_platform_test.sys_role_group definition

CREATE TABLE `sys_role_group` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色组名称',
                                  `code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色组编码',
                                  `org_type` int(2) DEFAULT NULL COMMENT '组织机构类型',
                                  `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                  `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色组表';


-- support_platform_test.sys_role_menu definition

CREATE TABLE `sys_role_menu` (
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID',
                                 `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
                                 PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';


-- support_platform_test.sys_role_role_group definition

CREATE TABLE `sys_role_role_group` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                       `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
                                       `role_group_id` bigint(20) DEFAULT NULL COMMENT '角色组ID',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色与角色组关系表';


-- support_platform_test.sys_task_form definition

CREATE TABLE `sys_task_form` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `form_id` bigint(20) DEFAULT NULL COMMENT '表单主键',
                                 `task_id` varchar(50) DEFAULT NULL COMMENT '所属任务',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程任务关联表单';


-- support_platform_test.sys_user definition

CREATE TABLE `sys_user` (
                            `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                            `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
                            `user_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
                            `nick_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
                            `user_type` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT '00' COMMENT '用户类型（00系统用户）',
                            `email` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户邮箱',
                            `phonenumber` varchar(11) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '手机号码',
                            `sex` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
                            `avatar` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像地址',
                            `password` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '密码',
                            `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
                            `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `login_ip` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
                            `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
                            `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1775324490983968770 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';


-- support_platform_test.sys_user_org definition

CREATE TABLE `sys_user_org` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                                `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
                                `org_id` bigint(20) DEFAULT NULL COMMENT '机构ID',
                                `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户与组织机构表';


-- support_platform_test.sys_user_post definition

CREATE TABLE `sys_user_post` (
                                 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                 `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
                                 PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户与岗位关联表';


-- support_platform_test.sys_user_role definition

CREATE TABLE `sys_user_role` (
                                 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 `org_id` bigint(20) DEFAULT NULL COMMENT '组织机构ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';


-- support_platform_test.sys_user_subject definition

CREATE TABLE `sys_user_subject` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
                                    `subject_info` varchar(4000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主题信息',
                                    `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                    `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=464 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户主题信息';

alter TABLE sys_org add column gu_pcode_path varchar(256) null COMMENT '管辖地址父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY' after gu_code;
alter TABLE sys_org add column dz_pcode_path varchar(256) null COMMENT '所在地址父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY' after dz_code;

CREATE TABLE `sys_org_extend` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `extend_str1` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str2` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str3` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str4` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str5` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str6` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str7` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str8` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str9` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str10` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                  `extend_str11` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型11',
                                  `extend_str12` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型12',
                                  `extend_str13` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型13',
                                  `extend_str14` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型14',
                                  `extend_str15` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型15',
                                  `extend_int1` bigint(10) DEFAULT NULL COMMENT '扩展字段整型类型1',
                                  `extend_int2` bigint(10) DEFAULT NULL COMMENT '扩展字段整型类型2',
                                  `extend_int3` bigint(10) DEFAULT NULL COMMENT '扩展字段整型类型3',
                                  `extend_double1` double(10,2) DEFAULT NULL COMMENT '扩展字段小数类型1',
`extend_double2` double(10,2) DEFAULT NULL COMMENT '扩展字段小数类型2',
`extend_double3` double(10,2) DEFAULT NULL COMMENT '扩展字段小数类型3',
`extend_datetime1` datetime DEFAULT NULL COMMENT '扩展字段时间类型1',
`extend_datetime2` datetime DEFAULT NULL COMMENT '扩展字段时间类型2',
`create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织机构信息扩展表';

CREATE TABLE `sys_user_extend` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `extend_str1` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str2` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str3` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str4` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str5` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str6` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str7` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str8` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str9` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_str10` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段字符类型1',
                                   `extend_int1` bigint(10) DEFAULT NULL COMMENT '扩展字段整型类型1',
                                   `extend_int2` bigint(10) DEFAULT NULL COMMENT '扩展字段整型类型2',
                                   `extend_int3` bigint(10) DEFAULT NULL COMMENT '扩展字段整型类型3',
                                   `extend_double1` double(10,2) DEFAULT NULL COMMENT '扩展字段小数类型1',
`extend_double2` double(10,2) DEFAULT NULL COMMENT '扩展字段小数类型2',
`extend_double3` double(10,2) DEFAULT NULL COMMENT '扩展字段小数类型3',
`extend_datetime1` datetime DEFAULT NULL COMMENT '扩展字段时间类型1',
`extend_datetime2` datetime DEFAULT NULL COMMENT '扩展字段时间类型2',
`create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息扩展表';


alter table sys_api add column app_id bigint(20) comment '应用ID'  after id;

ALTER TABLE `sys_user`
    ADD COLUMN `certificate_type` char(2) NULL COMMENT '证件类型 1:身份证  2:护照' AFTER `remark`;
ALTER TABLE `sys_user`
    ADD COLUMN `certificate_no` varchar(255) NULL COMMENT '证件号码' AFTER `certificate_type`;
ALTER TABLE `sys_user`
    ADD COLUMN `ext_properties` varchar(512) NULL COMMENT '扩展属性，以json字符串的形式存储' AFTER `certificate_no`;

CREATE TABLE `sys_data_scope` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                                  `data_id` bigint(20) DEFAULT NULL COMMENT '数据ID',
                                  `data_type` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据类型（1-角色、2-角色组）',
                                  `org_id` bigint(20) DEFAULT NULL COMMENT '机构ID',
                                  `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据权限关联表';

ALTER TABLE `sys_org`
    ADD COLUMN `ext_properties` varchar(512) NULL COMMENT '扩展属性，以json字符串的形式存储' AFTER `remark`;

-- V1.1.2版本新增
ALTER TABLE sys_config MODIFY COLUMN config_id bigint(20);

-- V1.1.3版本新增
ALTER TABLE sys_oper_log ADD app_code varchar(20) NULL COMMENT '应用编码';
ALTER TABLE sys_oper_log ADD `oper_desc` varchar(100) NULL COMMENT '描述（每个功能的简要描述）';
ALTER TABLE sys_org  ADD org_code varchar(200) NULL COMMENT '机构编码（上下级编码有关联）';
ALTER TABLE sys_user_org ADD org_code varchar(200) NULL COMMENT '组织机构编码';
ALTER TABLE sys_data_scope ADD org_code varchar(200) NULL COMMENT '组织机构编码';

-- V1.1.4版本新增
ALTER TABLE sys_org ADD parent_code_third varchar(200) NULL COMMENT '第三方上级机构编码';
ALTER TABLE sys_org ADD code_path_third varchar(100) NULL COMMENT '第三方机构层级路径';
ALTER TABLE sys_org ADD org_level INT(2) NULL COMMENT '机构层级';
ALTER TABLE sys_org ADD org_code_third varchar(50) NULL COMMENT '第三方机构编码';

-- V1.2.0版本新增
ALTER TABLE sys_menu MODIFY COLUMN is_frame char(1) NULL;
ALTER TABLE sys_menu MODIFY COLUMN is_cache char(1) NULL;
-- V1.2.1版本新增-20241225
ALTER TABLE sys_app MODIFY COLUMN url varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '跳转地址';

-- V1.2.3版本新增-20250305
ALTER TABLE sys_user ADD update_pwd_flag CHAR(1) DEFAULT 0 NULL COMMENT '修改密码标识（0-否，1-是）';
-- V1.2.3版本新增-20250327新增
CREATE TABLE `sys_syncdata_log` (
                                    `id` bigint(20) NOT NULL COMMENT '主键ID',
                                    `data_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据ID',
                                    `sync_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '同步类型（user，dept）',
                                    `sync_time` datetime DEFAULT NULL COMMENT '同步时间',
                                    `sync_status` int(1) DEFAULT NULL COMMENT '同步状态（0-待同步，1-已下发，2-同步成功，8-下发失败）',
                                    `batch_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次号',
                                    `status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态',
                                    `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                    `del_flag` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '删除标识',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='同步日志记录表';

-- V1.3.0版本新增-20250702
ALTER TABLE sys_api ADD request_method varchar(10) NULL COMMENT '请求方式';
-- V1.3.0版本新增-20250703
alter table sys_dept add org_id bigint null comment '机构id' after status;
alter table sys_app add biz_sense varchar(40) null comment '业务归属字段' after status;
alter table sys_user add last_login_org_id bigint null comment '最后登录的组织机构id';
alter table sys_user add last_login_role varchar(40) null comment '最后登录的角色key';
-- V1.3.0版本新增-20250715
alter table sys_user_role add dept_id bigint null comment '部门id';
-- V1.3.0版本新增-20250718
INSERT INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES (6, '用户信息-机构角色切换开关', 'sys.user.switchOrgRole', 'true', 'Y', 'admin', '2023-12-08 14:19:52', '', null, '是否开启机构角色切换功能（true开启，false关闭）');
-- V1.3.0版本新增-20250801
create table sys_enc_rule
(
    id          bigint auto_increment comment '表主键',
    column_name varchar(100) null comment '字段名称',
    enc_type    varchar(40)  null comment '加密类型',
    constraint sys_enc_role_pk
        primary key (id)
) comment '加密规则表';
INSERT INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES (7, '脱敏加密-是否开启入库加密配置', 'sys.enc.rule.switch', 'true', 'Y', 'admin', '2023-12-08 14:19:52', '', null, '是否开启入库加密功能（true开启，false关闭）');
