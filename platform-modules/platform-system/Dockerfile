FROM openjdk:8-jdk-alpine
VOLUME /tmp

RUN wget -O /etc/apk/keys/alias-alpine-virtual.rsa.pub https://artifact.srdcloud.cn/artifactory/api/security/keypair/public/repositories/public-alpine-virtual

RUN sed -i 's/http:\/\/dl-cdn.alpinelinux.org\/alpine/https:\/\/artifact.srdcloud.cn\/artifactory\/public-alpine-virtual/g' /etc/apk/repositories && \
  apk --no-cache add tzdata ttf-dejavu && \
  ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
  echo "Asia/Shanghai" > /etc/timezone

COPY ./target/platform-system.jar app.war

ENV ACTIVE_PROFILE=""
ENV JAVA_OPTS=""
ENV CUST_OPTS=""
ENV NACOS_URL="134.178.223.187:8848"
ENV NACOS_PASSWORD=""

EXPOSE 9211

#ENTRYPOINT java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /app.war ${CUST_OPTS} --spring.profiles.active=${ACTIVE_PROFILE} 
ENTRYPOINT java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /app.war ${CUST_OPTS} 

