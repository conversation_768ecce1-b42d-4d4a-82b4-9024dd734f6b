package com.platform.system.login.config.third;

import com.platform.system.login.filter.ThirdLoginFilter;
import com.platform.system.login.provider.ThirdAuthenticationProvider;
import com.platform.system.login.service.SysPasswordService;
import com.platform.system.login.service.impl.UserDetailServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * @Description: 第三方认证配置
 * @author: tr
 * @date: 2024年08月30日 16:21
 */
@Component
public class ThirdAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    @Autowired
    private AuthenticationSuccessHandler authenticationSuccessHandler;
    @Autowired
    private AuthenticationFailureHandler authenticationFailureHandler;

    @Autowired
    private UserDetailServiceImpl userDetailServiceImpl;
    @Autowired
    private SysPasswordService sysPasswordService;

    @Override
    public void configure(HttpSecurity builder) {
        AuthenticationManager localAuthManager = builder.getSharedObject(AuthenticationManager.class);

        ThirdLoginFilter thirdLoginFilter = new ThirdLoginFilter(localAuthManager);
        thirdLoginFilter.setAuthenticationFailureHandler(authenticationFailureHandler);
        thirdLoginFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
        //设置自定义UserPwdAuthenticationProvider的认证器userDetailsService
        ThirdAuthenticationProvider thirdAuthenticationProvider = new ThirdAuthenticationProvider();
        thirdAuthenticationProvider.setUserDetailsService(userDetailServiceImpl, sysPasswordService);
        //在UsernamePasswordAuthenticationFilter过滤前执行
        builder.authenticationProvider(thirdAuthenticationProvider).addFilterAfter(thirdLoginFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
