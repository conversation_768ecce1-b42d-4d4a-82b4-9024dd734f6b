package com.platform.system.utils;

import java.security.SecureRandom;

public class RandomPasswordUtils {
    // 定义密码中可能包含的字符集
    private static final String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;':,.<>?";

    // 将上述字符集合并成一个总的字符集
    private static final String PASSWORD_CHARACTERS = UPPER + LOWER + DIGITS + SPECIAL_CHARS;

    public static String getRandomPassword() {
        // 设置密码长度至少为10位
        int length = 10;
        // 使用SecureRandom生成随机数
        SecureRandom random = new SecureRandom();
        // 调用生成随机密码的方法
        String password = generatePassword(length, random);
        return password;
    }

    /**
     * 生成指定长度的随机密码
     * @param length 密码长度
     * @param random 随机数生成器
     * @return 返回生成的随机密码
     */
    private static String generatePassword(int length, SecureRandom random) {
        if (length < 1) {
            throw new IllegalArgumentException("length must be greater than 0");
        }
        StringBuilder sb = new StringBuilder(length);
        int randomNumber = random.nextInt(900000) + 100000;
        for (int i = 0; i < 5; i++) {
            if (i == 0 || i == 1) {
                int rndCharAt = random.nextInt(UPPER.length());
                char rndChar = UPPER.charAt(rndCharAt);
                sb.append(rndChar);
            } else if (i == 2) {
                sb.append(randomNumber);
            } else if (i == 3) {
                int rndCharAt = random.nextInt(LOWER.length());
                char rndChar = LOWER.charAt(rndCharAt);
                sb.append(rndChar);
            } else {
                int rndCharAt = random.nextInt(SPECIAL_CHARS.length());
                char rndChar = SPECIAL_CHARS.charAt(rndCharAt);
                sb.append(rndChar);
            }
        }
        return sb.toString();
    }
}
