package com.platform.system.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.common.middleware.file.FileClient;
import com.common.middleware.file.bean.FileStreamInfo;
import com.common.middleware.file.bean.ObjectInfo;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.domain.response.SysAppResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

/**
 * @Description: 文件服务前端控制器
 * @author: tr
 * @date: 2024年03月28日 14:47
 */
@Api(tags = "文件服务前端控制器")
@RestController
@RequestMapping("/file")
public class FileController {

    @Autowired
    private FileClient fileClient;

    @ApiOperation(value = "图片上传",  notes = "应用管理")
    @PostMapping("uploadIcon")
    public ResponseResult<SysAppResp> uploadIcon(@RequestParam("file") @ApiParam(name = "file", value = "上传文件") MultipartFile file) throws Exception{
        //获取文件名称
        String fileName = file.getOriginalFilename();
        //校验图片类型
        if (!fileName.matches(BusinessConstants.IMAGE_FORMAT)) {
            return ResponseResult.fail("上传图片的类型不正确!");
        }

        long size = file.getSize();
        if (size > BusinessConstants.INT_1M) {
            return ResponseResult.fail("上传图片的大小不能超过1M!");
        }
        if (0 == file.getSize()) {
            return ResponseResult.fail("上传图片错误!");
        }
        //封装文件信息
        FileStreamInfo fileStreamInfo = new FileStreamInfo();
        fileStreamInfo.setOriginalFilename(fileName);
        fileStreamInfo.setFileInputStreams(file.getInputStream());
        fileStreamInfo.setContentType(file.getContentType());
        ObjectInfo result = fileClient.uploadFileStream(fileStreamInfo);
        if (ObjectUtil.isNotNull(result)) {
            SysAppResp sysAppResp = new SysAppResp();
            sysAppResp.setIcon(result.getRelativePath());
            sysAppResp.setIconName(result.getObjectName());
            return ResponseResult.ok(sysAppResp);
        }
        return ResponseResult.fail("上传图片失败!");
    }

    @ApiOperation(value = "下载文件流", notes = "文件服务")
    @RequestMapping(value = "/download/{bucketName}/**", method = {RequestMethod.GET})
    public void download(@PathVariable(value="bucketName") String bucketName, HttpServletRequest request, HttpServletResponse httpResponse) throws IOException {
        final String path =
                request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE).toString();
        final String bestMatchingPattern =
                request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE).toString();
        //相对路径
        String relativePath = new AntPathMatcher().extractPathWithinPattern(bestMatchingPattern, path);
        String fileName = relativePath;
        if (relativePath.contains(BusinessConstants.SPRIT)){
            fileName = relativePath.substring(relativePath.lastIndexOf(BusinessConstants.SPRIT) + 1);
        }

        ObjectInfo objectInfo = new ObjectInfo();
        objectInfo.setDirectory(bucketName);
        objectInfo.setRelativePath(relativePath);
        InputStream result = fileClient.getObject(objectInfo);
        byte[] buf = new byte[1024];
        int length;
        httpResponse.reset();
        httpResponse.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharsetUtil.GBK));
        httpResponse.setContentType("application/octet-stream");
        httpResponse.setCharacterEncoding(CharsetUtil.GBK);
        OutputStream outputStream = httpResponse.getOutputStream();
        while ((length = result.read(buf)) > 0) {
            outputStream.write(buf, 0, length);
        }
        outputStream.close();
    }

    @ApiOperation(value = "获取文件的临时链接地址（有有效时长的）", notes = "文件服务")
    @RequestMapping(value = "/getFileUrl/{bucketName}/**", method = {RequestMethod.GET})
    public ResponseResult<String> getFileUrl(@PathVariable(value="bucketName") String bucketName
            , HttpServletRequest request){
        final String path =
                request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE).toString();
        final String bestMatchingPattern =
                request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE).toString();
        //相对路径
        String relativePath = new AntPathMatcher().extractPathWithinPattern(bestMatchingPattern, path);

        ObjectInfo objectInfo = new ObjectInfo();
        objectInfo.setDirectory(bucketName);
        objectInfo.setRelativePath(relativePath);
        String objectUrl = fileClient.getObjectURL(objectInfo);
        return ResponseResult.ok(objectUrl);
    }
}
