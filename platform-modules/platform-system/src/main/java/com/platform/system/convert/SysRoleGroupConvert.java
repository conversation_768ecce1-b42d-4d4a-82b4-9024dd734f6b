package com.platform.system.convert;

import com.platform.system.domain.SysRoleGroup;
import com.platform.system.domain.request.SysRoleGroupReq;
import com.platform.system.domain.response.SysRoleGroupResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 角色组信息转换
 * @author: tr
 * @date: 2024年02月29日 17:23
 */
@Mapper
public interface SysRoleGroupConvert {

    SysRoleGroupConvert INSTANCE = Mappers.getMapper(SysRoleGroupConvert.class);

    SysRoleGroup reqToDO(SysRoleGroupReq sysRoleGroupReq);

    SysRoleGroupResp doToResp(SysRoleGroup sysRoleGroup);

    List<SysRoleGroupResp> doListToRespList(List<SysRoleGroup> sysRoleGroupList);

}
