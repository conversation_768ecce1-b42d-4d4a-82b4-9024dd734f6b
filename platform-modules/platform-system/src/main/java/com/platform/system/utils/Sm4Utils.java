package com.platform.system.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;

/**
 * @Description:
 * @author: tr
 * @date: 2024年04月09日 8:53
 */
public class Sm4Utils {

    /**
     * @Description: 加密为16进制，也可以加密成base64/字节数组
     * @author: tr
     * @Date: 2024/4/9 9:44
     * @param: [plaintext, key]
     * @returnValue: java.lang.String
     */
    public static String encryptSm4(String plaintext,String key) {
        SM4 sm4 = new SM4(Mode.ECB, Padding.PKCS5Padding, key.getBytes());
        return sm4.encryptBase64(plaintext);
    }

    /**
     * @Description: 解密
     * @author: tr
     * @Date: 2024/4/9 9:44
     * @param: [ciphertext, key]
     * @returnValue: java.lang.String
     */
    public static String decryptSm4(String ciphertext,String key) {
        SM4 sm4 = new SM4(Mode.ECB, Padding.PKCS5Padding, key.getBytes());
        return sm4.decryptStr(ciphertext, CharsetUtil.CHARSET_UTF_8);
    }

}
