package com.platform.system.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.model.SysApiDTO;
import com.platform.system.service.SysApiService;
import com.platform.system.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.SysUser;
import com.platform.system.service.SysPermissionService;
import com.platform.system.service.SysRoleService;

/**
 * 用户权限处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPermissionServiceImpl implements SysPermissionService
{
    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysApiService sysApiService;

    @Autowired
    private SysConfigService configService;

    /**
     * 获取角色数据权限
     * 
     * @param user 用户对象
     * @return 角色权限信息
     */
    @Override
    public Set<String> getRolePermission(SysUser user)
    {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (SecurityUtils.isAdmin(user.getUserType()))
        {
            roles.add("admin");
        }
        else
        {
            //是否切换角色机构等信息
            String isSwitchOrgRole = configService.selectConfigByKey("sys.user.switchOrgRole");
            if ("true".equals(isSwitchOrgRole)){
                roles.add(user.getLastLoginRole());
            }else {
                roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
            }
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     * 
     * @param user 用户对象
     * @return 菜单权限信息
     */
    @Override
    public List<SysApiDTO> getMenuPermission(SysUser user)
    {
        List<SysRole> roles = user.getRoles();
        if (!CollectionUtils.isEmpty(roles))
        {
            //是否切换角色机构等信息
            String isSwitchOrgRole = configService.selectConfigByKey("sys.user.switchOrgRole");
            if ("true".equals(isSwitchOrgRole) && !SecurityUtils.isAdmin(user.getUserType())){
                List<Long> roleIdList = roles.stream()
                        .filter(sysRole -> sysRole.getRoleKey().equals(user.getLastLoginRole()))
                        .map(SysRole::getRoleId).collect(Collectors.toList());
                return sysApiService.selectApiPermsByRoleId(roleIdList);
            }else {
                List<Long> roleIdList = roles.stream()
                        .map(SysRole::getRoleId).collect(Collectors.toList());
                return sysApiService.selectApiPermsByRoleId(roleIdList);
            }
        }
        return new ArrayList<>();
    }
}
