package com.platform.system.login.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.platform.common.core.enums.UserTypeEnum;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.model.LoginUser;
import com.platform.system.api.model.SysUserDTO;
import com.platform.system.convert.SysUserConvert;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @author: tr
 * @date: 2025年02月14日 10:07
 */
@Service
public class MobilePhoneUserDetailServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserService sysUserService;

    @Override
    public SysUserDetails loadUserByUsername(String phonenumber) throws UsernameNotFoundException {
        // 根据手机号查询用户信息
        SysUser sysUser = sysUserService.selectUserByUserName(phonenumber);
        // 如果用户不存在，则创建用户
        if (sysUser == null) {
            sysUser = new SysUser();
            sysUser.setUserId(IdWorker.getId(sysUser));
            sysUser.setUserName(phonenumber);
            sysUser.setPhonenumber(phonenumber);
            sysUser.setUserType(UserTypeEnum.PERSONAL_USER.getCode());
            sysUserService.save(sysUser);
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setUserName(sysUser.getUserName());
        SysUserDTO sysUserDTO = SysUserConvert.INSTANCE.doToDTO(sysUser);
        loginUser.setSysUser(sysUserDTO);

        SysUserDetails sysUserDetails = SysUserConvert.INSTANCE.loginUserToSysUserDetails(loginUser);
        return sysUserDetails;
    }
}
