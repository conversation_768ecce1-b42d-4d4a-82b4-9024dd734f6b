package com.platform.system.utils;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.security.SecureRandom;

@Slf4j
public class ChineseToPinyinUtils {

    public static String getUserName(String name) {
        String result = convertChineseToPinyin(name);
        result = removeNonAlphanumeric(result);
        SecureRandom random = new SecureRandom();
        // 生成一个6位的随机数，范围是从100000到999999
        int randomNumber = random.nextInt(900000) + 100000;
        return result + randomNumber;
    }

    /**
     * 检查字符串是否包含中文，并将中文字符转换为拼音
     *
     * @param str 输入的字符串
     * @return 包含拼音的新字符串，非中文字符保持不变
     */
    private static String convertChineseToPinyin(String str) {
        StringBuilder result = new StringBuilder();
        for (char ch : str.toCharArray()) {
            // 检查当前字符是否为中文字符
            if (Character.UnicodeBlock.of(ch).equals(Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS)) {
                try {
                    // 使用pinyin4j库将中文字符转换为拼音
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch);
                    if (pinyinArray != null) {
                        String pinyinWithToneNumber = pinyinArray[0];
                        String pinyinWithoutToneNumber = pinyinWithToneNumber.replaceAll("[0-9]", "");
                        // 取第一个拼音，通常也是最常见的拼音
                        result.append(pinyinWithoutToneNumber);
                    } else {
                        // 如果没有找到对应的拼音，则舍弃字符
                    }
                } catch (Exception e) {
                    // 处理异常，通常是因为输入的汉字没有对应的拼音
                    log.error(e.getMessage());
                }
            } else {
                // 如果字符不是中文，则直接添加到结果字符串中
                result.append(ch);
            }
        }
        return result.toString();
    }

    /**
     * 去除字符串中的非字母和非数字字符
     *
     * @param str 需要清理的原始字符串
     * @return 清理后的字符串，只包含字母和数字
     */
    private static String removeNonAlphanumeric(String str) {
        return str.replaceAll("[^a-zA-Z0-9]", "");
    }
}
