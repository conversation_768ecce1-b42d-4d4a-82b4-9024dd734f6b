package com.platform.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * @Description: 全国地区编码实体类
 * @author: tr
 * @Date: 2024/3/18 14:48
 */
@Data
@ToString
@TableName("sys_country_address")
public class SysCountryAddress {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private String id;

	/**
	 * 地区编码CODE
	 */
	private String code;

	/**
	 * 父CODE（如果有层级关系使用）
	 */
	private String pcode;

	/**
	 * 父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY
	 */
	private String pcodePath;

	/**
	 * 如果有层级关系，从根节点开始至本节点的层级
	 */
	private Integer regionLevel;

	/**
	 * 字典表-地区类型
	 */
	private String regionType;

	/**
	 * 城市类型
	 */
	private String regionCityType;

	/**
	 * 国家行政区划代码-例如：110000
	 */
	private String regionOffcialCode;

	/**
	 * 全称聚合，例如：中国;北京市;朝阳区;
	 */
	private String regionFullName;

	/**
	 * 全称，例如：北京市
	 */
	private String regionName;

	/**
	 * 简称，例如：北京
	 */
	private String regionShortname;

	/**
	 * 行政简称，例如：京
	 */
	private String regionOffcialshortname;

	/**
	 * 国际化预留
	 */
	private String regionName1;

	/**
	 * 国际化预留
	 */
	private String regionName2;

	/**
	 * 区号
	 */
	private String regionCitycode;

	/**
	 * 邮编
	 */
	private String regionZipcode;

	/**
	 * 简称拼音，例如：BEIJING
	 */
	private String regionPinyin;

	/**
	 * 简称拼音首字母，例如：BJ
	 */
	private String regionPinyinFirstchar;

	/**
	 * 经度
	 */
	private String regionLng;

	/**
	 * 纬度
	 */
	private String regionLat;

	/**
	 * 地区排序
	 */
	private Integer regionSort;

	/**
	 * 备注
	 */
	private String regionRemark;

	/**
	 * 地区_属性1（备用字段）
	 */
	private String regionAttr1;

	/**
	 * 地区_属性2（备用字段）
	 */
	private String regionAttr2;

	/**
	 * 教育局备案，有效期时间，单位：年
	 */
	private String regionAttr3;

	/**
	 * 字典表-数据状态
	 */
	private String dataStatus;

	/**
	 * 版本
	 */
	private Integer version;

	/**
	 * 用户账号表CODE
	 */
	private String creatorAccountcode;

	/**
	 * 用户名
	 */
	private String creatorName;

	/**
	 * 用户账号表CODE
	 */
	private String updaterAccountcode;

	/**
	 * 用户名
	 */
	private String updaterName;

}
