package com.platform.system.convert;

import com.platform.system.domain.SysMessage;
import com.platform.system.domain.request.SysMessageReq;
import com.platform.system.domain.response.SysMessageResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 站内信消息信息转换
 * @author: tr
 * @date: 2024年02月29日 17:23
 */
@Mapper
public interface SysMessageConvert {

    SysMessageConvert INSTANCE = Mappers.getMapper(SysMessageConvert.class);

    SysMessage reqToDO(SysMessageReq sysMessageReq);

    SysMessageResp doToResp(SysMessage sysMessage);

    List<SysMessageResp> doListToRespList(List<SysMessage> sysMessageList);

}
