package com.platform.system.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.ctdi.common.starter.toolbox.annotation.Excel.ColumnType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 岗位表 sys_post
 * 
 * <AUTHOR>
 */
@Data
public class SysPostResp
{
    private static final long serialVersionUID = 1L;

    /** 岗位ID */
    @ApiModelProperty(value = "岗位ID")
    @Excel(name = "岗位序号", cellType = ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long postId;

    /** 岗位编码 */
    @ApiModelProperty(value = "岗位编码")
    @Excel(name = "岗位编码")
    private String postCode;

    /** 岗位名称 */
    @ApiModelProperty(value = "岗位名称")
    @Excel(name = "岗位名称")
    private String postName;

    /** 岗位排序 */
    @ApiModelProperty(value = "岗位排序")
    @Excel(name = "岗位排序")
    private Integer postSort;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1关闭）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 用户是否存在此岗位标识 默认不存在 */
    @ApiModelProperty(value = "用户是否存在此岗位标识 默认不存在")
    private boolean flag = false;
}
