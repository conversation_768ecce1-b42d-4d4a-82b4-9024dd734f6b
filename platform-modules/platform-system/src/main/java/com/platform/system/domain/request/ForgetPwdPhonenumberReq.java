package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 忘记密码通过手机号请求参数
 * @author: tr
 * @date: 2025年03月05日 16:27
 */
@Data
@ToString
public class ForgetPwdPhonenumberReq {

    /** 手机号码 **/
    @ApiModelProperty("手机号码")
    private String phonenumber;

    /** 唯一uuid **/
    @ApiModelProperty("唯一UUID")
    private String uuid;

    @ApiModelProperty("验证码类型，默认传5")
    private String type;

    @ApiModelProperty("验证码")
    private String validateCode;

    /** 新密码 **/
    @ApiModelProperty("新密码")
    private String newPassword;

    /** 再次输入新密码 **/
    @ApiModelProperty("再次输入新密码")
    private String againNewPassword;
}
