package com.platform.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.utils.DateUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.system.convert.SysLogininforConvert;
import com.platform.system.domain.request.SysLogininforPageReq;
import com.platform.system.domain.response.SysLogininforResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.platform.system.api.domain.SysLogininfor;
import com.platform.system.mapper.SysLogininforMapper;
import com.platform.system.service.SysLogininforService;

/**
 * 系统访问日志情况信息 服务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysLogininforServiceImpl extends ServiceImpl<SysLogininforMapper, SysLogininfor>
        implements SysLogininforService
{

    @Autowired
    private SysLogininforMapper logininforMapper;

    /**
     * 新增系统登录日志
     * 
     * @param logininfor 访问日志对象
     */
    @Override
    public int insertLogininfor(SysLogininfor logininfor)
    {
        logininfor.setAccessTime(new Date());
        save(logininfor);
        return 1;
    }

    /**
     * 查询系统登录日志集合
     * 
     * @param sysLogininforPageReq 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininforResp> selectLogininforList(SysLogininforPageReq sysLogininforPageReq)
    {
        LambdaQueryWrapper<SysLogininfor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(sysLogininforPageReq.getIpaddr()), SysLogininfor::getIpaddr, sysLogininforPageReq.getIpaddr());
        queryWrapper.like(StrUtil.isNotBlank(sysLogininforPageReq.getUserName()), SysLogininfor::getUserName, sysLogininforPageReq.getUserName());
        queryWrapper.eq(StrUtil.isNotBlank(sysLogininforPageReq.getStatus()), SysLogininfor::getStatus, sysLogininforPageReq.getStatus());

        Map<String, Object> params = sysLogininforPageReq.getParams();
        String beginTime = MapUtil.getStr(params, "beginTime");
        String endTime = MapUtil.getStr(params, "endTime");
        queryWrapper.between(StrUtil.isNotBlank(beginTime)
                , SysLogininfor::getAccessTime, DateUtils.parseDate(beginTime + " 00:00:00")
                , DateUtils.parseDate(endTime + " 23:59:59"));
        queryWrapper.orderByDesc(SysLogininfor::getAccessTime);

        List<SysLogininfor> list = list(queryWrapper);
        List<SysLogininforResp> sysLogininforRespList = SysLogininforConvert.INSTANCE.doListToRespList(list);
        return sysLogininforRespList;
    }

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }

    @Override
    public void saveLogininfor(String username, String status, String message) {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr());
        logininfor.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
        {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        }
        else if (Constants.LOGIN_FAIL.equals(status))
        {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        insertLogininfor(logininfor);
    }
}
