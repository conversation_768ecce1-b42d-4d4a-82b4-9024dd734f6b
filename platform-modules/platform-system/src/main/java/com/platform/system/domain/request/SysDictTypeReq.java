package com.platform.system.domain.request;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 字典类型新增或修改入参
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysDictTypeReq
{
    private static final long serialVersionUID = 1L;

    /** 字典ID */
    @ApiModelProperty(value = "字典ID")
    private Long dictId;

    /** 字典名称 */
    @NotBlank(message = "字典名称不能为空")
    @Excel(name = "字典名称")
    @Size(min = 0, max = 100, message = "字典类型名称长度不能超过100个字符")
    @ApiModelProperty(value = "字典名称")
    private String dictName;

    /** 字典类型 */
    @NotBlank(message = "字典类型不能为空")
    @Excel(name = "字典类型")
    @Size(min = 0, max = 100, message = "字典类型类型长度不能超过100个字符")
    @Pattern(regexp = "^[a-z][a-z0-9_]*$", message = "字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）")
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private String status;

    /** 备注 **/
    @Size(min = 0, max = 500, message = "备注长度不能超过500个字符")
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;
}
