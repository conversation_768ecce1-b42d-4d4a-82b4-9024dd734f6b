package com.platform.system.convert;

import com.platform.system.api.domain.response.SysMenuResp;
import com.platform.system.domain.SysMenu;
import com.platform.system.domain.request.SysMenuReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 菜单信息转换
 * @author: tr
 * @date: 2024年02月04日 9:23
 */
@Mapper
public interface SysMenuConvert {

    SysMenuConvert INSTANCE = Mappers.getMapper(SysMenuConvert.class);

    SysMenu reqToDO(SysMenuReq sysMenuReq);

    @Mapping(source = "perms", target = "perms", defaultValue = "")
    SysMenuResp doToResp(SysMenu sysMenu);

    List<SysMenuResp> doListToRespList(List<SysMenu> sysMenuList);
}
