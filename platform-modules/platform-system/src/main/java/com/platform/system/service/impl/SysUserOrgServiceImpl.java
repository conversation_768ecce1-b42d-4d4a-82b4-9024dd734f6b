package com.platform.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.domain.SysUserOrg;
import com.platform.system.mapper.SysUserOrgMapper;
import com.platform.system.service.SysUserOrgService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 用户与组织机构Service实现类
 * @author: tr
 * @date: 2024年03月18日 20:21
 */
@Service
public class SysUserOrgServiceImpl extends ServiceImpl<SysUserOrgMapper, SysUserOrg> implements SysUserOrgService {

    @Override
    public boolean existByOrgId(Long orgId) {
        LambdaQueryWrapper<SysUserOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserOrg::getOrgId, orgId);
        queryWrapper.last("LIMIT 1");
        SysUserOrg sysUserOrg = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysUserOrg)){
            return true;
        }
        return false;
    }

    @Override
    public void deleteByUserId(Long userId) {
        LambdaUpdateWrapper<SysUserOrg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUserOrg::getUserId, userId);
        remove(updateWrapper);
    }

    @Override
    public Long[] listByUserId(Long userId) {
        LambdaQueryWrapper<SysUserOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserOrg::getUserId, userId);
        List<SysUserOrg> list = list(queryWrapper);
        List<Long> orgIdList = list.stream().map(a->a.getOrgId()).collect(Collectors.toList());
        return orgIdList.toArray(new Long[0]);
    }
}
