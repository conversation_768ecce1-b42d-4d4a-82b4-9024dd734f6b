package com.platform.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.common.core.constant.SystemConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.datascope.annotation.DataPermission;
import com.platform.common.datascope.config.ThreadLocalKeyConfig;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import com.platform.system.api.domain.request.SysUserRoleReq;
import com.platform.system.api.domain.response.SysOrgRoleResp;
import com.platform.system.api.domain.response.SysRoleMenuResp;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.api.model.SysOrgDTO;
import com.platform.system.convert.SysRoleConvert;
import com.platform.system.domain.*;
import com.platform.system.domain.request.SysRoleMenuReq;
import com.platform.system.domain.request.SysRoleReq;
import com.platform.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.datascope.annotation.DataScope;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysRole;
import com.platform.system.mapper.SysRoleDeptMapper;
import com.platform.system.mapper.SysRoleMapper;
import com.platform.system.mapper.SysRoleMenuMapper;
import com.platform.system.mapper.SysUserRoleMapper;

/**
 * 角色 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService
{
    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysRoleDeptMapper roleDeptMapper;

    @Autowired
    private SysRoleRoleGroupService roleRoleGroupService;

    @Autowired
    private SysRoleAppService sysRoleAppService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @Autowired
    private SysOrgService sysOrgService;

    @Autowired
    private ThreadLocalKeyConfig threadLocalKeyConfig;

    /**
     * 根据条件分页查询角色数据
     * 
     * @param sysRoleQueryReq 角色信息
     * @return 角色数据集合信息
     */
    @Override
    @DataScope(permissionField = "sys_role.role_id", dataType = SystemConstants.DATA_TYPE_ROLE)
    public List<SysRoleResp> selectRoleList(SysRoleQueryReq sysRoleQueryReq)
    {
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysRoleQueryReq.getRoleId()), SysRole::getRoleId, sysRoleQueryReq.getRoleId());
        queryWrapper.like(StrUtil.isNotEmpty(sysRoleQueryReq.getRoleName()), SysRole::getRoleName, sysRoleQueryReq.getRoleName());
        queryWrapper.eq(StrUtil.isNotEmpty(sysRoleQueryReq.getStatus()), SysRole::getStatus, sysRoleQueryReq.getStatus());
        queryWrapper.like(StrUtil.isNotEmpty(sysRoleQueryReq.getRoleKey()), SysRole::getRoleKey, sysRoleQueryReq.getRoleKey());
        queryWrapper.between(StrUtil.isNotEmpty(sysRoleQueryReq.getBeginTime()), SysRole::getCreateTime, sysRoleQueryReq.getBeginTime() + " 00:00:00", sysRoleQueryReq.getEndTime() + " 23:59:59");
        queryWrapper.eq(SysRole::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.orderByAsc(SysRole::getRoleSort);
        List<SysRole> sysRoleList = list(queryWrapper);
        List<SysRoleResp> list = SysRoleConvert.INSTANCE.doListToRespList(sysRoleList);
        return list;
    }

    /**
     * 根据用户ID查询角色
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRoleResp> selectRolesByUserId(Long userId)
    {
        List<SysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
        List<SysRoleResp> roles = selectRoleAll();
        for (SysRoleResp role : roles)
        {
            for (SysRole userRole : userRoles)
            {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue())
                {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    @Override
    public List<SysRole> selectRoleListToRoleByUserId(Long userId) {
        return roleMapper.selectRolePermissionByUserId(userId);
    }

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId)
    {
        List<SysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms)
        {
            if (StringUtils.isNotNull(perm))
            {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     * 
     * @return 角色列表
     */
    @Override
    public List<SysRoleResp> selectRoleAll()
    {
        return SpringUtils.getAopProxy(this).selectRoleList(new SysRoleQueryReq());
    }

    /**
     * 通过角色ID查询角色
     * 
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRoleResp selectRoleById(Long roleId)
    {
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRole::getRoleId, roleId);
        queryWrapper.eq(SysRole::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysRole role = getOne(queryWrapper);
        if (ObjectUtil.isEmpty(role)){
            throw new ServiceException("角色信息不存在!");
        }
        SysRoleResp sysRoleResp = SysRoleConvert.INSTANCE.doToResp(role);
        //查询角色与应用之间的关系
        List<SysRoleApp> sysRoleAppList = sysRoleAppService.listByRoleId(role.getRoleId());
        List<Long> appIds = sysRoleAppList.stream().map(r -> r.getAppId()).collect(Collectors.toList());
        sysRoleResp.setAppIds(appIds.toArray(new Long[]{}));
        //查询角色与菜单之间的关系
        List<SysRoleMenu> list = sysRoleMenuService.listByRoleId(role.getRoleId());
        Map<Long, List<Long>> roleMenuMap = list.stream().collect(Collectors.toMap(SysRoleMenu::getAppId,
                p ->  {
                    List<Long> getNameList = new ArrayList<>();
                    getNameList.add(p.getMenuId());
                    return getNameList;
                },
                (List<Long> value1, List<Long> value2) -> {
                    value1.addAll(value2);
                    return value1;
                }
        ));
        List<SysRoleMenuResp> roleMenuList = new ArrayList<>();
        roleMenuMap.forEach((key, value) -> {
            SysRoleMenuResp sysRoleMenuResp = new SysRoleMenuResp();
            sysRoleMenuResp.setAppId(key);
            sysRoleMenuResp.setMenuIds(value.toArray(new Long[]{}));
            roleMenuList.add(sysRoleMenuResp);
        });
        sysRoleResp.setRoleMenuList(roleMenuList);
        return sysRoleResp;
    }

    /**
     * 校验角色名称是否唯一
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(SysRole role)
    {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleNameUnique(role.getRoleName());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleKeyUnique(SysRole role)
    {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否有数据权限
     * 
     * @param roleId 角色id
     */
    @Override
    public void checkRoleDataScope(Long roleId)
    {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserType()))
        {
            SysRoleQueryReq role = new SysRoleQueryReq();
            role.setRoleId(roleId);
            List<SysRoleResp> roles = SpringUtils.getAopProxy(this).selectRoleList(role);
            if (StringUtils.isEmpty(roles))
            {
                throw new ServiceException("没有权限访问角色数据！");
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId)
    {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     * 
     * @param sysRoleReq 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(dataType = SystemConstants.DATA_TYPE_ROLE, operType = SystemConstants.OPER_TYPE_INSERT)
    public int insertRole(SysRoleReq sysRoleReq)
    {
        SysRole role = SysRoleConvert.INSTANCE.reqToDO(sysRoleReq);
        if (!this.checkRoleNameUnique(role))
        {
            throw new ServiceException("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        else if (!this.checkRoleKeyUnique(role))
        {
            throw new ServiceException("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        // 新增角色信息
        boolean flag = save(role);
        if (flag){
            sysRoleReq.setRoleId(role.getRoleId());
            insertRoleApp(sysRoleReq);
            insertRoleMenu(sysRoleReq);

            threadLocalKeyConfig.set(role.getRoleId());
            return 1;
        }
        return 0;
    }

    /**
     * 修改保存角色信息
     * 
     * @param sysRoleReq 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(SysRoleReq sysRoleReq)
    {
        SysRole role = SysRoleConvert.INSTANCE.reqToDO(sysRoleReq);
        this.checkRoleDataScope(role.getRoleId());
        if (!this.checkRoleNameUnique(role))
        {
            throw new ServiceException("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        else if (!this.checkRoleKeyUnique(role))
        {
            throw new ServiceException("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }

        // 修改角色信息
        boolean flag = updateById(role);
        if (flag){
            // 删除角色与菜单关联
            roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
            //删除角色与应用关联
            sysRoleAppService.removeByRoleId(role.getRoleId());
            insertRoleApp(sysRoleReq);
            insertRoleMenu(sysRoleReq);
            return 1;
        }
        return 0;
    }

    /**
     * 修改角色状态
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role)
    {
        boolean flag = updateById(role);
        return flag ? 1 : 0;
    }

    /**
     * 修改数据权限信息
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int authDataScope(SysRole role)
    {
        // 修改角色信息
        updateById(role);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(role.getRoleId());
        // 新增角色和部门信息（数据权限）
        return insertRoleDept(role);
    }

    /**
     * 新增角色与菜单关系信息以及角色与应用关系信息
     * 
     * @param sysRoleReq 角色对象
     */
    public void insertRoleMenu(SysRoleReq sysRoleReq){
        Long roleId = sysRoleReq.getRoleId();

        List<SysRoleMenuReq> roleMenuList = sysRoleReq.getRoleMenuList();
        if (ObjectUtil.isNotEmpty(roleMenuList)){
            // 新增角色与菜单关联
            List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
            for (SysRoleMenuReq sysRoleMenuReq : sysRoleReq.getRoleMenuList()){
                Long[] menuIds = sysRoleMenuReq.getMenuIds();
                for (Long menuId : menuIds){
                    SysRoleMenu rm = new SysRoleMenu();
                    rm.setRoleId(roleId);
                    rm.setAppId(sysRoleMenuReq.getAppId());
                    rm.setMenuId(menuId);
                    list.add(rm);
                }
            }
            sysRoleMenuService.saveBatch(list);
        }
    }

    /**
     * @Description: 新增用户与角色绑定关系
     * @author: tr
     * @Date: 2024/4/1 15:29
     * @param: [sysRoleReq]
     * @returnValue: void
     */
    private void insertRoleApp(SysRoleReq sysRoleReq){
        Long roleId = sysRoleReq.getRoleId();

        //新增角色与应用关联
        Long[] appIds = sysRoleReq.getAppIds();
        if (ObjectUtil.isNotEmpty(appIds)){
            List<SysRoleApp> sysRoleAppList = new ArrayList<>();
            for (Long appId : appIds){
                SysRoleApp sysRoleApp = new SysRoleApp();
                sysRoleApp.setRoleId(roleId);
                sysRoleApp.setAppId(appId);
                sysRoleAppList.add(sysRoleApp);
            }
            sysRoleAppService.saveBatch(sysRoleAppList);
        }
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(SysRole role)
    {
        int rows = 1;
        if (ObjectUtil.isNotEmpty(role.getDeptIds())){
            // 新增角色与部门（数据权限）管理
            List<SysRoleDept> list = new ArrayList<SysRoleDept>();
            for (Long deptId : role.getDeptIds())
            {
                SysRoleDept rd = new SysRoleDept();
                rd.setRoleId(role.getRoleId());
                rd.setDeptId(deptId);
                list.add(rd);
            }
            if (list.size() > 0)
            {
                rows = roleDeptMapper.batchRoleDept(list);
            }
        }
        return rows;
    }

    /**
     * 批量删除角色信息
     * 
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @DataPermission(dataType = SystemConstants.DATA_TYPE_ROLE, operType = SystemConstants.OPER_TYPE_DELETE)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(Long[] roleIds)
    {
        for (Long roleId : roleIds)
        {
            checkRoleDataScope(roleId);
            SysRoleResp role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0)
            {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        //删除角色与角色组关联
        roleRoleGroupService.removeByRoleIds(roleIds);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDept(roleIds);
        //删除角色与应用关联
        sysRoleAppService.removeByRoleIds(roleIds);
        int count = roleMapper.deleteRoleByIds(roleIds);

        threadLocalKeyConfig.setLocals(roleIds);
        return count;
    }

    /**
     * 取消授权用户角色
     * 
     * @param sysUserRoleReq 用户和角色关联信息
     * @return 结果
     */
    @Override
    public int deleteAuthUser(SysUserRoleReq sysUserRoleReq)
    {
        LambdaUpdateWrapper<SysUserRole> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUserRole::getUserId, sysUserRoleReq.getUserId());
        updateWrapper.eq(SysUserRole::getRoleId, sysUserRoleReq.getRoleId());
        boolean flag = sysUserRoleService.remove(updateWrapper);
        return flag ? 1 : 0;
    }

    /**
     * 批量取消授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds)
    {
        return userRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    /**
     * 批量选择授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */


    public int insertAuthUsers(Long roleId, Long[] userIds)
    {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<SysUserRole>();
        for (Long userId : userIds)
        {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return userRoleMapper.batchUserRole(list);
    }

    @Override
    public List<SysRoleResp> listRoleByOrgIds(Long[] orgIds) {
        List<SysRole> sysRoleList = roleMapper.listRoleByOrgIds(orgIds);
        //去重
        sysRoleList = sysRoleList.stream().distinct().collect(Collectors.toList());
        List<SysRoleResp> list = SysRoleConvert.INSTANCE.doListToRespList(sysRoleList);
        return list;
    }

    @Override
    public SysRole getByCode(String code) {
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRole::getRoleKey,code).eq(SysRole::getDelFlag, DelFlagEnum.NO_DELETED.getCode()).last("limit 1");
        SysRole sysRole = getOne(queryWrapper);
        if (ObjectUtil.isEmpty(sysRole)){
            return new SysRole(-1L);
        }
        return sysRole;
    }

    @Override
    public List<SysOrgRoleResp> listOrgRoles() {

        List<SysUserRole> sysUserRoles = sysUserRoleService.listByUserId(SecurityUtils.getUserId());

        List<SysOrg> sysOrgs = sysOrgService.listByOrdIds(sysUserRoles.stream().map(SysUserRole::getOrgId).collect(Collectors.toList()));

        if (CollUtil.isNotEmpty(sysOrgs)){
            List<SysOrgRoleResp> result = new ArrayList<>();
            sysOrgs.stream().forEach(sysOrgDTO -> {
                List<Long> roles = sysUserRoles.stream().filter(sysUserRole -> sysUserRole.getOrgId().equals(sysOrgDTO.getId()))
                        .map(SysUserRole::getRoleId).collect(Collectors.toList());
                SysOrgRoleResp sysOrgRoleResp = BeanUtil.copyProperties(sysOrgDTO, SysOrgRoleResp.class);
                List<SysRole> sysRoleResps = listByIds(roles);
                if (CollUtil.isNotEmpty(sysRoleResps)){
                    sysOrgRoleResp.setRoles(BeanUtil.copyToList(sysRoleResps, SysRoleResp.class));
                }
                result.add(sysOrgRoleResp);
            });
            return result;
        }
        return Collections.emptyList();
    }
}
