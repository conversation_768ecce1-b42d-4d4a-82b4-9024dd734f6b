package com.platform.system.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.*;

@ApiModel("应用配置新增或修改参数")
@Data
@ToString
public class SysAppReq {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	private Long id;

	/**
	 * 应用名称
	 */
	@NotBlank(message = "应用名称不能为空")
	@Pattern(regexp = "^[\\u4e00-\\u9fa5_a-zA-Z0-9_]{1,100}", message = "应用名称格式不正确，支持中英文字母、数字和下划线组合，长度1-100个字符")
	@ApiModelProperty("应用名称")
	private String name;
	/**
	 * 应用编码
	 */
	@NotBlank(message = "应用标识不能为空")
	@Pattern(regexp = "^[a-zA-Z0-9_]{1,20}", message = "应用标识格式不正确，支持英文字母、数字和下划线组合，长度1-20个字符")
	@ApiModelProperty("应用编码")
	private String code;

	/**
	 * 图标地址
	 */
	@ApiModelProperty("图标地址")
	@Size(min = 0, max = 100, message = "应用图标不能超过100个字符")
	private String icon;

	/**
	 * 类型（1-PC端，2-APP端）
	 */
	@ApiModelProperty("类型（1-PC端，2-APP端）")
	private String type;

	/**
	 * 跳转地址
	 */
	@NotBlank(message = "应用链接不能为空")
	@ApiModelProperty("跳转地址")
	@Size(min = 1, max = 200, message = "应用链接不能超过200个字符")
	private String url;

	/**
	 * 显示顺序
	 */
	@ApiModelProperty("显示顺序")
	@NotNull(message = "应用排序不能为空")
	@Min(value = 1l, message = "应用排序取值在1-9999之间")
	@Max(value = 9999l, message = "应用排序取值在1-9999之间")
	private Integer orderNum;

	/**
	 * 外链标识，0-否，1-是
	 */
	@ApiModelProperty("外链标识，0-否，1-是")
	private Integer frameFlag;

	/**
	 * 备注
	 */
	@Size(min = 0, max = 500, message = "应用描述不能超过500个字符")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 状态（0正常 1停用）
	 */
	@ApiModelProperty("状态（0正常 1停用）")
	private String status;

	/**
	 * 业务归属
	 */
	@ApiModelProperty("业务归属 具体以字典为准")
	@NotNull(message = "业务归属不能为空")
	private String bizSense;

}
