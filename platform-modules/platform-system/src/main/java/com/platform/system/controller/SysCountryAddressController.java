package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.security.annotation.InnerAuth;
import com.platform.system.api.domain.request.SysCountryAddressQueryReq;
import com.platform.system.api.domain.response.SysCountryAddressResp;
import com.platform.system.domain.SysCountryAddress;
import com.platform.system.service.SysCountryAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 全国地区编码前端控制器
 * @author: tr
 * @date: 2024年03月18日 14:57
 */
@Api(tags = "全国地区编码前端控制器")
@RestController
@RequestMapping("/country")
public class SysCountryAddressController extends BaseController {

    @Autowired
    private SysCountryAddressService sysCountryAddressService;

//    @RequiresPermissions("system:country:list")
    @ApiOperation(value = "查询地区信息",  notes = "地址码")
    @GetMapping("list")
    public ResponseResult<List<SysCountryAddressResp>> list(SysCountryAddressQueryReq sysCountryAddressQueryReq){
        List<SysCountryAddressResp> list = sysCountryAddressService.list(sysCountryAddressQueryReq);
        return ResponseResult.ok(list);
    }

    @InnerAuth
    @GetMapping("listByPCodes")
    public ResponseResult<List<SysCountryAddressResp>> listByPCodes(String[] codeList){
        return ResponseResult.ok(sysCountryAddressService.listByPCodes(codeList));
    }
}
