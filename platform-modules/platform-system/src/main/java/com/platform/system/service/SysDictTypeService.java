package com.platform.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.SysDictData;
import com.platform.system.api.domain.SysDictType;
import com.platform.system.api.domain.request.SysDictTypePageReq;
import com.platform.system.api.domain.response.SysDictDataResp;
import com.platform.system.api.domain.response.SysDictTypeResp;
import com.platform.system.domain.request.SysDictTypeReq;

/**
 * 字典 业务层
 * 
 * <AUTHOR>
 */
public interface SysDictTypeService extends IService<SysDictType>
{
    /**
     * 根据条件分页查询字典类型
     * 
     * @param dictType 字典类型信息
     * @return 字典类型集合信息
     */
    public List<SysDictTypeResp> selectDictTypeList(SysDictTypePageReq sysDictTypePageReq);

    /**
     * @Description: 根据条件查询字典类型以及字典下的字典详情信息
     * @author: tr
     * @Date: 2024/6/14 18:04
     * @param: [sysDictTypePageReq]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysDictTypeResp>
     */
    List<SysDictTypeResp> selectDictTypeListAll(SysDictTypePageReq sysDictTypePageReq);

    /**
     * 根据所有字典类型
     * 
     * @return 字典类型集合信息
     */
    public List<SysDictTypeResp> selectDictTypeAll();

    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    public List<SysDictData> selectDictDataByType(String dictType);

    /**
     * 根据字典类型ID查询信息
     * 
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    public SysDictTypeResp selectDictTypeById(Long dictId);

    /**
     * 批量删除字典信息
     * 
     * @param dictIds 需要删除的字典ID
     */
    public void deleteDictTypeByIds(Long[] dictIds);

    /**
     * 加载字典缓存数据
     */
    public void loadingDictCache();

    /**
     * 清空字典缓存数据
     */
    public void clearDictCache();

    /**
     * 重置字典缓存数据
     */
    public void resetDictCache();

    /**
     * 新增保存字典类型信息
     * 
     * @param dictType 字典类型信息
     * @return 结果
     */
    public int insertDictType(SysDictType dictType);

    /**
     * 修改保存字典类型信息
     * 
     * @param dictType 字典类型信息
     * @return 结果
     */
    public int updateDictType(SysDictType dictType);

    /**
     * 校验字典类型称是否唯一
     * 
     * @param dictType 字典类型
     * @return 结果
     */
    public boolean checkDictTypeUnique(SysDictType dictType);

    /**
     * 通过字典类型和字典值查询字典数据
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典数据
     */
    SysDictDataResp selectDictDataByDictTypeAndDictValue(String dictType, String dictValue);

    /**
     * @Description: 批量保存字典类型数据
     * @author: tr
     * @Date: 2024/7/18 17:56
     * @param: [list]
     * @returnValue: void
     */
    String batchSaveDictType(List<SysDictTypeReq> sysDictTypeReqList);
}
