package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 修改密码的入参
 * @author: tr
 * @date: 2024年07月12日 10:08
 */
@Data
@ToString
public class UpdatePwdReq extends UpdatePwdMessageReq{

    /** 唯一uuid **/
    @ApiModelProperty("唯一UUID")
    private String uuid;

    /** 短信验证码 **/
    @ApiModelProperty("短信验证码")
    @NotBlank(message = "短信验证码不能为空")
    private String code;
}
