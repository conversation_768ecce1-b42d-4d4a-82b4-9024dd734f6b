package com.platform.system.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

@ApiModel("应用配置返回参数")
@Data
@ToString
public class SysAppResp {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	 * 应用名称
	 */
	@ApiModelProperty("应用名称")
	private String name;

	/**
	 * 应用编码
	 */
	@ApiModelProperty("应用编码")
	private String code;

	/**
	 * 图标地址
	 */
	@ApiModelProperty("图标地址")
	private String icon;

	/**
	 * 类型（1-PC端，2-APP端）
	 */
	@ApiModelProperty("类型（1-PC端，2-APP端）")
	private String type;

	/**
	 * 跳转地址
	 */
	@ApiModelProperty("跳转地址")
	private String url;

	/**
	 * 显示顺序
	 */
	@ApiModelProperty("显示顺序")
	private Integer orderNum;

	/**
	 * 外链标识，0-否，1-是
	 */
	@ApiModelProperty("外链标识，0-否，1-是")
	private Integer frameFlag;

	/**
	 * 公钥
	 */
	@ApiModelProperty("公钥")
	private String publicKey;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 状态（0正常 1停用）
	 */
	@ApiModelProperty("状态（0正常 1停用）")
	private String status;

	/**
	 * 业务归属
	 */
	@ApiModelProperty("业务归属以字典为准")
	private String bizSense;


	/**
	 * 图标名称
	 */
	@ApiModelProperty("图标名称")
	private String iconName;

	/**
	 * 权限标识，0-否，1-是
	 */
	@ApiModelProperty("权限标识，0-否，1-是")
	private Integer authorityFlag;

	/** 创建人 **/
	@ApiModelProperty("创建人")
	private String createBy;

	/** 创建时间 **/
	@ApiModelProperty("创建时间")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date createTime;
}
