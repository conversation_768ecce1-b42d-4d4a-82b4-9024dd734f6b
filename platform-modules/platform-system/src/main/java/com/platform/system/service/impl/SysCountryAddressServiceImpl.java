package com.platform.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.api.domain.request.SysCountryAddressQueryReq;
import com.platform.system.api.domain.response.SysCountryAddressResp;
import com.platform.system.convert.SysCountryAddressConvert;
import com.platform.system.domain.SysCountryAddress;
import com.platform.system.mapper.SysCountryAddressMapper;
import com.platform.system.service.SysCountryAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 全国地区编码Service实现类
 * @author: tr
 * @date: 2024年03月18日 14:56
 */
@Service
public class SysCountryAddressServiceImpl extends ServiceImpl<SysCountryAddressMapper, SysCountryAddress>
        implements SysCountryAddressService {

    //地区编码，默认湖南省
    @Value("${country.code:430000000000}")
    private String countryCode;

    @Autowired
    private CacheClient cacheClient;

    @Override
    public List<SysCountryAddressResp> list(SysCountryAddressQueryReq sysCountryAddressQueryReq) {
        String pCode = sysCountryAddressQueryReq.getPcode();
        LambdaQueryWrapper<SysCountryAddress> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isBlank(pCode)){
            //父级为空，从配置文件中获取编码
            queryWrapper.eq(SysCountryAddress::getCode, countryCode);
        }else{
            queryWrapper.eq(SysCountryAddress::getPcode, pCode);
        }
        queryWrapper.eq(ObjectUtil.isNotNull(sysCountryAddressQueryReq.getRegionLevel())
                , SysCountryAddress::getRegionLevel, sysCountryAddressQueryReq.getRegionLevel());
        List<SysCountryAddress> sysCountryAddressList = list(queryWrapper);
        List<SysCountryAddressResp> list = SysCountryAddressConvert.INSTANCE.doListToRespList(sysCountryAddressList);
        return list;
    }

    @Override
    public List<SysCountryAddress> listByCodes(List<String> codeList) {
        LambdaQueryWrapper<SysCountryAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysCountryAddress::getCode, codeList);
        return list(queryWrapper);
    }

    @Override
    public SysCountryAddress getByCode(String code) {
        LambdaQueryWrapper<SysCountryAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysCountryAddress::getCode, code);
        SysCountryAddress sysCountryAddress = getOne(queryWrapper);
        return sysCountryAddress;
    }

    @Override
    public List<SysCountryAddressResp> listByPCodes(String[] codeList) {
        List<SysCountryAddressResp> result = new ArrayList<>();

        LambdaQueryWrapper<SysCountryAddress> queryWrapper = null;
        for (String code : codeList) {
            //先从redis中取数据，再从数据库中取数据，最后将数据存入redis缓存
            List<SysCountryAddressResp> countryAddressRedisList =
                    cacheClient.getCacheList(String.format(CacheConstants.COUNTRY_ADDRESS_PCODES, code));
            if (ObjectUtil.isNotEmpty(countryAddressRedisList) && countryAddressRedisList.size() > 0){
                //redis中存在数据
                result.addAll(countryAddressRedisList);
            }else{
                //redis中不存在数据，则拼接条件去数据库中查询并缓存
                if (queryWrapper == null){
                    queryWrapper = new LambdaQueryWrapper<>();
                }
                queryWrapper.or().like(SysCountryAddress::getPcodePath, code);
            }

        }
        if (queryWrapper != null){
            List<SysCountryAddress> sysCountryAddressList = list(queryWrapper);
            List<SysCountryAddressResp> CountryAddressRespList = SysCountryAddressConvert.INSTANCE.doListToRespList(sysCountryAddressList);
            Map<String, List<SysCountryAddressResp>> map = new HashMap<>();
            for (SysCountryAddressResp sysCountryAddress : CountryAddressRespList){
                for (String code : codeList) {
                    //将所属同一父级的子节点归入对应父级下
                    if (sysCountryAddress.getPcodePath().contains(code)){
                        List<SysCountryAddressResp> list = map.get(code) == null ? new ArrayList<>() : map.get(code);
                        list.add(sysCountryAddress);
                        map.put(code, list);
                    }
                }
            }
            //以父类code为键存入缓存中
            map.forEach((key, value) ->
                    cacheClient.setCacheList(String.format(CacheConstants.COUNTRY_ADDRESS_PCODES, key), value));
        }

        return result;
    }
}
