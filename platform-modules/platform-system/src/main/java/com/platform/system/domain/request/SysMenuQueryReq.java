package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;


/**
 * 菜单权限查询参数
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysMenuQueryReq
{
    private static final long serialVersionUID = 1L;


    /** 应用ID */
    @ApiModelProperty(value = "应用ID")
    private Long appId;

    /**
     * 类型（1-PC端，2-APP端）
     */
    @ApiModelProperty(value = "应用ID")
    private String type;

    /** 菜单名称 */
    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    /** 显示状态（0显示 1隐藏） */
    @ApiModelProperty(value = "显示状态（0显示 1隐藏）")
    private String visible;
    
    /** 菜单状态（0正常 1停用） */
    @ApiModelProperty(value = "菜单状态（0正常 1停用）")
    private String status;

    /** 应用编码 */
    @ApiModelProperty(value = "应用编码")
    private String appCode;
}
