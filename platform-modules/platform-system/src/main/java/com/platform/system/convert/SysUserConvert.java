package com.platform.system.convert;

import com.platform.system.api.domain.SysUser;
import com.platform.system.api.domain.request.SysUserPageReq;
import com.platform.system.api.domain.request.SysUserReq;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.api.model.LoginUser;
import com.platform.system.api.model.SysUserDTO;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.openapi.domain.response.UserResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 用户信息转换
 * @author: tr
 * @date: 2024年02月04日 9:23
 */
@Mapper
public interface SysUserConvert {

    SysUserConvert INSTANCE = Mappers.getMapper(SysUserConvert.class);

    SysUser queryReqToDO(SysUserPageReq sysUserPageReq);

    SysUserResp doToResp(SysUser sysUser);

    List<SysUserResp> doListToRespList(List<SysUser> sysUserList);

    SysUserDTO doToDTO(SysUser sysUser);

    SysUser DTOToDO(SysUserDTO sysUserDTO);

    SysUser reqToDO(SysUserReq sysUserReq);

    UserResp DTOToResp(SysUserDTO sysUserDTO);

    /** OpenApi接口的返回参数 **/
    UserResp doToRespOpenApi(SysUser sysUser);
    /** OpenApi接口的返回参数 **/
    List<UserResp> doListToRespListOpenApi(List<SysUser> sysUserList);

    SysUserDetails loginUserToSysUserDetails(LoginUser loginUser);

    LoginUser sysUserDetailsToLoginUser(SysUserDetails sysUserDetails);
}
