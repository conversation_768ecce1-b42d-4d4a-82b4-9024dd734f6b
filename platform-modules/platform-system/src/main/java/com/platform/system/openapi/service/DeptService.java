package com.platform.system.openapi.service;

public interface DeptService {

    /**
     * 查询部门信息
     * @param updateDate 更新时间
     * @param code 应用编码
     * @return
     */
    String listAllV1(String updateDate, String code);

    /**
     * @Description: 分页查询部门信息
     * @author: tr
     * @Date: 2024/11/28 17:16
     * @param updateDate 更新时间
     * @param code   应用编码
     * @param pageNum  当前页数
     * @param pageSize 每页显示条数
     * @returnValue: java.lang.String
     */
    String listAllV2(String updateDate, String code, Integer pageNum, Integer pageSize);
}
