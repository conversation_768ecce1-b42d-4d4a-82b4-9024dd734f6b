package com.platform.system.login.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.login.domain.req.MobilePhoneReq;
import com.platform.system.login.domain.req.ThirdLoginReq;
import com.platform.system.login.domain.req.LoginReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;



/**
 * @Description: 登录前端控制器，供前端查看接口相关信息
 * @author: tr
 * @date: 2024年08月26日 20:08
 */
@Api(tags = "登录前端控制器")
@RestController
public class LoginController {

    @ApiOperation(value = "用户名密码登录",  notes = "登录")
    @PostMapping("login")
    public ResponseResult<?> login(@RequestBody LoginReq loginReq)
    {
        return ResponseResult.ok();
    }

    @ApiOperation(value = "退出",  notes = "登录")
    @PostMapping("logout")
    public ResponseResult<?> logout()
    {
        return ResponseResult.ok();
    }

    @ApiOperation(value = "第三方OpenAPI登录",  notes = "登录")
    @PostMapping("openapi/login")
    public ResponseResult<?> loginForThird(@RequestBody ThirdLoginReq form)
    {
        // 获取登录token
        return ResponseResult.ok();
    }

    @ApiOperation(value = "手机验证码登录",  notes = "登录")
    @PostMapping("/mobilephone/login")
    public ResponseResult<?> loginMobilePhone(@RequestBody MobilePhoneReq mobilePhoneReq)
    {
        return ResponseResult.ok();
    }

    @ApiOperation(value = "对接SW项目单点登录",  notes = "登录")
    @PostMapping("/login/single")
    public ResponseResult<?> loginForSingle(@RequestBody ThirdLoginReq form)
    {
        return ResponseResult.ok();
    }
}
