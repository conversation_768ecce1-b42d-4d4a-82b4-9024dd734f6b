package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 忘记密码修改密码的入参
 * @author: tr
 * @date: 2024年06月12日 9:26
 */
@Data
@ToString
public class ForgetPwdReq {

    /** 唯一uuid **/
    @ApiModelProperty("唯一UUID")
    private String uuid;

    /** 证件号码 **/
    @ApiModelProperty("证件号码")
    private String certificateNo;

    /** 新密码 **/
    @ApiModelProperty("新密码")
    private String newPassword;

    /** 再次输入新密码 **/
    @ApiModelProperty("再次输入新密码")
    private String againNewPassword;

}
