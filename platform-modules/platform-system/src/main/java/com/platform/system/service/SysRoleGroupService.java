package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysRoleGroup;
import com.platform.system.domain.request.SysRoleGroupPageReq;
import com.platform.system.domain.request.SysRoleGroupReq;
import com.platform.system.domain.request.SysRoleGroupRoleReq;
import com.platform.system.domain.response.SysRoleGroupResp;

import java.util.List;

/**
 * @Description: 角色组Service接口
 * @author: tr
 * @date: 2024年03月01日 10:42
 */
public interface SysRoleGroupService extends IService<SysRoleGroup> {

    /**
     * @Description: 查询角色组信息
     * @author: tr
     * @Date: 2024/3/1 11:26
     * @param: [sysRoleGroupPageReq]
     * @returnValue: java.util.List<com.platform.system.domain.response.SysRoleGroupResp>
     */
    List<SysRoleGroupResp> list(SysRoleGroupPageReq sysRoleGroupPageReq);

    /**
     * @Description: 查询角色组单个信息
     * @author: tr
     * @Date: 2024/3/20 9:54
     * @param: [id]
     * @returnValue: com.platform.system.domain.response.SysRoleGroupResp
     */
    SysRoleGroupResp getById(Long id);

    /**
     * @Description: 新增角色组信息
     * @author: tr
     * @Date: 2024/3/1 11:14
     * @param: [sysRoleGroupReq]
     * @returnValue: void
     */
    void save(SysRoleGroupReq sysRoleGroupReq);

    /**
     * @Description: 修改角色组信息
     * @author: tr
     * @Date: 2024/3/1 11:15
     * @param: [sysRoleGroupReq]
     * @returnValue: void
     */
    void update(SysRoleGroupReq sysRoleGroupReq);

    /**
     * @Description: 删除角色组信息
     * @author: tr
     * @Date: 2024/3/1 11:15
     * @param: [id]
     * @returnValue: void
     */
    void delete(Long id);

    /**
     * @Description: 角色组新增角色，可批量新增
     * @author: tr
     * @Date: 2024/3/1 14:45
     * @param: [sysRoleGroupRoleReq]
     * @returnValue: void
     */
    void saveRole(SysRoleGroupRoleReq sysRoleGroupRoleReq);

    /**
     * @Description: 根据角色组ID查询角色信息
     * @author: tr
     * @Date: 2024/3/20 14:41
     * @param: id
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysRoleResp>
     */
    List<String> listRoleByRoleGroupId(Long id);
}
