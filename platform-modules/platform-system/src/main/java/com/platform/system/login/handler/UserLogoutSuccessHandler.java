package com.platform.system.login.handler;

import cn.hutool.core.util.StrUtil;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.common.core.config.properties.JwtProperties;
import com.platform.common.core.utils.JwtUtils;
import com.platform.common.toolbox.service.TokenService;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.service.SysLogininforService;
import com.platform.system.utils.ResponseUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月26日 15:56
 */
@Component
public class UserLogoutSuccessHandler implements LogoutSuccessHandler {

    @Resource
    private TokenService tokenService;

    @Resource
    private SysLogininforService sysLogininforService;

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication){
        String token = TokenUtils.getToken(request);
        if (StrUtil.isNotBlank(token)){
            String username = JwtUtils.getUserName(token);

            tokenService.delLoginUser(token);

            sysLogininforService.saveLogininfor(username
                    , Constants.LOGOUT, "退出成功");
        }
        ResponseUtils.responseJson(response, ResponseResult.ok("退出成功。"));
    }
}
