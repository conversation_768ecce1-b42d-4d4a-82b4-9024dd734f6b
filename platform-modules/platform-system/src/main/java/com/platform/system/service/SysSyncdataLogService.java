package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.request.SysSyncdataLogReq;
import com.platform.system.api.domain.response.SysSyncdataLogResp;
import com.platform.system.domain.SysSyncdataLog;

import java.util.List;

/**
 * @Description: 数据同步日志记录Service接口
 * @author: tr
 * @date: 2025年03月26日 17:59
 */
public interface SysSyncdataLogService extends IService<SysSyncdataLog> {

    /**
     * @Description: 保存日志信息
     * @author: tr
     * @Date: 2025/3/26 18:05
     * @param: [log]
     * @returnValue: void
     */
    void saveLog(SysSyncdataLog log);

    /**
     * @Description: 批量保存日志信息
     * @author: tr
     * @Date: 2025/3/26 19:48
     * @param: [list]
     * @returnValue: void
     */
    void saveLogBatch(List<SysSyncdataLog> list);

    /**
     * @Description: 根据批次号查询待下发的日志信息
     * @author: tr
     * @Date: 2025/3/26 20:11
     * @param: [batchNumber]
     * @returnValue: java.util.List<com.platform.system.domain.SysSyndataLog>
     */
    List<SysSyncdataLogResp> listStayByBatchNumber(String batchNumber);

    /**
     * @Description: 根据批次号修改同步状态
     * @author: tr
     * @Date: 2025/3/26 20:49
     * @param: [sysSyncdataLogReq]
     * @returnValue: void
     */
    void updateSyncStatusByBatchNumber(SysSyncdataLogReq sysSyncdataLogReq);
}
