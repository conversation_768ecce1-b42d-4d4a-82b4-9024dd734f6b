package com.platform.system.login.handler;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.system.utils.ResponseUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 用户登录失败处理器
 * @author: tr
 * @date: 2024年08月26日 15:57
 */
@Component
public class UserLoinFailureHandler implements AuthenticationFailureHandler {

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) {
        ResponseUtils.responseJson(response, ResponseResult.fail(exception.getMessage()));
    }
}
