package com.platform.system.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.domain.response.ValidateCodeResp;
import com.platform.system.service.ValidateCodeService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 验证码前端控制器
 * @author: tr
 * @date: 2024年02月01日 15:44
 */
@Api(tags = "验证码前端控制器")
@RestController
@RequestMapping("/captchaImage")
public class ValidateCodeController {

    @Autowired
    private ValidateCodeService validateCodeService;

    @GetMapping
    public ResponseResult<ValidateCodeResp> getValidateCode(){
        ValidateCodeResp validateCodeResp = validateCodeService.createCaptcha();
        return ResponseResult.ok(validateCodeResp);
    }
}
