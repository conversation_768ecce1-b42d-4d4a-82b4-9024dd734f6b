package com.platform.system.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 修改手机号码入参
 * @author: tr
 * @date: 2025年03月04日 15:05
 */
@ApiModel("修改手机号码入参")
@Data
public class UpdatePhonenumberReq {

    @ApiModelProperty("原手机号码")
    private String oldPhonenumber;

    @ApiModelProperty("新手机号码")
    private String NewPhonenumber;

    @ApiModelProperty("验证码")
    private String validateCode;

    @ApiModelProperty("手机验证码的唯一uuid")
    private String uuid;

    /** 类型（1-注册验证码，2-登录验证码，3-设置密码的验证码，4-修改手机号码的验证码） **/
    @ApiModelProperty("类型，默认传4")
    private String type;

    @ApiModelProperty("下一步的UUID")
    private String nextUuid;
}
