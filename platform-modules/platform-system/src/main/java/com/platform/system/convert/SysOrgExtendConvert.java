package com.platform.system.convert;

import com.platform.system.api.domain.request.SysOrgExtendReq;
import com.platform.system.api.domain.response.SysOrgExtendResp;
import com.platform.system.domain.SysOrgExtend;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Description: 组织机构扩展信息转换类
 * @author: tr
 * @date: 2024年05月15日 11:32
 */
@Mapper
public interface SysOrgExtendConvert {

    SysOrgExtendConvert INSTANCE = Mappers.getMapper(SysOrgExtendConvert.class);

    SysOrgExtend reqToDO(SysOrgExtendReq sysOrgExtendReq);

    SysOrgExtendResp doToResp(SysOrgExtend sysOrgExtend);
}
