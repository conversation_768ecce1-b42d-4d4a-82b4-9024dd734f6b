package com.platform.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.domain.response.SysMenuResp;
import com.platform.system.domain.request.SysMenuQueryReq;
import com.platform.system.domain.request.SysMenuReq;
import com.platform.system.domain.vo.TreeSelect;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.service.SysMenuService;

/**
 * 菜单信息
 * 
 * <AUTHOR>
 */
@Api(tags = "菜单信息前端控制器")
@RestController
@RequestMapping("/menu")
public class SysMenuController extends BaseController
{
    @Autowired
    private SysMenuService menuService;

    /**
     * 获取菜单列表
     */
    @ApiOperation(value = "获取菜单列表，包含应用为第一级",  notes = "菜单信息")
    @RequiresPermissions("system:menu:list")
    @GetMapping("/list")
    public ResponseResult<List<SysMenuResp>> list(SysMenuQueryReq sysMenuQueryReq)
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenuResp> menus = menuService.selectMenuList(sysMenuQueryReq, userId);
        return ResponseResult.ok(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @ApiOperation(value = "根据菜单编号获取详细信息",  notes = "菜单信息")
    @RequiresPermissions("system:menu:query")
    @GetMapping(value = "/getInfo")
    public ResponseResult<SysMenuResp> getInfo(@RequestParam Long menuId)
    {
        return ResponseResult.ok(menuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @ApiOperation(value = "获取菜单下拉树列表",  notes = "菜单信息")
    @GetMapping("/treeselect")
    public ResponseResult<List<TreeSelect>> treeselect(SysMenuQueryReq sysMenuQueryReq)
    {
        List<SysMenuResp> menus = menuService.listMenu(sysMenuQueryReq);
        return ResponseResult.ok(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @ApiOperation(value = "加载对应角色菜单列表树",  notes = "菜单信息")
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public ResponseResult roleMenuTreeselect(@PathVariable("roleId") Long roleId)
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenuResp> menus = menuService.selectMenuList(userId);
        Map<String, Object> result = new HashMap<>();
        result.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        result.put("menus", menuService.buildMenuTreeSelect(menus));
        return ResponseResult.ok(result);
    }

    /**
     * 新增菜单
     */
    @ApiOperation(value = "新增菜单",  notes = "菜单信息")
    @RequiresPermissions("system:menu:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ResponseResult add(@Validated @RequestBody SysMenuReq sysMenuReq)
    {
        return menuService.insertMenu(sysMenuReq)==1?ResponseResult.ok():ResponseResult.fail("新增菜单失败!");
    }

    /**
     * 修改菜单
     */
    @ApiOperation(value = "修改菜单",  notes = "菜单信息")
    @RequiresPermissions("system:menu:edit")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult edit(@Validated @RequestBody SysMenuReq sysMenuReq)
    {
        return menuService.updateMenu(sysMenuReq)==1?ResponseResult.ok():ResponseResult.fail("修改菜单失败!");
    }

    /**
     * 删除菜单
     */
    @ApiOperation(value = "删除菜单",  notes = "菜单信息")
    @RequiresPermissions("system:menu:remove")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam Long menuId)
    {
        if (menuService.hasChildByMenuId(menuId))
        {
            return ResponseResult.fail("存在子菜单,不允许删除");
        }
        return menuService.deleteMenuById(menuId)>0?ResponseResult.ok():ResponseResult.fail("删除菜单失败");
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @ApiOperation(value = "获取路由信息",  notes = "菜单信息")
    @GetMapping("getRouters")
    public ResponseResult getRouters(String appCode)
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenuResp> menus = menuService.selectMenuTreeByUserId(userId, appCode);
        return ResponseResult.ok(menuService.buildMenus(menus));
    }

    @ApiOperation(value = "根据应用编码获取菜单列表，不将应用放在第一级",  notes = "菜单信息")
    @GetMapping("/listByAppCode")
    public ResponseResult<List<SysMenuResp>> listByAppCode(SysMenuQueryReq sysMenuQueryReq)
    {
        List<SysMenuResp> menus = menuService.listByAppCode(sysMenuQueryReq);
        return ResponseResult.ok(menus);
    }

}