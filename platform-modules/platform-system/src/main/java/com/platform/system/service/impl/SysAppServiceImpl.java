package com.platform.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.google.common.collect.Maps;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.model.LoginUser;
import com.platform.system.convert.SysAppConvert;
import com.platform.system.domain.SysApp;
import com.platform.system.domain.SysConfig;
import com.platform.system.domain.SysRoleApp;
import com.platform.system.domain.request.SysAppPageReq;
import com.platform.system.domain.request.SysAppReq;
import com.platform.system.domain.response.SysAppResp;
import com.platform.system.mapper.SysAppMapper;
import com.platform.system.service.SysAppService;
import com.platform.system.service.SysRoleAppService;
import com.platform.system.utils.KeyUtils;
import com.platform.system.utils.RSAUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 应用管理Service层
 * @author: tr
 * @date: 2024年03月27日 14:10
 */
@RefreshScope
@Service
public class SysAppServiceImpl extends ServiceImpl<SysAppMapper, SysApp> implements SysAppService {

    @Autowired
    private SysRoleAppService sysRoleAppService;

    @Autowired
    private CacheClient redisService;

    /** 应用链接的白名单集合，只有在白名单中的，才允许配置链接地址 **/
    @Value("${app.url.whiteList:}")
    private List<String> appUrlWhiteList = new ArrayList<>();

    @PostConstruct
    public void init()
    {
        loadingAppCache();
    }

    @Override
    public List<SysAppResp> list(SysAppPageReq sysAppPageReq) {
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(sysAppPageReq.getType()), SysApp::getType, sysAppPageReq.getType());
        queryWrapper.eq(SysApp::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.like(StrUtil.isNotBlank(sysAppPageReq.getName()), SysApp::getName, sysAppPageReq.getName());
        queryWrapper.like(StrUtil.isNotBlank(sysAppPageReq.getCode()), SysApp::getCode, sysAppPageReq.getCode());
        queryWrapper.eq(StrUtil.isNotBlank(sysAppPageReq.getStatus()), SysApp::getStatus, sysAppPageReq.getStatus());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysAppPageReq.getFrameFlag()), SysApp::getFrameFlag, sysAppPageReq.getFrameFlag());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysAppPageReq.getBizSense()), SysApp::getBizSense, sysAppPageReq.getBizSense());

        queryWrapper.orderByAsc(SysApp::getOrderNum);
        List<SysApp> sysAppList = list(queryWrapper);
        List<SysAppResp> list = SysAppConvert.INSTANCE.doListToRespList(sysAppList);
        return list;
    }

    @Override
    public SysAppResp getById(Long id) {
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApp::getId, id);
        queryWrapper.eq(SysApp::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysApp sysApp = getOne(queryWrapper);
        SysAppResp sysAppResp = SysAppConvert.INSTANCE.doToResp(sysApp);
        return sysAppResp;
    }

    @Override
    public void save(SysAppReq sysAppReq) {
        checkAppUrl(sysAppReq.getUrl());

        checkExist(sysAppReq);

        SysApp sysApp = SysAppConvert.INSTANCE.reqToDO(sysAppReq);

        //创建RSA加密的公钥和私钥，严禁外泄
        Map<String, String> keyMap = KeyUtils.generateSmKey();
        //公钥
        sysApp.setPublicKey(keyMap.get(SecurityConstants.PUBLIC_KEY));
        //私钥
        sysApp.setPrivateKey(keyMap.get(SecurityConstants.PRIVATE_KEY));
        save(sysApp);

        loadingAppCache();
    }

    @Override
    public void update(SysAppReq sysAppReq) {
        checkAppUrl(sysAppReq.getUrl());

        checkExist(sysAppReq);
        SysApp sysApp = SysAppConvert.INSTANCE.reqToDO(sysAppReq);
        updateById(sysApp);

        loadingAppCache();
    }

    @Override
    public void delete(Long id) {
        SysApp sysApp = new SysApp();
        sysApp.setId(id);
        sysApp.setDelFlag(DelFlagEnum.DELETED.getCode());
        updateById(sysApp);

        loadingAppCache();
    }

    @Override
    public List<SysAppResp> listUserApp(String type) {
        SysAppPageReq sysAppPageReq = new SysAppPageReq();
        sysAppPageReq.setType(type);
        List<SysAppResp> sysAppRespList = list(sysAppPageReq);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> roleIds = loginUser.getSysUser().getRolesDTO().stream().map(r->r.getRoleId()).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(roleIds)){
            LambdaQueryWrapper<SysRoleApp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRoleApp::getRoleId, roleIds);
            List<SysRoleApp> sysRoleAppList = sysRoleAppService.list(queryWrapper);
            List<Long> appIdList = sysRoleAppList.stream().distinct().map(r->r.getAppId()).collect(Collectors.toList());

            sysAppRespList.forEach(a ->{
                a.setAuthorityFlag(0);
                if (appIdList.contains(a.getId())){
                    a.setAuthorityFlag(1);
                }
            });
        }

        return sysAppRespList;
    }

    /**
     * @Description: 检查同一类型下应用名称不能重复，检查全局下应用编码不能重复
     * @author: tr
     * @Date: 2024/3/27 15:11
     * @param sysAppReq
     * @returnValue: void
     */
    private void checkExist(SysAppReq sysAppReq){
        Long id = sysAppReq.getId();
        String name = sysAppReq.getName();
        String code = sysAppReq.getCode();
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApp::getName, name);
        //判断同一类型下应用名称不能重复
        queryWrapper.eq(SysApp::getType, sysAppReq.getType());
        queryWrapper.eq(SysApp::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.ne(ObjectUtil.isNotNull(id), SysApp::getId, id);
        queryWrapper.last("limit 1");
        SysApp sysApp = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysApp)){
            throw new ServiceException("应用名称已存在!");
        }

        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApp::getCode, code);
        queryWrapper.eq(SysApp::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.ne(ObjectUtil.isNotNull(id), SysApp::getId, id);
        queryWrapper.last("limit 1");
        SysApp sysAppCode = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysAppCode)){
            throw new ServiceException("应用编码已存在!");
        }
    }

    /**
     * @Description: 加载应用缓存到数据库
     * @author: tr
     * @Date: 2024/4/9 16:07
     * @param: []
     * @returnValue: void
     */
    private void loadingAppCache()
    {
        HashMap<String, String> cacheMap = Maps.newHashMap();
        HashMap<String, String> codePublicKeycacheMap = Maps.newHashMap();
        HashMap<String, SysApp> codeObjMap = Maps.newHashMap();
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApp::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        List<SysApp> sysAppList = list(queryWrapper);
        for (SysApp app : sysAppList)
        {
            cacheMap.put(app.getCode(), app.getPrivateKey());
            codePublicKeycacheMap.put(app.getCode(), app.getPublicKey());
            codeObjMap.put(app.getCode(), app);
        }
        redisService.setCacheMap(CacheConstants.APP_KEY_PAIR_MAP,cacheMap);
        redisService.setCacheMap(CacheConstants.APP_CODE_PUBLIC_KEY_MAP,codePublicKeycacheMap);
        redisService.setCacheMap(CacheConstants.APP_CODE_OBJECT_MAP, codeObjMap);
    }

    /**
     * @Description: 检查应用链接是否符合规范
     * @author: tr
     * @Date: 2024/9/14 17:09
     * @param: [url]
     * @returnValue: void
     */
    private void checkAppUrl(String url){
        if (ObjectUtil.isEmpty(appUrlWhiteList)){
            //是空的就不进行限制
            return ;
        }
        if (!appUrlWhiteList.contains(url)){
            //不是允许配置的应用链接
            throw new ServiceException("不是允许配置的应用链接");
        }
    }

    @Override
    public void addAppVisit(Long appId) {
        String today = LocalDate.now() + ":";
        String key = CacheConstants.APP_VISIT_COUNT + today + appId;
        Object count = redisService.getCacheObject(key);
        if (count == null) {
            redisService.setCacheObject(key, 1, 60 * 24L, TimeUnit.MINUTES);
        } else {
            redisService.incr(key);
        }
    }

    @Override
    public Map<String,Object> getAppVisit(){
        Map<String, Object> result = new HashMap<>();

        String today = LocalDate.now() + ":";
        String key = CacheConstants.APP_VISIT_COUNT + today;
        List<SysApp> appList = list();
        List<Map<String, Object>> appCountList = new ArrayList<>();
        for (SysApp app : appList) {
            Map<String, Object> appMap = new HashMap<>();
            Integer count = redisService.getCacheObject(key + app.getId());
            count = count == null ? 0 : count;
            appMap.put("appName", app.getName());
            appMap.put("count", count);
            appCountList.add(appMap);
        }
        appCountList.sort((map1, map2) -> {
            Integer value1 = map1.get("count") == null ? 0 : (Integer) map1.get("count");
            Integer value2 = map2.get("count") == null ? 0 : (Integer) map2.get("count");
            return value2.compareTo(value1); // 降序排列
        });
        result.put("total", appList.size());
        result.put("appCountList", appCountList);
        return result;
    }
}
