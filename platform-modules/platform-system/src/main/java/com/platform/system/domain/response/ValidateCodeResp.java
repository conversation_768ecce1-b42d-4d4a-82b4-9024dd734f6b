package com.platform.system.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 验证码返回类
 * @author: tr
 * @date: 2024年02月01日 15:50
 */
@Data
public class ValidateCodeResp {

    @ApiModelProperty("验证码开启标识")
    private Boolean captchaEnabled;

    @ApiModelProperty("验证码图片的base64位字符串")
    private String img;

    @ApiModelProperty("验证码唯一uuid")
    private String uuid;

    @ApiModelProperty("加密盐值")
    private String securitySalt;
}
