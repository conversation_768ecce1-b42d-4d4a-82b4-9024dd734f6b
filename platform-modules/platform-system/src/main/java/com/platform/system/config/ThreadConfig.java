//package com.platform.system.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
///**
// * @Description: 线程配置
// * @author: tr
// * @date: 2024年01月29日 10:30
// */
//@Slf4j
//@Configuration
//public class ThreadConfig {
//
//    @Bean
//    public ThreadPoolTaskExecutor threadPoolExecutor() {
//        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
//
//        // 返回可用处理器的Java虚拟机的数量
//        int processNum = Runtime.getRuntime().availableProcessors();
//        int corePoolSize = (int) (processNum / (1 - 0.2));
//        int maxPoolSize = (int) (processNum / (1 - 0.5));
//        log.info("核心线程数：{}，最大线程数：{}", corePoolSize, maxPoolSize);
//
//        // 核心池大小
//        pool.setCorePoolSize(corePoolSize);
//
//        // 最大线程数
//        pool.setMaxPoolSize(maxPoolSize);
//
//        // 队列程度
//        pool.setQueueCapacity(maxPoolSize * 1000);
//        pool.setThreadPriority(Thread.MAX_PRIORITY);
//        pool.setDaemon(false);
//
//        // 线程空闲时间
//        pool.setKeepAliveSeconds(300);
//        return pool;
//    }
//}
