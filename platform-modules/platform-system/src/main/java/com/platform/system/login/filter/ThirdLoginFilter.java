package com.platform.system.login.filter;

import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.text.Convert;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.UserConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.utils.AESUtils;
import com.platform.system.login.config.third.ThirdAuthenticationToken;
import com.platform.system.login.domain.req.ThirdLoginReq;
import com.platform.system.service.SysLogininforService;
import com.platform.system.service.impl.SysLogininforServiceImpl;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Map;

/**
 * @Description: 第三方登录过滤器
 * @author: tr
 * @date: 2024年08月30日 11:23
 */
public class ThirdLoginFilter extends AbstractAuthenticationProcessingFilter {

    public CacheClient cacheClient = SpringUtils.getBean(CacheClient.class);

    private SysLogininforService sysLogininforService = SpringUtils.getBean(SysLogininforServiceImpl.class);

    public ThirdLoginFilter(AuthenticationManager authenticationManager) {
        super(new AntPathRequestMatcher("/openapi/login", "POST"), authenticationManager);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        //获取POST请求中body的入参信息并转换为LoginReq对象
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        BufferedReader reader = req.getReader();
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line).append('\n');
        }
        String body = stringBuilder.toString();
        ThirdLoginReq loginReq = JSONUtil.toBean(body, ThirdLoginReq.class);

        //用户名
        String username = loginReq.getUsername();
        //获取加密盐值
        String appKey = loginReq.getAppKey();
        Map<String, String> appKeyPair = cacheClient.getCacheMap(CacheConstants.APP_KEY_PAIR_MAP);
        if(!appKeyPair.containsKey(appKey)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_APP_KEY_NOT_EXIST.getMsg(), ExceptionEnum.CHECK_SIGN_APP_KEY_NOT_EXIST.getCode());
        }
        String securitySalt = appKeyPair.get(appKey).substring(0,32);
        //密码
        String password = loginReq.getPassword();
        //解密
        password = AESUtils.decrypt(password, securitySalt);
        loginReq.setPassword(password);
        if (StringUtils.isAnyBlank(username, password)){
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.USER_PWD_NOT_NULL.getMsg());
            throw new ServiceException(ExceptionEnum.USER_PWD_NOT_NULL.getMsg());
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.PWD_NOT_SCOPE.getMsg());
            throw new ServiceException(ExceptionEnum.PWD_NOT_SCOPE.getMsg());
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.USER_NOT_SCOPE.getMsg());
            throw new ServiceException(ExceptionEnum.USER_NOT_SCOPE.getMsg());
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(cacheClient.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.IP_BLACK_LIST.getMsg());
            throw new ServiceException(ExceptionEnum.IP_BLACK_LIST.getMsg());
        }

        ThirdAuthenticationToken thirdAuthenticationToken = new ThirdAuthenticationToken(loginReq);
        setDetails(req, thirdAuthenticationToken);
        return this.getAuthenticationManager().authenticate(thirdAuthenticationToken);
    }

    protected void setDetails(HttpServletRequest request, ThirdAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }
}
