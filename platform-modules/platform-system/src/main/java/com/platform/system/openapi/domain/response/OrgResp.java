package com.platform.system.openapi.domain.response;

import lombok.Data;

/**
 * @Description: 组织机构OpenApi返回参数
 * @author: tr
 * @date: 2024年04月09日 16:40
 */
@Data
public class OrgResp {

    /** 主键ID **/
    private Long id;

    /**
     * 机构（主体）名称
     */
    private String name;

    /** 机构编码 **/
    private String orgCode;

    /**
     * 组织机构类型
     */
    private Integer orgType;

    /**
     * 父级机构Id
     */
    private Long parentId;

    /**
     * 联系人
     */
    private String contactsPerson;

    /**
     * 联系电话
     */
    private String contactsPhone;
}
