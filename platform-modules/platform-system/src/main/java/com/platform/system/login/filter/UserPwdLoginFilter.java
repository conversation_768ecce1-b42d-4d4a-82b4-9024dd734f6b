package com.platform.system.login.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.CaptchaException;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.text.Convert;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.UserConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.utils.AESUtils;
import com.platform.system.config.properties.CaptchaProperties;
import com.platform.system.login.config.code.UserPwdAuthenticationToken;
import com.platform.system.login.domain.req.LoginReq;
import com.platform.system.service.SysLogininforService;
import com.platform.system.service.impl.SysLogininforServiceImpl;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * @Description: 用户名密码登录过滤器
 * @author: tr
 * @date: 2024年08月26日 10:43
 */
public class UserPwdLoginFilter extends AbstractAuthenticationProcessingFilter {

    public CacheClient cacheClient = SpringUtils.getBean(CacheClient.class);

    private CaptchaProperties captchaProperties = SpringUtils.getBean(CaptchaProperties.class);

    private SysLogininforService sysLogininforService = SpringUtils.getBean(SysLogininforServiceImpl.class);

    public UserPwdLoginFilter(AuthenticationManager authenticationManager) {
        super(new AntPathRequestMatcher("/login", "POST"), authenticationManager);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        //获取POST请求中body的入参信息并转换为LoginReq对象
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        BufferedReader reader = req.getReader();
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line).append('\n');
        }
        String body = stringBuilder.toString();
        LoginReq loginReq = JSONUtil.toBean(body, LoginReq.class);
        String username = loginReq.getUsername();
        String password = loginReq.getPassword();
        //唯一uuid
        String uuid = loginReq.getUuid();

        //验证码
        String code = loginReq.getCode();
        //校验验证码是否正确
        checkCaptcha(code, uuid);

        if (StringUtils.isAnyBlank(username, password))
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.USER_PWD_NOT_NULL.getMsg());
            throw new InternalAuthenticationServiceException(ExceptionEnum.USER_PWD_NOT_NULL.getMsg());
        }

        //获取加密盐值
        String securitySalt = cacheClient.getCacheObject(CacheConstants.LOGIN_PASSWORD_SALT + uuid);
        if (captchaProperties.getEnabled()){
            //开启了验证码，才进行加密传输
            password = AESUtils.decrypt(password, securitySalt);
            //解密后的密码，存储到用户认证令牌中，传给AuthenticationManager进行比较密码的正确性。
            loginReq.setPassword(password);
        }

        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.PWD_NOT_SCOPE.getMsg());
            throw new InternalAuthenticationServiceException(ExceptionEnum.PWD_NOT_SCOPE.getMsg());
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.USER_NOT_SCOPE.getMsg());
            throw new InternalAuthenticationServiceException(ExceptionEnum.USER_NOT_SCOPE.getMsg());
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(cacheClient.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, ExceptionEnum.IP_BLACK_LIST.getMsg());
            throw new InternalAuthenticationServiceException(ExceptionEnum.IP_BLACK_LIST.getMsg());
        }

        UserPwdAuthenticationToken userPwdAuthenticationToken = new UserPwdAuthenticationToken(loginReq);
        setDetails(req, userPwdAuthenticationToken);
        return this.getAuthenticationManager().authenticate(userPwdAuthenticationToken);
    }

    protected void setDetails(HttpServletRequest request, UserPwdAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    /**
     * @Description: 检查验证码
     * @author: tr
     * @Date: 2024/2/21 17:02
     * @param: code 验证码
     * @param uuid 唯一uuid
     * @returnValue: void
     */
    private void checkCaptcha(String code, String uuid) throws CaptchaException
    {
        if (!captchaProperties.getEnabled()){
            return ;
        }
        if (StrUtil.isEmpty(code))
        {
            throw new InternalAuthenticationServiceException("验证码不能为空");
        }

        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        String captcha = cacheClient.getCacheObject(verifyKey);
        if (StrUtil.isEmpty(captcha))
        {
            throw new InternalAuthenticationServiceException("验证码已失效");
        }
        cacheClient.deleteObject(verifyKey);

        if (!code.equalsIgnoreCase(captcha))
        {
            throw new InternalAuthenticationServiceException("验证码错误");
        }
    }
}
