package com.platform.system.convert;

import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.domain.SysPost;
import com.platform.system.domain.request.SysPostReq;
import com.platform.system.domain.response.SysPostResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 岗位信息转换接口
 * @author: tr
 * @date: 2024年09月24日 17:33
 */
@Mapper
public interface SysPostConvert {

    SysPostConvert INSTANCE = Mappers.getMapper(SysPostConvert.class);

    SysPost reqToDO(SysPostReq sysPostReq);

    SysPostResp doToResp(SysPost sysPost);

    List<SysPostResp> doListToRespList(List<SysPost> sysPostList);
}
