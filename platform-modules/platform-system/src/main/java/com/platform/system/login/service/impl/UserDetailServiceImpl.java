package com.platform.system.login.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.enums.UserStatusEnum;
import com.platform.common.core.utils.ServletUtils;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.model.LoginUser;
import com.platform.system.api.model.SysOrgDTO;
import com.platform.system.convert.SysUserConvert;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.service.SysLogininforService;
import com.platform.system.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * @Description: 用户信息处理服务
 * @author: tr
 * @date: 2024年08月26日 17:41
 */
@Service
public class UserDetailServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysLogininforService sysLogininforService;

    @Override
    public SysUserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser sysUser = sysUserService.selectUserByUserName(username);
        if (sysUser == null) {
            throw new InternalAuthenticationServiceException(ExceptionEnum.USER_PWD_EROOR.getMsg());
        }
        if (UserStatusEnum.DISABLE.getCode().equals(sysUser.getStatus()))
        {
            String msg = String.format(ExceptionEnum.ACCOUNT_BLOCK_UP.getMsg(), username);
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, msg);
            throw new InternalAuthenticationServiceException(msg);
        }

        LoginUser loginUser = sysUserService.loginUserInfo(username);

        packageUserAgentInfo(loginUser);

        SysUserDetails sysUserDetails = SysUserConvert.INSTANCE.loginUserToSysUserDetails(loginUser);
        sysUserDetails.setPassword(sysUser.getPassword());
        return sysUserDetails;
    }

    private void packageUserAgentInfo(LoginUser userInfo){
        //获取登录地点信息
        String region = IpUtils.getAttributionByIP(userInfo.getIpaddr());
        userInfo.setLoginLocation(region);

        HttpServletRequest request = ServletUtils.getRequest();
        if(null == request) {
            return;
        }
        String userAgentStr = request.getHeader("User-Agent");
        UserAgent userAgent = UserAgentUtil.parse(userAgentStr);
        //获取浏览器
        String browser = userAgent.getBrowser().toString();
        userInfo.setBrowser(browser);

        //获取操作系统
        String os = userAgent.getOs().toString();
        userInfo.setOs(getSimpleOs(os));
    }

    private String getSimpleOs(String os){

        if(StringUtils.isEmpty(os)){
            return "unKnow";
        }else if(os.contains("Windows")){
            return "Windows";
        }else if(os.contains("Mac")){
            return "Mac";
        }else if (os.contains("Ubuntu")){
            return "Ubuntu";
        }else if (os.contains("Android")){
            return "Android";
        }else if (os.contains("iPhone")){
            return "iPhone";
        }else if (os.indexOf("NT 10") > 0){
            return "windows10";
        }else if (os.contains("Linux")){
            return "Linux";
        }else {
            return "unKnow";
        }

    }
}
