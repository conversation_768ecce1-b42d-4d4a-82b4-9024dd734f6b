package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.ToString;

/**
 * 用户和角色关联 sys_user_role
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysUserRole
{
    /** 用户ID */
    private Long userId;
    
    /** 角色ID */
    private Long roleId;

    /** 组织机构ID */
    private Long orgId;

    /** 部门ID */
    private Long deptId;

    /** 用户ID集合 **/
    @TableField(exist = false)
    private Long[] userIds;

}
