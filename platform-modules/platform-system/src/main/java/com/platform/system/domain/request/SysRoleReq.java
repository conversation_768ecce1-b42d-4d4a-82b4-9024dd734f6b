package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 角色新增或修改入参
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysRoleReq
{
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    /** 角色名称 */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 0, max = 30, message = "角色名称长度不能超过30个字符")
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /** 角色权限 */
    @NotBlank(message = "权限字符不能为空")
    @Size(min = 0, max = 100, message = "权限字符长度不能超过100个字符")
    @ApiModelProperty(value = "角色权限")
    private String roleKey;

    /** 角色排序 */
    @NotNull(message = "显示顺序不能为空")
    @ApiModelProperty(value = "角色排序")
    private Integer roleSort;

    /** 角色状态（0正常 1停用） */
    @ApiModelProperty(value = "角色状态（0正常 1停用）")
    private String status;

    @Size(min = 0, max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    @ApiModelProperty(value = "菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）")
    private boolean menuCheckStrictly;

    /** 菜单组 */
    @ApiModelProperty(value = "菜单组")
    private Long[] menuIds;

    /** 角色与菜单关系 */
    @ApiModelProperty(value = "角色与菜单关系")
    private List<SysRoleMenuReq> roleMenuList;

    /** 应用组 **/
    @ApiModelProperty(value = "应用组")
    private Long[] appIds;
}
