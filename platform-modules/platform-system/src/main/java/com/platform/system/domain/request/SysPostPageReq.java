package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import com.ctdi.common.starter.toolbox.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 岗位表 sys_post
 * 
 * <AUTHOR>
 */
@Data
public class SysPostPageReq extends PageReq
{
    private static final long serialVersionUID = 1L;

    /** 岗位编码 */
    @ApiModelProperty(value = "岗位编码")
    @Excel(name = "岗位编码")
    private String postCode;

    /** 岗位名称 */
    @ApiModelProperty(value = "岗位名称")
    @Excel(name = "岗位名称")
    private String postName;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1关闭）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

}
