package com.platform.system.controller;

import java.util.Map;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.toolbox.service.TokenService;
import com.platform.system.api.model.SysUserDTO;
import com.platform.system.convert.SysUserConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.model.LoginUser;
import com.platform.system.service.SysUserService;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 个人信息 业务处理
 * 
 * <AUTHOR>
 */
@Api(tags = "个人信息前端控制器")
@RestController
@RequestMapping("/user/profile")
public class SysProfileController extends BaseController
{
    @Autowired
    private SysUserService userService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 个人信息
     */
    @ApiOperation(value = "获取当前登录用户信息",  notes = "个人信息")
    @GetMapping
    public ResponseResult profile()
    {
        String username = SecurityUtils.getUsername();
        SysUser user = userService.selectUserByUserName(username);
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> userMap = objectMapper.convertValue(user, Map.class);

        userMap.put("roleGroup", userService.selectUserRoleGroup(username));
        userMap.put("postGroup", userService.selectUserPostGroup(username));


        return ResponseResult.ok(userMap);
    }

    /**
     * 修改用户
     */
    @ApiOperation(value = "修改当前登录用户信息",  notes = "个人信息")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult updateProfile(@RequestBody SysUser user)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserDTO sysUserDTO = loginUser.getSysUser();
        sysUserDTO.setNickName(user.getNickName());
        sysUserDTO.setEmail(user.getEmail());
        sysUserDTO.setPhonenumber(user.getPhonenumber());
        sysUserDTO.setSex(user.getSex());

        SysUser currentUser = SysUserConvert.INSTANCE.DTOToDO(sysUserDTO);
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser))
        {
            return ResponseResult.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
        {
            return ResponseResult.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        if (userService.updateUserProfile(currentUser) > 0)
        {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return ResponseResult.ok();
        }
        return ResponseResult.fail("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @ApiOperation(value = "重置密码",  notes = "个人信息")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public ResponseResult updatePwd(String oldPassword, String newPassword)
    {
        String username = SecurityUtils.getUsername();
        SysUser user = userService.selectUserByUserName(username);
        String password = user.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return ResponseResult.fail("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return ResponseResult.fail("新密码不能与旧密码相同");
        }
        if (userService.resetUserPwd(username, SecurityUtils.encryptPassword(newPassword)) > 0)
        {
            // 更新缓存用户密码
            LoginUser loginUser = SecurityUtils.getLoginUser();
            tokenService.setLoginUser(loginUser);
            return ResponseResult.ok();
        }
        return ResponseResult.fail("修改密码异常，请联系管理员");
    }
}
