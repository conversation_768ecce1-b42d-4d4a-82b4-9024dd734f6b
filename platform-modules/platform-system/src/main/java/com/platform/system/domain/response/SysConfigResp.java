package com.platform.system.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.ctdi.common.starter.toolbox.annotation.Excel.ColumnType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参数配置表 sys_config
 * 
 * <AUTHOR>
 */
@Data
public class SysConfigResp
{
    private static final long serialVersionUID = 1L;

    /** 参数ID */
    @ApiModelProperty(value = "参数ID")
    @Excel(name = "参数主键", cellType = ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long configId;

    /** 参数名称 */
    @ApiModelProperty(value = "参数名称")
    @Excel(name = "参数名称")
    private String configName;

    /** 参数键名 */
    @ApiModelProperty(value = "参数键名")
    @Excel(name = "参数键名")
    private String configKey;

    /** 参数键值 */
    @ApiModelProperty(value = "参数键值")
    @Excel(name = "参数键值")
    private String configValue;

    /** 系统内置（Y是 N否） */
    @ApiModelProperty(value = "系统内置（Y是 N否）")
    @Excel(name = "系统内置", readConverterExp = "Y=是,N=否")
    private String configType;
}
