package com.platform.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysPost;
import com.platform.system.domain.request.SysPostPageReq;
import com.platform.system.domain.request.SysPostReq;
import com.platform.system.domain.response.SysPostResp;

/**
 * 岗位信息 服务层
 * 
 * <AUTHOR>
 */
public interface SysPostService extends IService<SysPost>
{
    /**
     * 查询岗位信息集合
     * 
     * @param sysPostPageReq 岗位信息
     * @return 岗位列表
     */
    public List<SysPostResp> selectPostList(SysPostPageReq sysPostPageReq);

    /**
     * 查询所有岗位
     * 
     * @return 岗位列表
     */
    public List<SysPostResp> selectPostAll();

    /**
     * 通过岗位ID查询岗位信息
     * 
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    public SysPostResp selectPostById(Long postId);

    /**
     * 校验岗位名称
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    public boolean checkPostNameUnique(SysPostReq sysPostReq);

    /**
     * 校验岗位编码
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    public boolean checkPostCodeUnique(SysPostReq sysPostReq);

    /**
     * 通过岗位ID查询岗位使用数量
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    public int countUserPostById(Long postId);

    /**
     * 批量删除岗位信息
     * 
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    public int deletePostByIds(Long[] postIds);

    /**
     * 新增保存岗位信息
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    public int insertPost(SysPostReq sysPostReq);

    /**
     * 修改保存岗位信息
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    public int updatePost(SysPostReq sysPostReq);
}
