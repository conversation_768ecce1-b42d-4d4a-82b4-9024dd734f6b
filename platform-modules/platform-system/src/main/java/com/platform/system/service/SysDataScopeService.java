package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.request.SysDataScopeReq;
import com.platform.system.domain.SysDataScope;

import java.util.List;

/**
 * @Description: 数据权限Service接口
 * @author: tr
 * @date: 2024年06月24日 19:32
 */
public interface SysDataScopeService extends IService<SysDataScope> {

    /**
     * @Description: 新增数据权限范围
     * @author: tr
     * @Date: 2024/6/25 8:57
     * @param: [sysDataScopeReq]
     * @returnValue: void
     */
    void saveBatch(List<SysDataScopeReq> sysDataScopeReqList);

    /**
     * @Description: 根据数据ID删除数据权限
     * @author: tr
     * @Date: 2024/6/25 8:55
     * @param dataId 数据ID
     * @returnValue: void
     */
    void deleteByDataId(Long[] dataIds, String dataType);
}
