package com.platform.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.platform.system.api.model.SysApiDTO;
import com.platform.system.domain.SysApi;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: API权限配置Mapper
 * @author: tr
 * @date: 2024年02月29日 16:10
 */
@Mapper
public interface SysApiMapper extends BaseMapper<SysApi> {

    /**
     * @Description: 根据角色ID查询角色对应的权限字符
     * @author: tr
     * @Date: 2024/3/6 16:12
     * @param: roleIdList 角色ID集合
     * @returnValue: java.util.List<java.lang.String>
     */
    List<SysApiDTO> selectApiPermsByRoleId(List<Long> roleIdList);
}
