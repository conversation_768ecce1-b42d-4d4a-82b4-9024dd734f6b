package com.platform.system.utils;

/**
 * @Description: 加密解密工具类
 * @author: tr
 * @date: 2024年04月09日 14:43
 */
public class EncrypDecrypUtils {

    /**
     * @Description: 加密
     * @author: tr
     * @Date: 2024/4/9 14:44
     * @param: [content, key]
     * @returnValue: java.lang.String
     */
    public static String encrypt(String content, String key) {
        return Sm4Utils.encryptSm4(content, key);
    }
}
