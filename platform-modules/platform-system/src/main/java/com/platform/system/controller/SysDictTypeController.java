package com.platform.system.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.system.api.domain.request.SysDictTypePageReq;
import com.platform.system.api.domain.response.SysDictTypeResp;
import com.platform.system.convert.SysDictTypeConvert;
import com.platform.system.domain.request.SysDictTypeReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysDictType;
import com.platform.system.service.SysDictTypeService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@Api(tags = "字典类型前端控制器")
@RestController
@RequestMapping("/dict/type")
public class SysDictTypeController extends BaseController
{
    @Autowired
    private SysDictTypeService dictTypeService;

    @ApiOperation(value = "获取字典类型列表",  notes = "字典类型")
    @RequiresPermissions("system:dict:list")
    @GetMapping("/list")
    public ResponseResult<PageResult<SysDictTypeResp>> page(SysDictTypePageReq sysDictTypePageReq)
    {
        Page<SysDictTypeResp> page = PageUtils.startPage();
        List<SysDictTypeResp> list = dictTypeService.selectDictTypeList(sysDictTypePageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "获取字典类型列表",  notes = "字典类型")
    @RequiresPermissions("system:dict:list")
    @GetMapping("/listAll")
    public ResponseResult<List<SysDictTypeResp>> list(SysDictTypePageReq sysDictTypePageReq)
    {
        List<SysDictTypeResp> list = dictTypeService.selectDictTypeListAll(sysDictTypePageReq);
        return ResponseResult.ok(list);
    }

    @ApiOperation(value = "导出字典类型",  notes = "字典类型")
    @Log(title = "字典类型", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:dict:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictTypePageReq sysDictTypePageReq)
    {
        List<SysDictTypeResp> list = dictTypeService.selectDictTypeList(sysDictTypePageReq);
        ExcelUtil<SysDictTypeResp> util = new ExcelUtil<>(SysDictTypeResp.class);
        util.exportExcel(response, list, "字典类型");
    }

    /**
     * 查询字典类型详细
     */
    @ApiOperation(value = "查询字典类型详细",  notes = "字典类型")
    @RequiresPermissions("system:dict:query")
    @GetMapping("/dictId")
    public ResponseResult<SysDictTypeResp> getInfo(Long dictId)
    {
        return ResponseResult.ok(dictTypeService.selectDictTypeById(dictId));
    }

    /**
     * 新增字典类型
     */
    @ApiOperation(value = "新增字典类型",  notes = "字典类型")
    @RequiresPermissions("system:dict:add")
    @Log(title = "字典类型", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult add(@Validated @RequestBody SysDictTypeReq sysDictTypeReq)
    {
        SysDictType dict = SysDictTypeConvert.INSTANCE.reqToDO(sysDictTypeReq);
        if (!dictTypeService.checkDictTypeUnique(dict))
        {
            return ResponseResult.fail("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
        }
        dict.setCreateBy(SecurityUtils.getUsername());
        return dictTypeService.insertDictType(dict)>0?ResponseResult.ok():ResponseResult.fail("新增字典失败");
    }

    /**
     * 修改字典类型
     */
    @ApiOperation(value = "修改字典类型",  notes = "字典类型")
    @RequiresPermissions("system:dict:edit")
    @Log(title = "字典类型", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public ResponseResult edit(@Validated @RequestBody SysDictTypeReq sysDictTypeReq)
    {
        SysDictType dict = SysDictTypeConvert.INSTANCE.reqToDO(sysDictTypeReq);
        if (!dictTypeService.checkDictTypeUnique(dict))
        {
            return ResponseResult.fail("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
        }
        dict.setUpdateBy(SecurityUtils.getUsername());
        return dictTypeService.updateDictType(dict)>0?ResponseResult.ok():ResponseResult.fail("修改字典失败");
    }

    /**
     * 删除字典类型
     */
    @ApiOperation(value = "删除字典类型",  notes = "字典类型")
    @RequiresPermissions("system:dict:remove")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam("dictIds") Long[] dictIds)
    {
        dictTypeService.deleteDictTypeByIds(dictIds);
        return ResponseResult.ok();
    }

    /**
     * 刷新字典缓存
     */
    @ApiOperation(value = "刷新字典缓存",  notes = "字典类型")
    @RequiresPermissions("system:dict:remove")
    @Log(title = "字典类型", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public ResponseResult refreshCache()
    {
        dictTypeService.resetDictCache();
        return ResponseResult.ok();
    }

    /**
     * 获取字典选择框列表
     */
    @ApiOperation(value = "获取字典选择框列表",  notes = "字典类型")
    @GetMapping("/optionselect")
    public ResponseResult<List<SysDictTypeResp>> optionselect()
    {
        List<SysDictTypeResp> dictTypes = dictTypeService.selectDictTypeAll();
        return ResponseResult.ok(dictTypes);
    }

    @ApiOperation(value = "导入字典类型信息",  notes = "字典类型")
    @Log(title = "字典类型", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public ResponseResult<String> importData(MultipartFile file) throws IOException {
        ExcelUtil<SysDictTypeReq> util = new ExcelUtil<>(SysDictTypeReq.class);
        List<SysDictTypeReq> list = util.importExcel(file.getInputStream());
        String msg = dictTypeService.batchSaveDictType(list);
        return ResponseResult.ok(msg);
    }
}
