package com.platform.system.service;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import com.platform.system.api.domain.request.SysUserRoleReq;
import com.platform.system.api.domain.response.SysOrgRoleResp;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.domain.request.SysRoleReq;

/**
 * 角色业务层
 * 
 * <AUTHOR>
 */
public interface SysRoleService extends IService<SysRole>
{
    /**
     * 根据条件分页查询角色数据
     * 
     * @param sysRoleQueryReq 角色信息
     * @return 角色数据集合信息
     */
    public List<SysRoleResp> selectRoleList(SysRoleQueryReq sysRoleQueryReq);

    /**
     * 根据用户ID查询角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<SysRoleResp> selectRolesByUserId(Long userId);

    /**
     * @Description: 根据用户ID查询角色的集合对象
     * @author: tr
     * @Date: 2024/2/20 18:27
     * @param: [userId]
     * @returnValue: java.util.List<com.platform.system.api.domain.SysRole>
     */
    List<SysRole> selectRoleListToRoleByUserId(Long userId);

    /**
     * 根据用户ID查询角色权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectRolePermissionByUserId(Long userId);

    /**
     * 查询所有角色
     * 
     * @return 角色列表
     */
    public List<SysRoleResp> selectRoleAll();

    /**
     * 通过角色ID查询角色
     * 
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    public SysRoleResp selectRoleById(Long roleId);

    /**
     * 校验角色名称是否唯一
     * 
     * @param role 角色信息
     * @return 结果
     */
    public boolean checkRoleNameUnique(SysRole role);

    /**
     * 校验角色权限是否唯一
     * 
     * @param role 角色信息
     * @return 结果
     */
    public boolean checkRoleKeyUnique(SysRole role);

    /**
     * 校验角色是否有数据权限
     * 
     * @param roleId 角色id
     */
    public void checkRoleDataScope(Long roleId);

    /**
     * 通过角色ID查询角色使用数量
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    public int countUserRoleByRoleId(Long roleId);

    /**
     * 新增保存角色信息
     * 
     * @param sysRoleReq 角色信息
     * @return 结果
     */
    public int insertRole(SysRoleReq sysRoleReq);

    /**
     * 修改保存角色信息
     * 
     * @param sysRoleReq 角色信息
     * @return 结果
     */
    public int updateRole(SysRoleReq sysRoleReq);

    /**
     * 修改角色状态
     * 
     * @param role 角色信息
     * @return 结果
     */
    public int updateRoleStatus(SysRole role);

    /**
     * 修改数据权限信息
     * 
     * @param role 角色信息
     * @return 结果
     */
    public int authDataScope(SysRole role);

    /**
     * 批量删除角色信息
     * 
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    public int deleteRoleByIds(Long[] roleIds);

    /**
     * 取消授权用户角色
     * 
     * @param sysUserRoleReq 用户和角色关联信息
     * @return 结果
     */
    public int deleteAuthUser(SysUserRoleReq sysUserRoleReq);

    /**
     * 批量取消授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    public int deleteAuthUsers(Long roleId, Long[] userIds);

    /**
     * 批量选择授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
    public int insertAuthUsers(Long roleId, Long[] userIds);

    /**
     * @Description: 我还没有写描述
     * @author: tr
     * @Date: 2024/3/22 10:48
     * @param: [orgIds]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysRoleResp>
     */
    List<SysRoleResp> listRoleByOrgIds(Long[] orgIds);

    /**
     * @Description: 根据角色编码查询角色信息
     * @author: tr
     * @Date: 2024/5/21 15:52
     * @param: [code]
     * @returnValue: com.platform.system.api.domain.SysRole
     */
    SysRole getByCode(String code);

    /**
     * @Description: 查询当前用户的机构以及被赋予的角色信息
     * @author: tr
     * @Date: 2024/5/21 15:52
     * @returnValue: com.platform.system.api.domain.response.SysOrgRoleResp
     */
    List<SysOrgRoleResp> listOrgRoles();
}
