package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysApp;
import com.platform.system.domain.request.SysAppPageReq;
import com.platform.system.domain.request.SysAppReq;
import com.platform.system.domain.response.SysAppResp;

import java.util.List;
import java.util.Map;

/**
 * @Description: 应用管理Service接口层
 * @author: tr
 * @date: 2024年03月27日 14:09
 */
public interface SysAppService extends IService<SysApp> {

    /**
     * @Description: 查询应用信息，公钥和私钥不能直接返回
     * @author: tr
     * @Date: 2024/3/27 14:33
     * @param: [sysAppPageReq]
     * @returnValue: com.platform.system.domain.response.SysAppResp
     */
    List<SysAppResp> list(SysAppPageReq sysAppPageReq);

    /**
     * @Description: 根据ID查询单个应用
     * @author: tr
     * @Date: 2024/3/29 9:32
     * @param: [id]
     * @returnValue: com.platform.system.domain.response.SysAppResp
     */
    SysAppResp getById(Long id);

    /**
     * @Description: 新增应用信息，自动创建公钥和私钥
     * @author: tr
     * @Date: 2024/3/27 14:30
     * @param: [sysAppReq]
     * @returnValue: void
     */
    void save(SysAppReq sysAppReq);

    /**
     * @Description: 修改应用信息，公钥和私钥不允许修改
     * @author: tr
     * @Date: 2024/3/27 14:32
     * @param: [sysAppReq]
     * @returnValue: void
     */
    void update(SysAppReq sysAppReq);

    /**
     * @Description: 根据ID逻辑删除应用
     * @author: tr
     * @Date: 2024/3/27 14:32
     * @param id 应用ID
     * @returnValue: void
     */
    void delete(Long id);

    /**
     * @Description: 查询用户拥有的应用权限
     * @author: tr
     * @Date: 2024/3/29 18:06
     * @param: []
     * @returnValue: java.util.List<com.platform.system.domain.response.SysAppResp>
     */
    List<SysAppResp> listUserApp(String type);

    void addAppVisit(Long appId);

    Map<String,Object> getAppVisit();
}
