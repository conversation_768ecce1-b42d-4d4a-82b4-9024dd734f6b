package com.platform.system.convert;

import com.platform.system.api.domain.request.SysDataScopeReq;
import com.platform.system.domain.SysDataScope;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Description: 数据权限转换类接口
 * @author: tr
 * @date: 2024年06月25日 9:03
 */
@Mapper
public interface SysDataScopeConvert {

    SysDataScopeConvert INSTANCE = Mappers.getMapper(SysDataScopeConvert.class);

    SysDataScope reqToDO(SysDataScopeReq sysDataScopeReq);
}
