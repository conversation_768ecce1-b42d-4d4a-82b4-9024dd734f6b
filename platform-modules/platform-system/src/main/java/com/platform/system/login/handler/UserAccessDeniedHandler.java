package com.platform.system.login.handler;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.utils.ResponseUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * @Description: 用户无权限访问处理类
 * @author: tr
 * @date: 2024年08月26日 16:04
 */
@Component
public class UserAccessDeniedHandler implements AccessDeniedHandler, Serializable {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) {
        ResponseUtils.responseJson(response, ResponseResult.fail("无权限访问！"));
    }
}
