package com.platform.system.login.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.CaptchaException;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.text.Convert;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.system.login.config.mobilephone.MobilePhoneAuthenticationToken;
import com.platform.system.login.domain.req.MobilePhoneReq;
import com.platform.system.service.SysLogininforService;
import com.platform.system.service.impl.SysLogininforServiceImpl;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * @Description: 手机号码登录过滤器
 * @author: tr
 * @date: 2025年02月14日 08:43
 */
public class MobilePhoneLoginFilter extends AbstractAuthenticationProcessingFilter {

    public CacheClient cacheClient = SpringUtils.getBean(CacheClient.class);

    private SysLogininforService sysLogininforService = SpringUtils.getBean(SysLogininforServiceImpl.class);

    public MobilePhoneLoginFilter(AuthenticationManager authenticationManager) {
        super(new AntPathRequestMatcher("/mobilephone/login", "POST"), authenticationManager);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        //获取POST请求中body的入参信息并转换为LoginReq对象
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        BufferedReader reader = req.getReader();
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line).append('\n');
        }
        String body = stringBuilder.toString();
        MobilePhoneReq mobilePhoneReq = JSONUtil.toBean(body, MobilePhoneReq.class);
        String phonenumber = mobilePhoneReq.getPhonenumber();
        String validateCode = mobilePhoneReq.getValidateCode();
        String uuid = mobilePhoneReq.getUuid();

        // IP黑名单校验
        String blackStr = Convert.toStr(cacheClient.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            sysLogininforService.saveLogininfor(phonenumber, Constants.LOGIN_FAIL, ExceptionEnum.IP_BLACK_LIST.getMsg());
            throw new ServiceException(ExceptionEnum.IP_BLACK_LIST.getMsg());
        }

        //校验验证码是否正确
        checkCaptcha(phonenumber, validateCode, uuid);

        MobilePhoneAuthenticationToken mobilePhoneAuthenticationToken = new MobilePhoneAuthenticationToken(mobilePhoneReq);
        setDetails(req, mobilePhoneAuthenticationToken);
        return this.getAuthenticationManager().authenticate(mobilePhoneAuthenticationToken);
    }

    protected void setDetails(HttpServletRequest request, MobilePhoneAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    /**
     * @Description: 检查验证码
     * @author: tr
     * @Date: 2024/2/21 17:02
     * @param: code 验证码
     * @param uuid 唯一uuid
     * @returnValue: void
     */
    private void checkCaptcha(String phonenumber, String code, String uuid) throws CaptchaException
    {
        if (StrUtil.isEmpty(code))
        {
            throw new ServiceException("验证码不能为空");
        }

        String verifySmsKey = CacheConstants.SMS_CODE_KEY+ "2" + ":" + phonenumber + ":" + uuid;
        String captcha = cacheClient.getCacheObject(verifySmsKey);
        if (StrUtil.isEmpty(captcha))
        {
            throw new ServiceException("验证码已失效");
        }
        cacheClient.deleteObject(verifySmsKey);

        if (!StrUtil.equalsIgnoreCase(code, captcha))
        {
            throw new ServiceException("验证码错误");
        }
    }
}
