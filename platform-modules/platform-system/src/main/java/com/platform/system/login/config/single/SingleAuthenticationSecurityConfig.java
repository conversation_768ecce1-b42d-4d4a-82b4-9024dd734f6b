package com.platform.system.login.config.single;

import com.platform.system.login.filter.SingleLoginFilter;
import com.platform.system.login.provider.SingleAuthenticationProvider;
import com.platform.system.login.service.impl.SingleUserDetailServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * @Description: 手机号码认证配置，用于添加MobilePhoneLoginFilter过滤器，以及添加认证成功和认证失败的处理器
 * @author: tr
 * @date: 2024年08月26日 15:22
 */
@Component
public class SingleAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    @Autowired
    private AuthenticationSuccessHandler authenticationSuccessHandler;
    @Autowired
    private AuthenticationFailureHandler authenticationFailureHandler;

    @Autowired
    private SingleUserDetailServiceImpl singleUserDetailService;

    @Override
    public void configure(HttpSecurity builder) {
        AuthenticationManager localAuthManager = builder.getSharedObject(AuthenticationManager.class);

        SingleLoginFilter singleLoginFilter = new SingleLoginFilter(localAuthManager);
        singleLoginFilter.setAuthenticationFailureHandler(authenticationFailureHandler);
        singleLoginFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
        //设置自定义UserPwdAuthenticationProvider的认证器userDetailsService
        SingleAuthenticationProvider singleAuthenticationProvider = new SingleAuthenticationProvider();
        singleAuthenticationProvider.setUserDetailsService(singleUserDetailService);
        //在UsernamePasswordAuthenticationFilter过滤前执行
        builder.authenticationProvider(singleAuthenticationProvider)
                .addFilterAfter(singleLoginFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
