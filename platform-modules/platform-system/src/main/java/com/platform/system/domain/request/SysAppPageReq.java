package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("应用配置新增或修改参数")
@Data
@ToString
public class SysAppPageReq extends PageReq {

	private static final long serialVersionUID = 1L;

	/**
	 * 应用名称
	 */
	@ApiModelProperty("应用名称")
	private String name;

	/**
	 * 应用编码
	 */
	@ApiModelProperty("应用编码")
	private String code;

	/**
	 * 类型（1-PC端，2-APP端）
	 */
	@ApiModelProperty("类型（1-PC端，2-APP端）")
	private String type;

	/**
	 * 状态（0正常 1停用）
	 */
	@ApiModelProperty("状态（0正常 1停用）")
	private String status;

	/**
	 * 外链标识，0-否，1-是
	 */
	@ApiModelProperty("外链标识，0-否，1-是")
	private Integer frameFlag;

	/**
	 * 业务归属
	 */
	@ApiModelProperty("业务归属以字典为准")
	private String bizSense;
}
