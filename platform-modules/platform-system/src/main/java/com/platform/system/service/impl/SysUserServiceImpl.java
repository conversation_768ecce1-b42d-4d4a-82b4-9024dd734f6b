package com.platform.system.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Validator;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.security.GeneralAlgorithmUtil;
import com.ctdi.common.starter.toolbox.utils.DateUtils;
import com.ctdi.common.starter.toolbox.uuid.IdUtils;
import com.platform.common.core.config.properties.CoreProperties;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.core.enums.FlagEnum;
import com.platform.common.core.enums.SyncTypeEnum;
import com.platform.common.core.enums.UserTypeEnum;
import com.platform.common.core.utils.AESUtils;
import com.platform.common.core.utils.bean.BeanValidators;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.domain.SysDept;
import com.platform.system.api.domain.request.*;
import com.platform.system.api.domain.response.*;
import com.platform.system.api.model.*;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.convert.*;
import com.platform.system.domain.*;
import com.platform.system.domain.request.*;
import com.platform.system.domain.response.CertificateNoValidateResp;
import com.platform.system.mapper.*;
import com.platform.system.openapi.domain.response.UserResp;
import com.platform.system.service.*;
import com.platform.system.utils.ChineseToPinyinUtils;
import com.platform.system.utils.RandomPasswordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.datascope.annotation.DataScope;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.SysUser;

/**
 * 用户 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    private static final String QUICK_USER_ROLE_KEY = "quick_user";

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private SysConfigService configService;

    @Autowired
    protected Validator validator;

    @Autowired
    private SysRoleDeptMapper sysRoleDeptMapper;

    @Autowired
    private SysUserOrgService userOrgService;

    @Autowired
    private SysDeptService deptService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysOrgService orgService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysUserExtendService sysUserExtendService;

    @Autowired
    private SysCountryAddressService sysCountryAddressService;

    @Autowired
    private CacheClient cacheClient;

    /** 短信验证码修改密码的类型 **/
    private final Integer UPDATE_TYPE = 3;
    @Autowired
    private SysUserOrgMapper sysUserOrgMapper;

    @Autowired
    private SysSyncdataLogService sysSyncdataLogService;

    @Autowired
    private CoreProperties coreProperties;

    /**
     * 根据条件分页查询用户列表
     * 
     * @param sysUserPageReq 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(joinTableName = "sys_user_org", permissionField = "u.user_id", joinSelectField = "user_id")
    public List<SysUserResp> selectUserList(SysUserPageReq sysUserPageReq)
    {
        if (StrUtil.isNotBlank(sysUserPageReq.getBeginTime())){
            sysUserPageReq.setBeginTimeDate(DateUtils.parseDate(sysUserPageReq.getBeginTime() + " 00:00:00"));
        }
        if (StrUtil.isNotBlank(sysUserPageReq.getEndTime())){
            sysUserPageReq.setEndTimeDate(DateUtils.parseDate(sysUserPageReq.getEndTime() + " 23:59:59"));
        }
        sysUserPageReq.setDelFlag(DelFlagEnum.NO_DELETED.getCode());
        List<SysUserResp> userList = userMapper.selectUserList(sysUserPageReq);
        return userList;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(joinTableName = "sys_user_org", permissionField = "u.user_id", joinSelectField = "user_id")
    public List<SysUserResp> selectAllocatedList(SysUserPageReq user)
    {
        List<SysUser> sysUserList = userMapper.selectAllocatedList(user);
        List<SysUserResp> list = SysUserConvert.INSTANCE.doListToRespList(sysUserList);
        return list;
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(joinTableName = "sys_user_org", permissionField = "u.user_id", joinSelectField = "user_id")
    public List<SysUserResp> selectUnallocatedList(SysUserPageReq sysUserPageReq)
    {
        List<SysUser> list = userMapper.selectUnallocatedList(sysUserPageReq);
        List<SysUserResp> sysUserRespList = SysUserConvert.INSTANCE.doListToRespList(list);
        return sysUserRespList;
    }

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName)
    {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUserResp selectUserById(Long userId)
    {
        SysUser sysUser = userMapper.selectUserById(userId);
        sysUser.setPassword(null);

        SysUserResp sysUserResp = SysUserConvert.INSTANCE.doToResp(sysUser);

        //是否切换角色机构等信息
        String isSwitchOrgRole = configService.selectConfigByKey("sys.user.switchOrgRole");
        Boolean flag = "true".equals(isSwitchOrgRole)  && !SecurityUtils.isAdmin(sysUser.getUserType());
        sysUserResp.setSwitchOrgRole(flag);

        if (flag){
            sysUserResp.setRoles(sysUserResp.getRoles().stream().filter(sysRole -> sysRole.getRoleKey()
                    .equals(sysUser.getLastLoginRole())).collect(Collectors.toList()));
        }

        if (ObjectUtil.isNotEmpty(sysUserResp.getLastLoginOrgId()) && sysUserResp.getLastLoginOrgId() > 0L){
            SysOrgResp sysOrg = orgService.getById(sysUserResp.getLastLoginOrgId());
            if (ObjectUtil.isNotEmpty(sysOrg)){
                sysUserResp.setLastLoginOrgName(sysOrg.getName());
            }
        }
        //用户扩展信息
        SysUserExtend sysUserExtend = sysUserExtendService.getById(sysUserResp.getUserId());
        SysUserExtendResp sysUserExtendResp = SysUserExtendConvert.INSTANCE.doToResp(sysUserExtend);
        sysUserResp.setSysUserExtendResp(sysUserExtendResp);
        return sysUserResp;
    }

    /**
     * 查询用户所属角色组
     * 
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName)
    {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list))
        {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     * 
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName)
    {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list))
        {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     * 
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && SecurityUtils.isAdmin(user.getUserType()))
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     * 
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId)
    {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserType()))
        {
            SysUserPageReq user = new SysUserPageReq();
            user.setUserId(userId);
            List<SysUserResp> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users))
            {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     * 
     * @param sysUserReq 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUser(SysUserReq sysUserReq)
    {
        sysUserReq.setPassword(AESUtils.decrypt(sysUserReq.getPassword(), this.coreProperties.getPwdEncrypKey()));
        SysUser user = SysUserConvert.INSTANCE.reqToDO(sysUserReq);

        check(user);

        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        // 新增用户信息
        user.setUserId(IdWorker.getId(user));
        if(ObjectUtil.isEmpty(user.getUserType())){
            user.setUserType(UserTypeEnum.PERSONAL_USER.getCode());
        }else if(UserTypeEnum.SYSTEM_USER.getCode().equals(user.getUserType())){
            String userType = SecurityUtils.getUserType();
            if (!UserTypeEnum.SYSTEM_USER.getCode().equals(userType)){
                throw new ServiceException("非超管用户无法新增超管用户类型");
            }
        }
        save(user);
        sysUserReq.setUserId(user.getUserId());
        // 新增用户与角色管理
        insertUserRole(sysUserReq);
        //新增用户与组织机构信息
        sysUserReq.setUserId(user.getUserId());
        insertUserOrg(sysUserReq);

        //新增扩展属性字段
        SysUserExtendReq sysUserExtendReq = sysUserReq.getSysUserExtendReq();
        if (ObjectUtil.isNotEmpty(sysUserExtendReq)){
            SysUserExtend sysUserExtend = SysUserExtendConvert.INSTANCE.reqToDO(sysUserExtendReq);
            sysUserExtend.setId(user.getUserId());
            sysUserExtendService.save(sysUserExtend);
        }
        SysSyncdataLog log = new SysSyncdataLog();
        log.setDataId(StrUtil.toString(user.getUserId()));
        log.setSyncType(SyncTypeEnum.USER.getCode());
        sysSyncdataLogService.saveLog(log);
    }

    /**
     * 注册用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user)
    {
        boolean flag = save(user);
        return flag;
    }

    /**
     * 修改保存用户信息
     * 
     * @param sysUserReq 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(SysUserReq sysUserReq)
    {
        SysUser user = SysUserConvert.INSTANCE.reqToDO(sysUserReq);

        this.checkUserAllowed(user);
        this.checkUserDataScope(user.getUserId());
        check(user);

        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(sysUserReq);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        //删除用户与组织机构关联
        userOrgService.deleteByUserId(userId);
        insertUserOrg(sysUserReq);
        updateById(user);

        //修改扩展属性字段
        SysUserExtendReq sysUserExtendReq = sysUserReq.getSysUserExtendReq();
        if (ObjectUtil.isNotEmpty(sysUserExtendReq)){
            SysUserExtend sysUserExtend = SysUserExtendConvert.INSTANCE.reqToDO(sysUserExtendReq);
            sysUserExtend.setId(user.getUserId());
            sysUserExtendService.saveOrUpdate(sysUserExtend);
        }

        SysSyncdataLog log = new SysSyncdataLog();
        log.setDataId(StrUtil.toString(user.getUserId()));
        log.setSyncType(SyncTypeEnum.USER.getCode());
        sysSyncdataLogService.saveLog(log);
    }

    /**
     * 用户授权角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds)
    {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user)
    {
        SysUser sysUser = new SysUser();
        sysUser.setStatus(user.getStatus());
        sysUser.setUserId(user.getUserId());
        updateById(sysUser);
        return 1;
    }

    /**
     * 修改用户基本信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user)
    {
        updateById(user);
        return 1;
    }

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user)
    {
        updateById(user);
        return 1;
    }

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password)
    {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     * 
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user)
    {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     * 
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user)
    {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts)
            {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds)
    {
        if (StringUtils.isNotEmpty(roleIds))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId)
    {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds)
    {
        for (Long userId : userIds)
        {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        int index = userMapper.deleteUserByIds(userIds);

        List<SysSyncdataLog> logList = new ArrayList<>();
        for (Long userId : userIds)
        {
            SysSyncdataLog log = new SysSyncdataLog();
            log.setDataId(StrUtil.toString(userId));
            log.setSyncType(SyncTypeEnum.USER.getCode());
            logList.add(log);
        }
        sysSyncdataLogService.saveLogBatch(logList);
        return index;
    }

    /**
     * 导入用户数据
     * 
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList)
        {
            try
            {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u))
                {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    save(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operName);
                    updateById(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    /**
     * 根据用户ID查询该用户拥有的部门权限
     * @param userId
     * @return
     */
    @Override
    public Set<Long> getPermissionDeptIdsByUserId(Long userId){

        SysUser sysUser = userMapper.selectUserById(userId);
        if(null == sysUser){
            throw new ServiceException("指定用户不存在");
        }

        Set<Long> deptSet = new HashSet<>();

        List<SysRole> sysRoles = roleMapper.selectRolePermissionByUserId(userId);

        //先处理数据权限为所有的
        Optional<SysRole> any = sysRoles.stream().filter(m -> BusinessConstants.ROLE_DATA_SCOPE_ALL.equals(m.getDataScope())).findAny();
        if(any.isPresent()){
            //返回所有部门集合
            List<SysDeptResp> sysDepts = deptService.selectDeptList(new SysDeptQueryReq());
            deptSet.addAll(sysDepts.stream().map(SysDeptResp::getDeptId).collect(Collectors.toSet()));
            return deptSet;
        }

        //处理数据权限为本部门及以下的
        Optional<SysRole> deptAndSub = sysRoles.stream().filter(m -> BusinessConstants.ROLE_DATA_SCOPE_DEPT_AND_SUB.equals(m.getDataScope())).findAny();
        if(deptAndSub.isPresent()){
            List<SysDept> sysDepts = deptService.selectDeptAndChildDept(sysUser.getDeptId());
            deptSet.addAll(sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet()));
        }

        //处理数据权限为本部门的--只有数据为空的时候才处理
        if(deptSet.size() == 0 ){
            deptSet.add(sysUser.getDeptId());
        }

        //处理自定义
        sysRoles.stream().filter(m -> BusinessConstants.ROLE_DATA_SCOPE_CUSTOM.equals(m.getDataScope())).forEach(d ->{

            Long roleId = d.getRoleId();
            List<SysRoleDept> sysRoleDepts = sysRoleDeptMapper.selectByRoleId(roleId);
            deptSet.addAll(sysRoleDepts.stream().map(SysRoleDept::getDeptId).collect(Collectors.toSet()));
        });

        return deptSet;
    }

    @Override
    public LoginUser loginUserInfo(String username) {
        SysUser sysUser = this.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser))
        {
            throw new ServiceException("用户名或密码错误");
        }

        //是否切换角色机构等信息
        String isSwitchOrgRole = configService.selectConfigByKey("sys.user.switchOrgRole");
        Boolean flag = "true".equals(isSwitchOrgRole)  && !SecurityUtils.isAdmin(sysUser.getUserType());
        LoginUser loginUser = new LoginUser();
        if(flag && ObjectUtil.isEmpty(sysUser.getLastLoginOrgId())){
            //查询用户的组织机构信息
            List<SysOrgResp> sysOrgList = orgService.listByUserId(sysUser.getUserId());
            sysUser.setLastLoginOrgId(sysOrgList.get(0).getId());
            List<SysRoleResp> role = roleService.listRoleByOrgIds(Collections.singletonList(sysUser.getLastLoginOrgId()).toArray(new Long[0]));
            sysUser.setLastLoginRole(role.get(0).getRoleKey());
            updateById(sysUser);
        }

        SysUserDTO sysUserDTO = SysUserConvert.INSTANCE.doToDTO(sysUser);
        //部门信息
        SysDeptResp sysDeptResp = deptService.selectDeptById(sysUser.getDeptId());
        SysDeptDTO sysDeptDTO = SysDeptConvert.INSTANCE.respToDTO(sysDeptResp);
        sysUserDTO.setDeptDTO(sysDeptDTO);

        //角色信息
        List<SysRole> sysRoleList = roleService.selectRoleListToRoleByUserId(sysUser.getUserId());
        List<SysRoleDTO> sysRoleDTOList = SysRoleConvert.INSTANCE.doToDTOList(sysRoleList);
        sysUserDTO.setRolesDTO(sysRoleDTOList);
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser);
        // 权限集合
        List<SysApiDTO> permissions = permissionService.getMenuPermission(sysUser);
        //查询用户的组织机构信息
        List<SysOrgResp> sysOrgList = orgService.listByUserId(sysUserDTO.getUserId());
        if (flag){
            sysOrgList = sysOrgList.stream().filter(sysOrgResp -> sysOrgResp.getId().equals(sysUser.getLastLoginOrgId())).collect(Collectors.toList());
        }
        List<SysOrgDTO> sysOrgDTOList = BeanUtil.copyToList(sysOrgList, SysOrgDTO.class);
        loginUser.setSysUser(sysUserDTO);
        loginUser.setRoles(roles);
        loginUser.setPermissions(permissions);
        loginUser.setOrgs(sysOrgDTOList);
        //用户扩展信息
        SysUserExtend sysUserExtend = sysUserExtendService.getById(sysUser.getUserId());
        SysUserExtendDTO sysUserExtendDTO = SysUserExtendConvert.INSTANCE.doToDTO(sysUserExtend);
        loginUser.setSysUserExtendDTO(sysUserExtendDTO);

        return loginUser;
    }

    @Override
    public SysUserResp getUserById(Long id) {
        SysUserResp sysUserResp = this.selectUserById(id);
        //查询用户与组织机构的关系
        Long[] orgIds = userOrgService.listByUserId(sysUserResp.getUserId());
        sysUserResp.setOrgIds(orgIds);
        //查询用户的机构与角色关系
        List<SysUserRole> sysUserRoleList = sysUserRoleService.listByUserId(id);
        Map<String, List<SysUserRole>> roleMenuMap = sysUserRoleList.stream().collect(Collectors.groupingBy(s -> {
            String key = s.getOrgId() + "|" + s.getDeptId();
            return key;}));
        List<SysUserRoleResp> sysUserRoleRespList = new ArrayList<>();
        roleMenuMap.forEach((key, value) -> {
            String orgId = key.split("\\|")[0];
            String deptId = key.split("\\|")[1];
            SysUserRoleResp sysUserRoleResp = new SysUserRoleResp();
            if (ObjectUtil.isNotEmpty(orgId) && !"null".equals(orgId)){
                sysUserRoleResp.setOrgId(Long.parseLong(orgId));
            }
            sysUserRoleResp.setRoleIds(value.stream().map(SysUserRole::getRoleId)
                    .collect(Collectors.toList()).toArray(new Long[]{}));
            if (ObjectUtil.isNotEmpty(deptId) && !"null".equals(deptId)){
                sysUserRoleResp.setDeptId(Long.parseLong(deptId));
            }
            sysUserRoleRespList.add(sysUserRoleResp);
        });
        sysUserResp.setUserRoleList(sysUserRoleRespList);

        //用户扩展信息
        SysUserExtend sysUserExtend = sysUserExtendService.getById(sysUserResp.getUserId());
        SysUserExtendResp sysUserExtendResp = SysUserExtendConvert.INSTANCE.doToResp(sysUserExtend);
        sysUserResp.setSysUserExtendResp(sysUserExtendResp);
        return sysUserResp;
    }

    @Override
    public List<SysUserResp> listByRoleIds(Long[] roldIdList) {
        List<SysUserResp> list = listByOrgIdsAndRoleIds(null, roldIdList);
        return list;
    }

    @Override
    public List<SysUserResp> listByOrgIds(Long[] orgIdList) {
        LambdaQueryWrapper<SysUserOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUserOrg::getOrgId, orgIdList);
        List<SysUserOrg> sysUserOrgList = userOrgService.list(queryWrapper);
        //获取用户ID集合
        List<Long> userIdList = sysUserOrgList.stream().distinct().map(SysUserOrg::getUserId).collect(Collectors.toList());
        List<SysUserResp> list = listUserByIds(userIdList);

        LambdaQueryWrapper<SysUserOrg> queryWrapperUserId = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUserOrg::getUserId, userIdList);
        List<SysUserOrg> sysUserOrgAllList = userOrgService.list(queryWrapper);
        Map<Long, List<SysUserOrg>> sysUserOrgAllMap = sysUserOrgAllList.stream()
                .collect(Collectors.groupingBy(SysUserOrg::getUserId));

        List<SysUserExtend> sysUserExtendList = sysUserExtendService.listByIds(userIdList);
        List<SysUserExtendResp> sysUserExtendRespList = SysUserExtendConvert.INSTANCE.doListToRespList(sysUserExtendList);
        Map<Long, SysUserExtendResp> sysUserExtendRespMap =
                sysUserExtendRespList.stream().collect(Collectors.toMap(SysUserExtendResp::getId, Function.identity()));
        list.forEach(u -> {
            u.setSysUserExtendResp(sysUserExtendRespMap.get(u.getUserId()));
            List<SysUserOrg> orgList = sysUserOrgAllMap.get(u.getUserId());
            if (ObjectUtil.isNotEmpty(orgList)){
                List<Long> orgIdAllList = orgList.stream().map(o -> o.getOrgId()).collect(Collectors.toList());
                u.setOrgIds(orgIdAllList.toArray(new Long[]{}));
            }
        });
        return list;
    }

    @Override
    public List<SysUserResp> listByOrgIdsAndRoleIds(Long[] orgIdList, Long[] roldIdList) {
        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUserRole::getRoleId, roldIdList);
        queryWrapper.in(ObjectUtil.isNotEmpty(orgIdList), SysUserRole::getOrgId, orgIdList);
        List<SysUserRole> sysUserRoleList = sysUserRoleService.list(queryWrapper);
        //获取用户ID集合
        List<Long> userIdList = sysUserRoleList.stream().distinct().map(SysUserRole::getUserId).collect(Collectors.toList());

        List<SysUserResp> list = listUserByIds(userIdList);
        return list;
    }

    @Override
    public List<SysUserResp> listByRoleCodeAndGuCode(String roleCode, String guCode) {
        List<String> pcodeList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(guCode)){
            SysCountryAddress sysCountryAddress = sysCountryAddressService.getByCode(guCode);
            if (ObjectUtil.isEmpty(sysCountryAddress)){
                return new ArrayList<>();
            }
            String pcodePath = sysCountryAddress.getPcodePath();
            String[] pcodePaths = pcodePath.split(";");
            pcodeList.add(guCode);
            if (pcodePaths.length > 2){
                pcodeList.add(pcodePaths[1]);
            }
            if (pcodePaths.length > 3) {
                pcodeList.add(pcodePaths[2]);
            }
            if (pcodePaths.length > 4) {
                pcodeList.add(pcodePaths[3]);
            }
            if (pcodePaths.length > 5) {
                pcodeList.add(pcodePaths[4]);
            }
        }
        Long roleId = null;
        if (StrUtil.isNotBlank(roleCode)){
            SysRole sysRole = roleService.getByCode(roleCode);
            roleId = sysRole.getRoleId();
        }

        List<SysUserResp> list = baseMapper.listByRoleCodeAndGuCode(roleId, pcodeList);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<SysUserReq> sysUserReqList) {
        List<SysUser> sysUserList = new ArrayList<>();
        List<SysUserExtend> sysUserExtendList = new ArrayList<>();
        sysUserReqList.forEach(user ->{
            //不允许修改的字段，置为空
            user.setPassword(null);
            user.setUserName(null);
            user.setUserType(null);
            user.setStatus(null);
            SysUserExtendReq sysUserExtendReq = user.getSysUserExtendReq();
            if (ObjectUtil.isNotEmpty(sysUserExtendReq)){
                SysUserExtend sysUserExtend = SysUserExtendConvert.INSTANCE.reqToDO(sysUserExtendReq);
                sysUserExtend.setId(user.getUserId());
                sysUserExtendList.add(sysUserExtend);
            }
            SysUser sysUser = SysUserConvert.INSTANCE.reqToDO(user);
            sysUserList.add(sysUser);
        });
        updateBatchById(sysUserList);
        sysUserExtendService.saveOrUpdateBatch(sysUserExtendList);

    }

    @Override
    public CertificateNoValidateResp getMobileByCard(CertificateNoValidateReq certificateNoValidateReq) {
        String certificateNo = certificateNoValidateReq.getCertificateNo();
        String uuidPar = certificateNoValidateReq.getUuid();
        String code = certificateNoValidateReq.getCode();
        //图形验证码的redis的key
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuidPar;
        String codeRedis = cacheClient.get(verifyKey);
        if (StrUtil.isBlank(code) || !StrUtil.equals(code, codeRedis)){
            throw new ServiceException("验证码输入有误请重新尝试!");
        }

        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getCertificateNo,certificateNo);
        queryWrapper.eq(SysUser::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysUser sysUser = getOne(queryWrapper);
        if (ObjectUtil.isEmpty(sysUser)){
            throw new ServiceException("身份证号码不存在!");
        }

        String phonenumber = sysUser.getPhonenumber();
        phonenumber = GeneralAlgorithmUtil.phoneNumberDesensitize(phonenumber);

        String uuid = IdUtils.simpleUUID();
        cacheClient.setCacheObject(CacheConstants.CARD_UUID_KEY + uuid, certificateNo, 5L, TimeUnit.MINUTES);
        cacheClient.setCacheObject(CacheConstants.PHONENUMBER_UUID_KEY + uuid, sysUser.getPhonenumber()
                , 5L, TimeUnit.MINUTES);

        CertificateNoValidateResp certificateNoValidateResp = new CertificateNoValidateResp();
        certificateNoValidateResp.setPhonenumber(phonenumber);
        certificateNoValidateResp.setUuid(uuid);
        return certificateNoValidateResp;
    }

    @Override
    public void updateForgetPwd(ForgetPwdReq forgetPwdReq) {
        String uuid = forgetPwdReq.getUuid();
        String certificateNo = forgetPwdReq.getCertificateNo();

        String certificateNoRedis = cacheClient.get(CacheConstants.CARD_UUID_KEY + uuid);
        if (!StrUtil.equals(certificateNoRedis, certificateNo)){
            throw new ServiceException("参数错误，请重新开始！");
        }

        //加密盐值
        String salt = cacheClient.get(CacheConstants.FORGET_PASSWORD_SALT + uuid);
        //原密码解密
        String newPassword = AESUtils.decrypt(forgetPwdReq.getNewPassword(), salt);
        //新密码解密
        String againNewPassword = AESUtils.decrypt(forgetPwdReq.getAgainNewPassword(), salt);

        if (!StrUtil.equals(newPassword, againNewPassword)){
            throw new ServiceException("两次输入的密码不一致！");
        }
        //根据身份证号修改密码
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysUser::getPassword, SecurityUtils.encryptPassword(againNewPassword));
        updateWrapper.eq(SysUser::getCertificateNo, certificateNo);
        update(new SysUser(), updateWrapper);

        cacheClient.delete(CacheConstants.CARD_UUID_KEY + uuid);
        cacheClient.delete(CacheConstants.FORGET_PASSWORD_SALT + uuid);
    }

    @Override
    public void updatePwd(UpdatePwdReq updatePwdReq) {
        //获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        SysUser sysUser = getById(userId);
        //旧密码加密
        String oldPassword = updatePwdReq.getOldPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, sysUser.getPassword())){
            throw new ServiceException("旧密码输入错误!");
        }
        if (!StrUtil.equals(updatePwdReq.getNewPassword(), updatePwdReq.getAgainNewPassword())){
            throw new ServiceException("两次输入密码不一致!");
        }
        //判断短信验证码是否正确
        String verifyKey = CacheConstants.SMS_CODE_KEY + UPDATE_TYPE + ":" + updatePwdReq.getUuid();
        String code = cacheClient.get(verifyKey);
        if (!StrUtil.equals(code, updatePwdReq.getCode())){
            throw new ServiceException("验证码错误，请重新输入！");
        }
        //修改用户密码
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setPassword(SecurityUtils.encryptPassword(updatePwdReq.getAgainNewPassword()));
        updateById(user);
    }

    @Override
    public void updatePersonalInfo(SysUserReq sysUserReq) {
        //获取当前用户ID
        Long userId = SecurityUtils.getUserId();

        SysUserExtendReq sysUserExtendReq = sysUserReq.getSysUserExtendReq();
        SysUserExtend sysUserExtend = SysUserExtendConvert.INSTANCE.reqToDO(sysUserExtendReq);
        sysUserExtend.setId(userId);
        if (StrUtil.isBlank(sysUserExtend.getExtendStr8()) && ObjectUtil.isNotNull(sysUserExtendReq.getExtendStr8s())){
            sysUserExtend.setExtendStr8(Arrays.stream(sysUserExtendReq.getExtendStr8s()).collect(Collectors.joining(Constants.COMMA)));
        }
        sysUserExtendService.saveOrUpdate(sysUserExtend);
    }

    /**
     * @Description: 新增用户与组织机构关系信息
     * @author: tr
     * @Date: 2024/3/18 20:25
     * @param: [sysUserReq]
     * @returnValue: void
     */
    private void insertUserOrg(SysUserReq sysUserReq){
        List<SysUserOrg> list = new ArrayList<>();
        List<SysUserRoleReq> userRoleList = sysUserReq.getUserRoleList();
        if (ObjectUtil.isNotEmpty(userRoleList)){
            List<Long> orgIdList = userRoleList.stream().map(r -> r.getOrgId()).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(orgIdList)){
                List<SysOrg> sysOrgList = orgService.listByOrdIds(orgIdList);
                for (SysOrg org : sysOrgList){
                    SysUserOrg sysUserOrg = new SysUserOrg();
                    sysUserOrg.setUserId(sysUserReq.getUserId());
                    sysUserOrg.setOrgId(org.getId());
                    sysUserOrg.setOrgCode(org.getOrgCode());
                    list.add(sysUserOrg);
                }
                userOrgService.saveBatch(list);
            }
        }
    }

    /**
     * @Description: 检查账户是否存在，手机号码是否存在，邮箱账户是否存在
     * @author: tr
     * @Date: 2024/3/19 17:00
     * @param: [user]
     * @returnValue: void
     */
    private void check(SysUser user){
        if (!this.checkUserNameUnique(user))
        {
            throw new ServiceException("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !this.checkPhoneUnique(user))
        {
            throw new ServiceException("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !this.checkEmailUnique(user))
        {
            throw new ServiceException("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
    }

    /**
     * @Description: 保存用户与角色关系，将用户、角色和组织机构关联
     * @author: tr
     * @Date: 2024/3/29 11:23
     * @param: [sysUserReq]
     * @returnValue: void
     */
    private void insertUserRole(SysUserReq sysUserReq){
        List<SysUserRoleReq> userRoleList = sysUserReq.getUserRoleList();
        if (StringUtils.isNotEmpty(userRoleList))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (SysUserRoleReq sysUserRoleReq : userRoleList)
            {
                Long[] roleIds = sysUserRoleReq.getRoleIds();
                if (ObjectUtil.isNotEmpty(roleIds)){
                    for (Long roldId : roleIds){
                        SysUserRole ur = new SysUserRole();
                        ur.setUserId(sysUserReq.getUserId());
                        ur.setRoleId(roldId);
                        ur.setOrgId(sysUserRoleReq.getOrgId());
                        ur.setDeptId(sysUserRoleReq.getDeptId());
                        list.add(ur);
                    }
                }
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * @Description: 根据用户ID集合查询用户信息列表
     * @author: tr
     * @Date: 2024/4/16 10:20
     * @param: [userIdList]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysUserResp>
     */
    private List<SysUserResp> listUserByIds(List<Long> userIdList){
        if (ObjectUtil.isEmpty(userIdList) || userIdList.size() == 0){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SysUser> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.in(SysUser::getUserId, userIdList);
        userQueryWrapper.eq(SysUser::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        List<SysUser> sysUserList = list(userQueryWrapper);
        List<SysUserResp> list = SysUserConvert.INSTANCE.doListToRespList(sysUserList);
        return list;
    }

    /**
     * @Description: 通过部门和姓名生成用户
     * @author: mbt
     * @Date: 2024/9/12 11:40
     * @param deptId 部门ID
     * @param userName 姓名
     * @returnValue: SysUserResp
     */
    @Override
    public SysUser createUserByDeptAndName(Long deptId, String userName) {
        SysUser user = new SysUser();
        String password = RandomPasswordUtils.getRandomPassword();
        String username = ChineseToPinyinUtils.getUserName(userName);
        user.setUserName(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setNickName(userName);
        user.setUserId(IdWorker.getId(user));
        user.setDeptId(deptId);
        save(user);
        //保存用户角色关系表
        SysRole role = roleService.getByCode(QUICK_USER_ROLE_KEY);
        SysUserRole userRole = new SysUserRole();
        userRole.setRoleId(role.getRoleId());
        userRole.setUserId(user.getUserId());
        userRoleMapper.insert(userRole);
        //保存用户机构
        SysUserOrg sysUserOrg = new SysUserOrg();
        sysUserOrg.setOrgId(deptId);
        sysUserOrg.setUserId(user.getUserId());
        sysUserOrgMapper.insert(sysUserOrg);
        user.setPassword(password);
        return user;
    }

    @Override
    public void setupPassword(SetupPwdReq setupPwdReq) {
        SysUserDTO currUser = SecurityUtils.getLoginUser().getSysUser();
        String phonenumber = currUser.getPhonenumber();
        if (!StrUtil.equals(phonenumber, setupPwdReq.getPhonenumber())){
            throw new ServiceException("手机号码不一致");
        }

        String type = setupPwdReq.getType();
        String uuid = setupPwdReq.getUuid();
        String verifySmsKey = CacheConstants.SMS_CODE_KEY+ type + ":" + phonenumber + ":" + uuid;
        String captcha = cacheClient.getCacheObject(verifySmsKey);
        if (StrUtil.isEmpty(captcha))
        {
            throw new ServiceException("验证码已失效");
        }

        if (!StrUtil.equalsIgnoreCase(setupPwdReq.getValidateCode(), captcha))
        {
            throw new ServiceException("验证码错误");
        }

        if (!StrUtil.equals(setupPwdReq.getNewPassword(), setupPwdReq.getAgainNewPassword())){
            cacheClient.deleteObject(verifySmsKey);
            throw new ServiceException("两次输入密码不一致!");
        }

        cacheClient.deleteObject(verifySmsKey);

        SysUser user = new SysUser();
        user.setUserId(currUser.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(setupPwdReq.getAgainNewPassword()));
        user.setUpdatePwdFlag(FlagEnum.YES.getCode());
        updateById(user);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        loginUser.getSysUser().setUpdatePwdFlag(FlagEnum.YES.getCode());
        TokenUtils.refreshToken(loginUser);
    }

    @Override
    public String updatePhonenumberNext(UpdatePhonenumberReq updatePhonenumberReq) {
        String type = updatePhonenumberReq.getType();
        String validateCode = updatePhonenumberReq.getValidateCode();
        String phonenumber = updatePhonenumberReq.getOldPhonenumber();
        String uuid = updatePhonenumberReq.getUuid();

        String verifySmsKey = CacheConstants.SMS_CODE_KEY+ type + ":" + phonenumber + ":" + uuid;
        String captcha = cacheClient.getCacheObject(verifySmsKey);
        if (StrUtil.isEmpty(captcha))
        {
            throw new ServiceException("验证码已失效");
        }
        if (!StrUtil.equalsIgnoreCase(validateCode, captcha))
        {
            throw new ServiceException("验证码错误");
        }
        cacheClient.deleteObject(verifySmsKey);
        String uuidNext = IdUtils.simpleUUID();
        String verifySmsKeyNext = CacheConstants.SMS_CODE_KEY+ type + ":" + phonenumber + ":" + uuidNext;
        cacheClient.setCacheObject(verifySmsKeyNext, uuidNext, 5L, TimeUnit.MINUTES);
        return uuidNext;
    }

    @Override
    public void updatePhonenumber(UpdatePhonenumberReq updatePhonenumberReq) {
        String type = updatePhonenumberReq.getType();
        String validateCode = updatePhonenumberReq.getValidateCode();
        String oldPhonenumber = updatePhonenumberReq.getOldPhonenumber();
        String newPhonenumber = updatePhonenumberReq.getNewPhonenumber();
        String uuid = updatePhonenumberReq.getUuid();
        String nextUuid = updatePhonenumberReq.getNextUuid();

        if (StrUtil.isBlank(nextUuid)){
            throw new ServiceException("原手机号码验证已失效，请返回上一步重新获取验证码");
        }
        String verifySmsKeyNext = CacheConstants.SMS_CODE_KEY+ type + ":" + oldPhonenumber + ":" + nextUuid;
        String captchaNext = cacheClient.getCacheObject(verifySmsKeyNext);
        if (StrUtil.isBlank(captchaNext)){
            throw new ServiceException("原手机号码验证已失效，请返回上一步重新获取验证码");
        }
        String verifySmsKey = CacheConstants.SMS_CODE_KEY+ type + ":" + newPhonenumber + ":" + uuid;
        String captcha = cacheClient.getCacheObject(verifySmsKey);
        if (StrUtil.isEmpty(captcha))
        {
            throw new ServiceException("验证码已失效");
        }
        if (!StrUtil.equalsIgnoreCase(validateCode, captcha))
        {
            throw new ServiceException("验证码错误");
        }
        cacheClient.deleteObject(verifySmsKey);

        SysUserDTO currUser = SecurityUtils.getLoginUser().getSysUser();

        SysUser user = new SysUser();
        user.setUserId(currUser.getUserId());
        user.setPhonenumber(newPhonenumber);
        if (StrUtil.equals(currUser.getUserName(), oldPhonenumber)){
            //如果用户名称=原手机号码，则将用户名修改为新手机号码
            user.setUserName(newPhonenumber);
        }
        updateById(user);
    }

    @Override
    public void updateForgetPhonenumberPwd(ForgetPwdPhonenumberReq forgetPwdPhonenumberReq) {
        String uuid = forgetPwdPhonenumberReq.getUuid();
        String phonenumber = forgetPwdPhonenumberReq.getPhonenumber();
        String type = forgetPwdPhonenumberReq.getType();

        String verifySmsKey = CacheConstants.SMS_CODE_KEY+ type + ":" + phonenumber + ":" + uuid;
        String captcha = cacheClient.getCacheObject(verifySmsKey);
        if (StrUtil.isBlank(captcha)){
            throw new ServiceException("验证码已失效");
        }

        String validateCode = forgetPwdPhonenumberReq.getValidateCode();
        if (!StrUtil.equalsIgnoreCase(validateCode, captcha))
        {
            throw new ServiceException("验证码错误");
        }

        //加密盐值
        String salt = cacheClient.get(CacheConstants.FORGET_PASSWORD_SALT + uuid);
        //原密码解密
        String newPassword = AESUtils.decrypt(forgetPwdPhonenumberReq.getNewPassword(), salt);
        //新密码解密
        String againNewPassword = AESUtils.decrypt(forgetPwdPhonenumberReq.getAgainNewPassword(), salt);

        if (!StrUtil.equals(newPassword, againNewPassword)){
            throw new ServiceException("两次输入的密码不一致！");
        }
        //根据手机号修改密码
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysUser::getPassword, SecurityUtils.encryptPassword(againNewPassword));
        updateWrapper.eq(SysUser::getPhonenumber, phonenumber);
        updateWrapper.eq(SysUser::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        update(new SysUser(), updateWrapper);

        cacheClient.delete(verifySmsKey);
        cacheClient.delete(CacheConstants.FORGET_PASSWORD_SALT + uuid);
    }

    @Override
    public List<SysUserResp> listByIdList(Long[] userIdList) {
        SysUserPageReq sysUserPageReq = new SysUserPageReq();
        sysUserPageReq.setUserIdList(Arrays.asList(userIdList));
        List<SysUserResp> list = this.baseMapper.selectUserList(sysUserPageReq);
        return list;
    }

    @Override
    public void switchOrgAndRole(SwitchOrgAndRoleReq switchOrgAndRoleReq) {
        Long userId = SecurityUtils.getUserId();
        SysUser user = getById(userId);
        user.setLastLoginRole(switchOrgAndRoleReq.getLastLoginRole());
        user.setLastLoginOrgId(switchOrgAndRoleReq.getLastLoginOrgId());
        updateById(user);
    }

    @Override
    public UserResp validateToken(String token) {
        LoginUser loginUser = TokenUtils.checkToken(token);
        UserResp userResp = SysUserConvert.INSTANCE.DTOToResp(loginUser.getSysUser());
        return userResp;
    }
}
