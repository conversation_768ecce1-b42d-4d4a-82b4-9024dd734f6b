package com.platform.system.service.impl;

import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.domain.SysReceiveLog;
import com.platform.system.domain.request.ReceiveParamsReq;
import com.platform.system.mapper.SysReceiveLogMapper;
import com.platform.system.service.ReceiveParamsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 接收消息的service层
 * @author: tr
 * @date: 2024年01月29日 10:11
 */
@Slf4j
@Service
public class ReceiveParamsServiceImpl implements ReceiveParamsService {

    @Autowired
    private SysReceiveLogMapper sysReceiveLogMapper;

    @Autowired
    StreamBridge streamBridge;

    @Autowired
    private CacheClient redisService;

    /** 与配置文件中的bindings对应，消息发送者的信息 **/
    private String SEND_PARAMS_PRODUCT = "platform-send-params-output";

    @Override
    @Async
    public void receiveMsg(ReceiveParamsReq receiveParamsReq) {
        redisService.setCacheObject(CacheConstants.RECEIVE_PARAMS_KEY + System.currentTimeMillis()
                , JSONUtil.toJsonStr(receiveParamsReq), CacheConstants.REFRESH_TIME, TimeUnit.MINUTES);
        log.info("接收线程-开始：{}", JSONUtil.toJsonStr(receiveParamsReq));
        //将接收到的参数保存数据库，处理方式方式分两种，数据库只保存两个月数据或数据库按年月建表
        SysReceiveLog receiveLog = new SysReceiveLog();
        receiveLog.setReceiveParams(receiveParamsReq.getParams());
        receiveLog.setCreateTime(new Date());
        receiveLog.setUpdateTime(new Date());
        sysReceiveLogMapper.insert(receiveLog);
        //将接收到的参数通过kafka发送出去，供消费者进行业务逻辑处理
        boolean flag = streamBridge.send(SEND_PARAMS_PRODUCT, receiveParamsReq.getParams());
        log.info("消息发送成功标识：{}", flag);
        log.info("接收线程-结束：{}", JSONUtil.toJsonStr(receiveParamsReq));
    }


}
