package com.platform.system.convert;

import com.platform.system.api.domain.request.SysOrgReq;
import com.platform.system.api.domain.response.SysOrgResp;
import com.platform.system.api.model.SysOrgDTO;
import com.platform.system.domain.SysOrg;
import com.platform.system.openapi.domain.response.OrgResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 组织机构信息转换
 * @author: tr
 * @date: 2024年02月29日 17:23
 */
@Mapper
public interface SysOrgConvert {

    SysOrgConvert INSTANCE = Mappers.getMapper(SysOrgConvert.class);

    SysOrg reqToDO(SysOrgReq sysOrgReq);

    List<SysOrg> reqListToDOList(List<SysOrgReq> sysOrgReqList);

    SysOrgResp doToResp(SysOrg sysOrg);

    List<SysOrgResp> doListToRespList(List<SysOrg> sysOrgList);

    SysOrgDTO doToDTO(SysOrg sysOrg);

    List<SysOrgDTO> doListToDtoList(List<SysOrg> sysOrgList);

    /** OpenApi接口的返回参数 **/
    OrgResp doToRespOpenApi(SysOrg sysOrg);
    /** OpenApi接口的返回参数 **/
    List<OrgResp> doListToRespListOpenApi(List<SysOrg> sysOrgList);
}
