package com.platform.system.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.constant.SystemConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.datascope.annotation.DataScope;
import com.platform.system.api.domain.request.SysOrgExtendReq;
import com.platform.system.api.domain.request.SysOrgPageReq;
import com.platform.system.api.domain.request.SysOrgReq;
import com.platform.system.api.domain.response.SysOrgExtendResp;
import com.platform.system.api.domain.response.SysOrgResp;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.convert.SysOrgConvert;
import com.platform.system.convert.SysOrgExtendConvert;
import com.platform.system.domain.SysCountryAddress;
import com.platform.system.domain.SysOrg;
import com.platform.system.domain.SysOrgExtend;
import com.platform.system.mapper.SysOrgMapper;
import com.platform.system.service.SysCountryAddressService;
import com.platform.system.service.SysOrgExtendService;
import com.platform.system.service.SysOrgService;
import com.platform.system.service.SysUserOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 组织机构Service实现层
 * @author: tr
 * @date: 2024年03月15日 11:07
 */
@Slf4j
@Service
public class SysOrgServiceImpl extends ServiceImpl<SysOrgMapper, SysOrg> implements SysOrgService {

    @Resource
    private SysOrgMapper orgMapper;

    @Autowired
    private SysCountryAddressService sysCountryAddressService;

    @Autowired
    private SysUserOrgService userOrgService;

    @Autowired
    private SysOrgExtendService sysOrgExtendService;


    @Override
    public List<SysOrgResp> list(SysOrgPageReq sysOrgPageReq) {
        List<SysOrgResp> list = baseMapper.listAllByCondition(sysOrgPageReq);
        if (ObjectUtil.isNotEmpty(list)){
            List<String> codeList = list.stream().map(s -> s.getDzCode()).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(codeList)){
                List<SysCountryAddress> sysCountryAddressList = sysCountryAddressService.listByCodes(codeList);
                Map<String, String> countryMap = sysCountryAddressList.stream()
                        .collect(Collectors.toMap(SysCountryAddress::getCode, SysCountryAddress::getRegionFullName));

                list.forEach(o ->{
                    o.setParentId(ObjectUtil.isNull(o.getParentId()) ? BusinessConstants.PARENT_ID : o.getParentId());
                    o.setDzFullName(MapUtil.getStr(countryMap, o.getDzCode(), ""));
                });
            }
        }
        return list;
    }

    @Override
    public SysOrgResp getById(Long id) {
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrg::getId, id);
        queryWrapper.eq(SysOrg::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysOrg sysOrg = getOne(queryWrapper);

        SysOrgExtendResp sysOrgExtendResp = sysOrgExtendService.getOneById(id);

        SysOrgResp sysOrgResp = SysOrgConvert.INSTANCE.doToResp(sysOrg);
        sysOrgResp.setSysOrgExtendResp(sysOrgExtendResp);
        return sysOrgResp;
    }

    @Override
    public void save(SysOrgReq sysOrgReq) {
        SysOrg sysOrg = SysOrgConvert.INSTANCE.reqToDO(sysOrgReq);
        Long parentId = sysOrg.getParentId();
        //机构编码
        String orgCode = getOrgCode(sysOrg, parentId);

        sysOrg.setOrgCode(orgCode);
        setAddressInfo(sysOrg);

        save(sysOrg);

        //新增扩展字段信息
        SysOrgExtendReq sysOrgExtendReq = sysOrgReq.getSysOrgExtendReq();
        if (ObjectUtil.isNotEmpty(sysOrgExtendReq)){
            SysOrgExtend sysOrgExtend = SysOrgExtendConvert.INSTANCE.reqToDO(sysOrgExtendReq);
            sysOrgExtend.setId(sysOrg.getId());
            sysOrgExtendService.save(sysOrgExtend);
        }
    }

    @Override
    public void update(SysOrgReq sysOrgReq) {
        SysOrg sysOrg = SysOrgConvert.INSTANCE.reqToDO(sysOrgReq);

        setAddressInfo(sysOrg);

        updateById(sysOrg);

        //修改扩展字段信息
        SysOrgExtendReq sysOrgExtendReq = sysOrgReq.getSysOrgExtendReq();
        if (ObjectUtil.isNotEmpty(sysOrgExtendReq)){
            SysOrgExtend sysOrgExtend = SysOrgExtendConvert.INSTANCE.reqToDO(sysOrgExtendReq);
            sysOrgExtend.setId(sysOrg.getId());
            sysOrgExtendService.saveOrUpdate(sysOrgExtend);
        }
    }

    @Override
    public void delete(Long id) {
        //判断机构是否已关联用户，关联用户不允许删除
        boolean flag = userOrgService.existByOrgId(id);
        if (flag){
            throw new ServiceException("组织机构已关联用户，不允许删除！");
        }
        //组织机构已绑定下级组织机构不允许删除
        LambdaQueryWrapper<SysOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysOrg::getParentId, id);
        queryWrapper.eq(SysOrg::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        long count = count(queryWrapper);
        if (count > 0){
            throw new ServiceException("组织机构已绑定下级组织机构，不允许删除！");
        }

        SysOrg sysOrg = new SysOrg();
        sysOrg.setId(id);
        sysOrg.setDelFlag(DelFlagEnum.DELETED.getCode());
        updateById(sysOrg);
    }

    @Override
    public List<SysOrgResp> listByUserId(Long userId) {
        return orgMapper.listByUserId(userId);
    }

    @Override
    public List<SysOrg> listByOrdIds(List<Long> ordIds) {
        LambdaQueryWrapper<SysOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(SysOrg::getId, ordIds);
        queryWrapper.eq(SysOrg::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        return list(queryWrapper);
    }

    @Override
    public String batchSaveOrg(List<SysOrgReq> list) {
        if (ObjectUtil.isEmpty(list)){
            throw new ServiceException("导入数据不能为空！");
        }

        //获取顶级机构的最大的机构编码
        String parentNextOrgCode = getParentNextLevelOrgCode(BusinessConstants.PARENT_ID);

        //存储父类的机构编码
        Map<String, String> parentCodeMap = new HashMap<>();
        //存储当前类的机构编码
        Map<String, String> currCodeMap = new HashMap<>();
        //存储当前父类的id
        Map<String, Long> parentIdMap = new HashMap<>();
        List<SysOrg> sysOrgList = SysOrgConvert.INSTANCE.reqListToDOList(list);
        for (SysOrg sysOrg : sysOrgList){
            sysOrg.setId(IdWorker.getId(sysOrg));
            String parentCodeThird = sysOrg.getParentCodeThird();
            if (StrUtil.equals(parentCodeThird, "0")){
                //一个顶级机构
                sysOrg.setOrgCode(parentNextOrgCode);
                sysOrg.setParentId(BusinessConstants.PARENT_ID);
                parentCodeMap.put(sysOrg.getOrgCodeThird(), parentNextOrgCode);
            }else{
                String orgCode = "";
                if (StrUtil.isNotBlank(parentCodeMap.get(sysOrg.getParentCodeThird()))){
                    orgCode = getNextLevelOrgCode(
                            parentCodeMap.get(sysOrg.getParentCodeThird()), currCodeMap.get(sysOrg.getParentCodeThird()));
                    currCodeMap.put(sysOrg.getParentCodeThird(), orgCode);
                    parentCodeMap.put(sysOrg.getOrgCodeThird(), orgCode);
                }
                sysOrg.setOrgCode(orgCode);
                sysOrg.setParentId(parentIdMap.get(sysOrg.getParentCodeThird()));
            }
            parentIdMap.put(sysOrg.getOrgCodeThird(), sysOrg.getId());
        }
        saveBatch(sysOrgList);
        return "导入成功！";
    }

    /**
     * @Description: 组织机构赋予地址码相关信息
     * @author: tr
     * @Date: 2024/5/21 11:12
     * @param: [sysOrg]
     * @returnValue: void
     */
    private void setAddressInfo(SysOrg sysOrg){
        //查询所在地和管辖返回的地址码信息
        List<String> codeList = new ArrayList<>();
        codeList.add(sysOrg.getDzCode());
        codeList.add(sysOrg.getGuCode());
        List<SysCountryAddress> sysCountryAddressList = sysCountryAddressService.listByCodes(codeList);
        sysCountryAddressList.forEach(address ->{
            if (StrUtil.equals(sysOrg.getDzCode(), address.getCode())){
                sysOrg.setDzRegionLevel(address.getRegionLevel());
                sysOrg.setDzPcodePath(address.getPcodePath());
                sysOrg.setDzName(address.getRegionName());
            }
            if (StrUtil.equals(sysOrg.getGuCode(), address.getCode())){
                sysOrg.setGuRegionLevel(address.getRegionLevel());
                sysOrg.setGuPcodePath(address.getPcodePath());
                sysOrg.setGuName(address.getRegionName());
            }
        });
    }

    /**
     * @Description: 获取当前父级下的最大的机构编码，逻辑删除的也需计算在内
     * @author: tr
     * @Date: 2024/9/13 10:39
     * @param: [parentId]
     * @returnValue: java.lang.String
     */
    private String getMaxOrgCode(Long parentId){
        //获取当前父级下的最大的机构编码，逻辑删除的也需计算在内
        LambdaQueryWrapper<SysOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(SysOrg::getOrgCode);
        queryWrapper.eq(SysOrg::getParentId, parentId);
        queryWrapper.orderByDesc(SysOrg::getCreateTime);
        queryWrapper.last("limit 1");
        SysOrg sysOrg = getOne(queryWrapper);
        if (ObjectUtil.isNull(sysOrg)){
            return null;
        }
        String orgCode = sysOrg.getOrgCode();
        return orgCode;
    }

    /**
     * @Description: 获取组织机构的下一级编码
     * @author: tr
     * @Date: 2024/9/13 9:56
     * @param: [orgCode, nextOrgCode]
     * @returnValue: java.lang.String
     */
    private String getNextLevelOrgCode(String orgCode, String nextOrgCode){
        if (StrUtil.isBlank(nextOrgCode)){
            return orgCode + "0000";
        }else{
            String prefix = nextOrgCode.substring(0, nextOrgCode.length()-4);
            String suffix = nextOrgCode.substring(nextOrgCode.length()-4);
            return prefix + StringUtils.increment(suffix);
        }
    }

    /**
     * @Description: 获取自动生成的组织机构代码
     * @author: tr
     * @Date: 2024/10/16 17:16
     * @param: [sysOrg, parentId]
     * @returnValue: java.lang.String
     */
    private String getOrgCode(SysOrg sysOrg, Long parentId){
        //机构编码
        String orgCode = "";

        if (parentId != null && !ObjectUtil.equal(parentId, BusinessConstants.PARENT_ID)){
            //非父节点
            String maxOrgCode = getMaxOrgCode(parentId);
            if (StrUtil.isBlank(maxOrgCode)){
                SysOrg sysOrgDB = super.getById(parentId);
                if (ObjectUtil.isEmpty(sysOrgDB)){
                    throw new ServiceException("父级机构不存在，新增失败!");
                }
                orgCode = sysOrgDB.getOrgCode();
            }
            orgCode = getNextLevelOrgCode(orgCode, maxOrgCode);
        }else{
            //父节点
            sysOrg.setParentId(BusinessConstants.PARENT_ID);
            orgCode = getParentNextLevelOrgCode(sysOrg.getParentId());
        }
        return orgCode;
    }

    /**
     * @Description: 获取父类的下一级编码
     * @author: tr
     * @Date: 2024/10/16 17:29
     * @param: [sysOrg]
     * @returnValue: java.lang.String
     */
    public String getParentNextLevelOrgCode(Long parentId){
        //机构编码
        String orgCode = "";

        String maxOrgCode = getMaxOrgCode(parentId);
        if (ObjectUtil.isEmpty(maxOrgCode)){
            orgCode = BusinessConstants.ORG_FIRST_CODE;
        }else{
            String prefixCode = maxOrgCode.substring(0,3);
            orgCode = prefixCode + StringUtils.increment(maxOrgCode.substring(3));
        }
        return orgCode;
    }
}
