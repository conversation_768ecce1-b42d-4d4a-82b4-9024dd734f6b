package com.platform.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.domain.request.SysLogininforPageReq;
import com.platform.system.domain.response.SysLogininforResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.InnerAuth;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.api.domain.SysLogininfor;
import com.platform.system.service.SysLogininforService;

/**
 * 系统访问记录
 * 
 * <AUTHOR>
 */
@Api(tags = "系统访问记录前端控制器")
@RestController
@RequestMapping("/logininfor")
public class SysLogininforController extends BaseController
{
    @Autowired
    private SysLogininforService logininforService;

    @Autowired
    private CacheClient redisService;

    @ApiOperation(value = "获取系统访问记录列表",  notes = "系统访问记录")
    @RequiresPermissions("system:logininfor:list")
    @GetMapping("/list")
    public ResponseResult<PageResult<SysLogininforResp>> list(SysLogininforPageReq sysLogininforPageReq)
    {
        Page<SysLogininforResp> page = PageUtils.startPage(sysLogininforPageReq.getPageNum(), sysLogininforPageReq.getPageSize());
        List<SysLogininforResp> list = logininforService.selectLogininforList(sysLogininforPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "导出系统访问记录",  notes = "系统访问记录")
    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:logininfor:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLogininforPageReq sysLogininforPageReq)
    {
        List<SysLogininforResp> list = logininforService.selectLogininforList(sysLogininforPageReq);
        ExcelUtil<SysLogininforResp> util = new ExcelUtil<>(SysLogininforResp.class);
        util.exportExcel(response, list, "登录日志");
    }

    @ApiOperation(value = "根据ID移除系统访问记录",  notes = "系统访问记录")
    @RequiresPermissions("system:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam("infoIds") Long[] infoIds)
    {
        return logininforService.deleteLogininforByIds(infoIds)>0?ResponseResult.ok():ResponseResult.fail("删除失败");
    }

    @ApiOperation(value = "清空系统访问记录",  notes = "系统访问记录")
    @RequiresPermissions("system:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean")
    public ResponseResult clean()
    {
        logininforService.cleanLogininfor();
        return ResponseResult.ok();
    }

    @ApiOperation(value = "根据用户名对账户解锁",  notes = "系统访问记录")
    @RequiresPermissions("system:logininfor:unlock")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public ResponseResult unlock(@PathVariable("userName") String userName)
    {
        redisService.deleteObject(CacheConstants.PWD_ERR_CNT_KEY + userName);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "新增系统访问记录",  notes = "系统访问记录")
    @InnerAuth
    @PostMapping("/save")
    public ResponseResult add(@RequestBody SysLogininfor logininfor)
    {
        return logininforService.insertLogininfor(logininfor)==1?ResponseResult.ok():ResponseResult.fail("新增失败");
    }
}
