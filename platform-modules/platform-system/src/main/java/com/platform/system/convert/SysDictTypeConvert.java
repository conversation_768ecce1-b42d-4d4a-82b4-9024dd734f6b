package com.platform.system.convert;

import com.platform.system.api.domain.SysDictType;
import com.platform.system.api.domain.response.SysDictTypeResp;
import com.platform.system.domain.request.SysDictTypeReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * @Description: 字典类型信息转换
 * @author: tr
 * @date: 2024年02月02日 16:05
 */
@Mapper
public interface SysDictTypeConvert {

    SysDictTypeConvert INSTANCE = Mappers.getMapper(SysDictTypeConvert.class);

    SysDictType reqToDO(SysDictTypeReq sysDictTypeReq);

    SysDictTypeResp doToResp(SysDictType sysDictType);

    List<SysDictTypeResp> doListToRespList(List<SysDictType> sysDictTypeList);

    List<SysDictType> reqListToDoList(List<SysDictTypeReq> sysDictTypeReqList);
}
