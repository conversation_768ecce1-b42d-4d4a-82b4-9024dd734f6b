package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.domain.request.SysUserSubjectReq;
import com.platform.system.domain.response.SysUserSubjectResp;
import com.platform.system.service.SysUserSubject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 用户主题前端控制层
 * @author: tr
 * @date: 2023年12月11日 10:20
 */
@Api(tags = "用户主题前端控制器")
@RestController
@RequestMapping("/userSubject")
public class SysUserSubjectController extends BaseController {

    @Autowired
    private SysUserSubject sysUserSubject;

    @ApiOperation(value = "新增用户的主题信息",  notes = "用户主题")
    @PostMapping("add")
    public ResponseResult add(@RequestBody SysUserSubjectReq sysUserSubjectReq){
        sysUserSubject.add(sysUserSubjectReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "查询用户的主题信息（每个用户只有一个主题信息）",  notes = "用户主题")
    @PostMapping("getOneByUser")
    public ResponseResult<SysUserSubjectResp> getOneByUser(){
        SysUserSubjectResp sysUserSubjectResp = sysUserSubject.getOneByUser();
        return ResponseResult.ok(sysUserSubjectResp);
    }
}
