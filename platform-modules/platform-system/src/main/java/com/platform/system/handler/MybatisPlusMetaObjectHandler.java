package com.platform.system.handler;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.security.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * @Description: 自动补充插入或更新时的值
 * @author: tr
 * @Date: 2024/3/18 11:13
 */
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        String username = SecurityUtils.getUsername();

        this.setFieldValByName("createBy", username, metaObject);
        this.setFieldValByName("updateBy", username, metaObject);

        String createTime = metaObject.findProperty("createTime", true);
        if (StrUtil.isNotBlank(createTime) && metaObject.getValue(createTime) == null){
            this.setFieldValByName("createTime", new Date(),metaObject);
        }
        String updateTime = metaObject.findProperty("updateTime", true);
        if (StrUtil.isNotBlank(updateTime) &&  metaObject.getValue(updateTime) == null){
            this.setFieldValByName("updateTime", new Date(),metaObject);
        }
        String delFlag = metaObject.findProperty("delFlag", true);
        if (StrUtil.isNotBlank(delFlag) && metaObject.getValue(delFlag) == null){
            this.setFieldValByName("delFlag", DelFlagEnum.NO_DELETED.getCode(),metaObject);
        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        String username = SecurityUtils.getUsername();
        this.setFieldValByName("updateBy", username, metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }
}
