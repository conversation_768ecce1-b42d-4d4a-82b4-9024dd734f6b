//package com.platform.system.consumer;
//
//import com.platform.system.config.ConsumerProcessor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.cloud.stream.annotation.EnableBinding;
//import org.springframework.cloud.stream.annotation.StreamListener;
//
///**
// * @Description: 接收参数消费者
// * @author: tr
// * @date: 2024年01月29日 14:46
// */
//@Slf4j
//@EnableBinding(ConsumerProcessor.class)
//public class ReceiveParamsReceiver {
//
//    /**
//     * @Description: 监听消息通道的消息
//     * @author: tr
//     * @Date: 2024/1/30 16:50
//     * @param: [message]
//     * @returnValue: void
//     */
//    @StreamListener(ConsumerProcessor.SEND_PARAMS_CONSUMER)
//    public void consume(String message) {
//        log.info("接收消息：{}", message);
//    }
//}
