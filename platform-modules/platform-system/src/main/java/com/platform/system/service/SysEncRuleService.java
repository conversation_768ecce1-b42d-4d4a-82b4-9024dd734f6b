package com.platform.system.service;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.domain.vo.SysEncRule;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * <p>
 * 加密规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface SysEncRuleService extends IService<SysEncRule> {


    /**
     * page
     */
    ResponseResult<List<SysEncRule>> page(SysEncRule entity, Pageable pageable);

    /**
     * Get
     */
    List<SysEncRule> findList(SysEncRule entity);

    /**
     * Get
     */
    SysEncRule findOne(Integer id);

    /**
     * create
     */
    SysEncRule create(SysEncRule entity);


    /**
     * update
     */
    int update(SysEncRule entity);

    /**
     * Delete
     */
    int delete(Integer id);

     /**
     * existBySysEncRuleId
     */
    boolean existBySysEncRuleId(Integer code);

    /**
    * create batch
    */
    Boolean insertBatch(List<SysEncRule> dataList);

    /**
     * 加载参数缓存数据
     */
    public void loadingConfigCache();

    /**
     * 清空参数缓存数据
     */
    public void clearConfigCache();

    /**
     * 重置参数缓存数据
     */
    public void resetConfigCache();



}
