package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.domain.SysUserRole;
import com.platform.system.mapper.SysUserRoleMapper;
import com.platform.system.service.SysUserRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 用户与角色Service实现类
 * @author: tr
 * @date: 2024年04月01日 19:00
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {

    @Override
    public List<SysUserRole> listByUserId(Long userId) {
        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysUserRole::getUserId, userId);
        List<SysUserRole> list = list(queryWrapper);
        return list;
    }
}
