package com.platform.system.service.impl;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.convert.SysPostConvert;
import com.platform.system.domain.request.SysPostPageReq;
import com.platform.system.domain.request.SysPostReq;
import com.platform.system.domain.response.SysPostResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.system.domain.SysPost;
import com.platform.system.mapper.SysPostMapper;
import com.platform.system.mapper.SysUserPostMapper;
import com.platform.system.service.SysPostService;

/**
 * 岗位信息 服务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPostServiceImpl extends ServiceImpl<SysPostMapper, SysPost> implements SysPostService
{
    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    /**
     * 查询岗位信息集合
     * 
     * @param post 岗位信息
     * @return 岗位信息集合
     */
    @Override
    public List<SysPostResp> selectPostList(SysPostPageReq sysPostPageReq)
    {
        LambdaQueryWrapper<SysPost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotEmpty(sysPostPageReq.getPostCode()), SysPost::getPostCode, sysPostPageReq.getPostCode());
        queryWrapper.eq(StrUtil.isNotEmpty(sysPostPageReq.getStatus()), SysPost::getStatus, sysPostPageReq.getStatus());
        queryWrapper.like(StrUtil.isNotEmpty(sysPostPageReq.getPostName()), SysPost::getPostName, sysPostPageReq.getPostName());
        List<SysPost> list = list(queryWrapper);
        List<SysPostResp> sysPostRespList = SysPostConvert.INSTANCE.doListToRespList(list);
        return sysPostRespList;
    }

    /**
     * 查询所有岗位
     * 
     * @return 岗位列表
     */
    @Override
    public List<SysPostResp> selectPostAll()
    {
        List<SysPost> list = list();
        List<SysPostResp> sysPostRespList = SysPostConvert.INSTANCE.doListToRespList(list);
        return sysPostRespList;
    }

    /**
     * 通过岗位ID查询岗位信息
     * 
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    @Override
    public SysPostResp selectPostById(Long postId)
    {
        SysPost sysPost = getById(postId);
        SysPostResp sysPostResp = SysPostConvert.INSTANCE.doToResp(sysPost);
        return sysPostResp;
    }

    /**
     * 校验岗位名称是否唯一
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    @Override
    public boolean checkPostNameUnique(SysPostReq sysPostReq)
    {
        Long postId = StringUtils.isNull(sysPostReq.getPostId()) ? -1L : sysPostReq.getPostId();
        LambdaQueryWrapper<SysPost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPost::getPostName, sysPostReq.getPostName());
        queryWrapper.last("limit 1");
        SysPost info = getOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getPostId().longValue() != postId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验岗位编码是否唯一
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    @Override
    public boolean checkPostCodeUnique(SysPostReq sysPostReq)
    {
        Long postId = StringUtils.isNull(sysPostReq.getPostId()) ? -1L : sysPostReq.getPostId();
        LambdaQueryWrapper<SysPost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPost::getPostCode, sysPostReq.getPostCode());
        queryWrapper.last("limit 1");
        SysPost info = getOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getPostId().longValue() != postId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 通过岗位ID查询岗位使用数量
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int countUserPostById(Long postId)
    {
        return userPostMapper.countUserPostById(postId);
    }

    /**
     * 批量删除岗位信息
     * 
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    @Override
    public int deletePostByIds(Long[] postIds)
    {
        for (Long postId : postIds) {
            SysPostResp post = selectPostById(postId);
            if (countUserPostById(postId) > 0)
            {
                throw new ServiceException(String.format("%1$s已分配,不能删除", post.getPostName()));
            }
        }
        boolean flag = removeBatchByIds(Arrays.asList(postIds));
        return flag ? 1 : 0;
    }

    /**
     * 新增保存岗位信息
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    @Override
    public int insertPost(SysPostReq sysPostReq) {
        SysPost post = SysPostConvert.INSTANCE.reqToDO(sysPostReq);
        boolean flag = save(post);
        return flag ? 1 : 0;
    }

    /**
     * 修改保存岗位信息
     * 
     * @param sysPostReq 岗位信息
     * @return 结果
     */
    @Override
    public int updatePost(SysPostReq sysPostReq) {
        SysPost post = SysPostConvert.INSTANCE.reqToDO(sysPostReq);
        boolean flag = updateById(post);
        return flag ? 1 : 0;
    }
}
