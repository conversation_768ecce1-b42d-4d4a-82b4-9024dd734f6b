package com.platform.system.login.provider;

import com.platform.system.login.config.third.ThirdAuthenticationToken;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.login.domain.req.ThirdLoginReq;
import com.platform.system.login.service.SysPasswordService;
import com.platform.system.login.service.impl.UserDetailServiceImpl;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月30日 16:17
 */
public class ThirdAuthenticationProvider implements AuthenticationProvider {

    private UserDetailServiceImpl userDetailService;

    private SysPasswordService sysPasswordService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        ThirdLoginReq thirdLoginReq = (ThirdLoginReq)authentication.getPrincipal();

        SysUserDetails sysUserDetails = userDetailService.loadUserByUsername(thirdLoginReq.getUsername());

        sysPasswordService.validate(thirdLoginReq.getUsername(), sysUserDetails.getPassword(), thirdLoginReq.getPassword());

        //密码清空后返回token对象并放入缓存中，以便其他地方使用
        sysUserDetails.setPassword(null);
        ThirdAuthenticationToken userPwdAuthenticationToken = new ThirdAuthenticationToken(sysUserDetails, sysUserDetails.getAuthorities());

        return userPwdAuthenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(ThirdAuthenticationToken.class);
    }

    public void setUserDetailsService(UserDetailServiceImpl userDetailsService, SysPasswordService sysPasswordService) {
        this.userDetailService = userDetailsService;
        this.sysPasswordService = sysPasswordService;
    }
}
