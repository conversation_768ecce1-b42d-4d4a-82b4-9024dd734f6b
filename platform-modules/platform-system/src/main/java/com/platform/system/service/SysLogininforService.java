package com.platform.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.SysLogininfor;
import com.platform.system.domain.request.SysLogininforPageReq;
import com.platform.system.domain.response.SysLogininforResp;

/**
 * 系统访问日志情况信息 服务层
 * 
 * <AUTHOR>
 */
public interface SysLogininforService extends IService<SysLogininfor>
{
    /**
     * 新增系统登录日志
     * 
     * @param logininfor 访问日志对象
     */
    public int insertLogininfor(SysLogininfor logininfor);

    /**
     * 查询系统登录日志集合
     * 
     * @param sysLogininforPageReq 访问日志对象
     * @return 登录记录集合
     */
    public List<SysLogininforResp> selectLogininforList(SysLogininforPageReq sysLogininforPageReq);

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    public int deleteLogininforByIds(Long[] infoIds);

    /**
     * 清空系统登录日志
     */
    public void cleanLogininfor();

    /**
     * @Description: 保存登录日志信息
     * @author: tr
     * @Date: 2024/8/29 19:52
     * @param username 用户名
     * @param status 登录状态
     * @param message 登录信息
     * @returnValue: void
     */
    void saveLogininfor(String username, String status, String message);
}
