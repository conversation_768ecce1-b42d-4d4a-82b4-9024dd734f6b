package com.platform.system.controller;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.domain.request.SysEncRuleReq;
import com.platform.system.api.domain.response.SysEncRuleResp;
import com.platform.system.domain.vo.SysEncRule;
import com.platform.system.service.SysEncRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 *
 */

@Slf4j
@RestController
@RequestMapping("/encRule")
@Api(value = "加密规则表", tags = "加密规则表接口")
public class SysEncRuleController {

    @Autowired
    private SysEncRuleService sysEncRuleService;

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(true);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    public ResponseResult<SysEncRule> create(@RequestBody SysEncRule sysEncRule) throws URISyntaxException {
        log.debug("REST request to save sysEncRule : {}", sysEncRule);
        SysEncRule result = sysEncRuleService.create(sysEncRule);
        return ResponseResult.ok(result);
    }

    /**
     *  更新存在数据.
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResponseResult<SysEncRule> update(@RequestBody SysEncRule sysEncRule) {
        log.debug("REST request to update sysEncRule : {}", sysEncRule);
	    Assert.notNull(sysEncRule.getId(), "general.IdNotNull");
        int count = sysEncRuleService.update(sysEncRule);
        Assert.isTrue(count > 0, "general.updateFail");
        return ResponseResult.ok(sysEncRule);
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResponseResult<Void> delete(@PathVariable Integer id) {
        log.debug("REST request to delete sysEncRule : {}",  id);
        int count = sysEncRuleService.delete(id);
        Assert.isTrue(count > 0, "general.deleteFail");
        return ResponseResult.ok();
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResponseResult<SysEncRule> get(@PathVariable Integer id) {
        log.debug("REST request to get sysEncRule : {}",  id);
        SysEncRule sysEncRule = sysEncRuleService.findOne(id);
        return ResponseResult.ok(sysEncRule);
    }

    /**
    *  翻页查询多条数据.
    */
    @PostMapping("/page")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResponseResult<List<SysEncRule>> page(@RequestBody SysEncRule sysEncRule, Pageable pageable) {
        log.debug("REST request to get sysEncRule page: {}", sysEncRule);
        return sysEncRuleService.page(sysEncRule, pageable);
    }

   /**
    * 查询多条数据.
    */
   @PostMapping("/list")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResponseResult<List<SysEncRuleResp>> list(@RequestBody SysEncRuleReq sysEncRuleReq) {
       log.debug("REST request to get sysEncRule list: {}", sysEncRuleReq);
       List<SysEncRule> list = sysEncRuleService.findList(BeanUtil.copyProperties(sysEncRuleReq, SysEncRule.class));
       return ResponseResult.ok(BeanUtil.copyToList(list, SysEncRuleResp.class));
   }

}
