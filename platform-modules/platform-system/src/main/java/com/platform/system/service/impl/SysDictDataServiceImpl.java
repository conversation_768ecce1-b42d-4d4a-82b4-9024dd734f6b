package com.platform.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.system.api.domain.request.SysDictDataPageReq;
import com.platform.system.api.domain.request.SysDictTypePageReq;
import com.platform.system.api.domain.response.SysDictDataResp;
import com.platform.system.api.domain.response.SysDictTypeResp;
import com.platform.system.api.model.SysDictDataDTO;
import com.platform.system.convert.SysDictDataConvert;
import com.platform.system.domain.request.SysDictDataReq;
import com.platform.system.service.SysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.platform.common.security.utils.DictUtils;
import com.platform.system.api.domain.SysDictData;
import com.platform.system.mapper.SysDictDataMapper;
import com.platform.system.service.SysDictDataService;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl extends ServiceImpl<SysDictDataMapper, SysDictData> implements SysDictDataService
{
    @Autowired
    private SysDictDataMapper dictDataMapper;

    @Autowired
    @Lazy
    private SysDictTypeService sysDictTypeService;

    /**
     * 根据条件分页查询字典数据
     * 
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictDataResp> selectDictDataList(SysDictDataPageReq sysDictDataPageReq)
    {
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getDictType, sysDictDataPageReq.getDictType());
        queryWrapper.like(StrUtil.isNotBlank(sysDictDataPageReq.getDictLabel()),
                SysDictData::getDictLabel, sysDictDataPageReq.getDictLabel());
        queryWrapper.eq(StrUtil.isNotBlank(sysDictDataPageReq.getStatus()),
                SysDictData::getStatus, sysDictDataPageReq.getStatus());
        queryWrapper.orderByAsc(SysDictData::getDictSort);
        List<SysDictData> list = list(queryWrapper);
        List<SysDictDataResp> sysDictDataRespList = SysDictDataConvert.INSTANCE.doListToRespList(list);
        return sysDictDataRespList;
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     * 
     * @param dictType 字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue)
    {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     * 
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictDataResp selectDictDataById(Long dictCode)
    {
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getDictCode, dictCode);
        SysDictData sysDictData = getOne(queryWrapper);
        SysDictDataResp sysDictDataResp = SysDictDataConvert.INSTANCE.doToResp(sysDictData);
        return sysDictDataResp;
    }

    /**
     * 批量删除字典数据信息
     * 
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes)
    {
        for (Long dictCode : dictCodes)
        {
            SysDictDataResp data = selectDictDataById(dictCode);
            dictDataMapper.deleteDictDataById(dictCode);
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            List<SysDictDataDTO> sysDictDataDTOList = SysDictDataConvert.INSTANCE.doToDTOList(dictDatas);
            DictUtils.setDictCache(data.getDictType(), sysDictDataDTOList);
        }
    }

    /**
     * 新增保存字典数据信息
     * 
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int insertDictData(SysDictData data)
    {
        List<SysDictDataDTO> list = DictUtils.getDictCache(data.getDictType());
        if (ObjectUtil.isNotEmpty(list) && list.stream().anyMatch(d -> StrUtil.equals(d.getDictValue(), data.getDictValue()))){
            throw new ServiceException("字典键值已存在！");
        }
        boolean flag = save(data);
        if (flag)
        {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            List<SysDictDataDTO> sysDictDataDTOList = SysDictDataConvert.INSTANCE.doToDTOList(dictDatas);
            DictUtils.setDictCache(data.getDictType(), sysDictDataDTOList);
        }
        return flag ? 1 : 0;
    }

    /**
     * 修改保存字典数据信息
     * 
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int updateDictData(SysDictData data)
    {
        List<SysDictDataDTO> list = DictUtils.getDictCache(data.getDictType());
        if (list.stream().anyMatch(d -> (StrUtil.equals(d.getDictValue(), data.getDictValue())
                && !ObjectUtil.equal(d.getDictCode(), data.getDictCode())))){
            throw new ServiceException("字典键值已存在！");
        }
        boolean flag = updateById(data);
        if (flag){
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            List<SysDictDataDTO> sysDictDataDTOList = SysDictDataConvert.INSTANCE.doToDTOList(dictDatas);
            DictUtils.setDictCache(data.getDictType(), sysDictDataDTOList);
        }
        return flag ? 1 : 0;
    }

    @Override
    public String batchSaveDictData(List<SysDictDataReq> sysDictDataReqList) {
        if (ObjectUtil.isEmpty(sysDictDataReqList)){
            throw new ServiceException("导入数据不能为空！");
        }
        //判断导入的字典类型是否已存在数据库中
        List<String> dictTypeStrList = sysDictDataReqList.stream().distinct().map(d -> d.getDictType()).collect(Collectors.toList());

        SysDictTypePageReq sysDictTypePageReq = new SysDictTypePageReq();
        sysDictTypePageReq.setDictTypeList(dictTypeStrList);
        List<SysDictTypeResp> selectDictTypeList = sysDictTypeService.selectDictTypeList(sysDictTypePageReq);
        List<String> dictTypeStrListDb = selectDictTypeList.stream().map(d -> d.getDictType()).collect(Collectors.toList());
        List<String> dictTypeNoList = new ArrayList<>();
        sysDictDataReqList.forEach(d ->{
            if (!dictTypeStrListDb.contains(d.getDictType())){
                dictTypeNoList.add(d.getDictType());
            }
        });
        if (dictTypeNoList.size()>0){
            String dictTypeStr = String.join(Constants.COMMA, dictTypeNoList);
            throw new ServiceException("字典类型["+ dictTypeStr +"]不存在，请核对导入的数据！");
        }

        List<SysDictData> list = SysDictDataConvert.INSTANCE.reqListToDoList(sysDictDataReqList);

        saveBatch(list);
        return "导入成功！";
    }
}
