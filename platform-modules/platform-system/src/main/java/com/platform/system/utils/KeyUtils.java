package com.platform.system.utils;

import cn.hutool.core.util.RandomUtil;
import com.ctdi.base.core.exception.ServiceException;
import com.platform.common.core.constant.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: tr
 * @date: 2024年04月01日 9:39
 */
@Slf4j
public class KeyUtils {

    public static final String RSA_ALGORITHM = "RSA";

    /**
     * 生成国密公私钥对
     */
    public static Map<String, String> generateSmKey() {
        return generateSm4Key();
    }

    /**
     * @Description: 生成RSA算法的密钥对
     * @author: tr
     * @Date: 2024/4/9 9:33
     * @param: []
     * @returnValue: java.util.Map<java.lang.String,java.lang.String>
     */
    private static Map<String, String> generateRsaKey() {
        try{
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyGen.initialize(2048); // 设置密钥长度为2048位

            KeyPair keyPair = keyGen.generateKeyPair();
            RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
            RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();

            String publicKey = Base64.getEncoder().encodeToString(rsaPublicKey.getEncoded());
            String privateKey = Base64.getEncoder().encodeToString(rsaPrivateKey.getEncoded());
            log.info("publicKey:{}", publicKey);
            log.info("privateKey:{}", privateKey);
            Map<String, String> result = new HashMap<>();
            result.put(SecurityConstants.PUBLIC_KEY, publicKey);
            result.put(SecurityConstants.PRIVATE_KEY, privateKey);
            return result;
        }catch (Exception e){
            throw new ServiceException("生成RSA密钥错误");
        }
    }

    /**
     * @Description: 生成国密SM2算法的密钥对
     * @author: tr
     * @Date: 2024/4/9 9:34
     * @param: []
     * @returnValue: java.util.Map<java.lang.String,java.lang.String>
     */
    private static Map<String, String> generateSm2Key() {
        try{
            KeyPairGenerator keyPairGenerator = null;
            SecureRandom secureRandom = new SecureRandom();
            ECGenParameterSpec sm2Spec = new ECGenParameterSpec("sm2p256v1");
            keyPairGenerator = KeyPairGenerator.getInstance("EC", new BouncyCastleProvider());
            keyPairGenerator.initialize(sm2Spec);
            keyPairGenerator.initialize(sm2Spec, secureRandom);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            PrivateKey privateKey = keyPair.getPrivate();
            PublicKey publicKey = keyPair.getPublic();
            String publicKeyStr = new String(Base64.getEncoder().encode(publicKey.getEncoded()));
            String privateKeyStr = new String(Base64.getEncoder().encode(privateKey.getEncoded()));

            Map<String, String> result = new HashMap<>();
            result.put(SecurityConstants.PUBLIC_KEY, publicKeyStr);
            result.put(SecurityConstants.PRIVATE_KEY, privateKeyStr);
            return result;
        }catch (Exception e){
            throw new ServiceException("生成国密Sm2密钥错误");
        }
    }

    /**
     * @Description: 生成国密SM4算法的密钥对，主要使用公钥进行加密，私钥用于接口鉴权的用途
     * @author: tr
     * @Date: 2024/4/9 9:34
     * @param: []
     * @returnValue: java.util.Map<java.lang.String,java.lang.String>
     */
    private static Map<String, String> generateSm4Key() {
        try{
            String publicKeyStr = RandomUtil.randomString(16);
            String privateKeyStr = RandomUtil.randomString(32);

            Map<String, String> result = new HashMap<>();
            result.put(SecurityConstants.PUBLIC_KEY, publicKeyStr);
            result.put(SecurityConstants.PRIVATE_KEY, privateKeyStr);
            return result;
        }catch (Exception e){
            throw new ServiceException("生成国密Sm2密钥错误");
        }
    }

    /**
     * 将Base64转码的公钥串，转化为公钥对象
     */
    public static PublicKey createPublicKey(String publicKey) {
        PublicKey publickey = null;
        try {
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
            publickey = keyFactory.generatePublic(publicKeySpec);
        } catch (Exception e) {
            log.error("将Base64转码的公钥串，转化为公钥对象异常：{}", e.getMessage(), e);
        }
        return publickey;
    }

    /**
     * 将Base64转码的私钥串，转化为私钥对象
     */
    public static PrivateKey createPrivateKey(String privateKey) {
        PrivateKey publickey = null;
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
            publickey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            log.error("将Base64转码的私钥串，转化为私钥对象异常：{}", e.getMessage(), e);
        }
        return publickey;
    }
}
