package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.response.SysApiResp;
import com.platform.system.api.model.SysApiDTO;
import com.platform.system.domain.SysApi;
import com.platform.system.domain.request.SysApiQueryReq;
import com.platform.system.domain.request.SysApiReq;

import java.util.List;
import java.util.Set;

/**
 * @Description: API权限配置Service接口
 * @author: tr
 * @date: 2024年02月29日 16:14
 */
public interface SysApiService extends IService<SysApi> {

    /**
     * @Description: 查询API权限配置信息
     * @author: tr
     * @Date: 2024/2/29 17:23
     * @param: [sysApiQueryReq]
     * @returnValue: java.util.List<com.platform.system.domain.response.SysApiResp>
     */
    List<SysApiResp> list(SysApiQueryReq sysApiQueryReq);

    /**
     * @Description: 根据角色ID查询API权限配置
     * @author: tr
     * @Date: 2024/3/20 17:15
     * @param: [roleId]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysApiResp>
     */
    List<SysApiResp> listByRoleId(Long roleId);

    /**
     * @Description: 根据ID查询API单个信息
     * @author: tr
     * @Date: 2024/3/21 11:10
     * @param: [id]
     * @returnValue: com.platform.system.api.domain.response.SysApiResp
     */
    SysApiResp getById(Long id);

    /**
     * @Description: 保存API权限配置
     * @author: tr
     * @Date: 2024/2/29 16:53
     * @param: [sysApiReq]
     * @returnValue: void
     */
    void save(SysApiReq sysApiReq);

    /**
     * @Description: 修改API权限配置
     * @author: tr
     * @Date: 2024/2/29 16:53
     * @param: [sysApiReq]
     * @returnValue: void
     */
    void update(SysApiReq sysApiReq);

    /**
     * @Description: 删除API权限配置
     * @author: tr
     * @Date: 2024/2/29 16:53
     * @param: [id]
     * @returnValue: void
     */
    void delete(Long id);

    /**
     * @Description: 根据角色ID查询API的权限字符集合
     * @author: tr
     * @Date: 2024/3/6 15:58
     * @param: roleIdList 角色ID集合
     * @returnValue: java.util.Set<java.lang.String>
     */
    List<SysApiDTO> selectApiPermsByRoleId(List<Long> roleIdList);

    /**
     * @Description: 保存角色与API的关系
     * @author: tr
     * @Date: 2024/3/20 17:03
     * @param: [sysApiReq]
     * @returnValue: void
     */
    void saveRoleAndApi(SysApiReq sysApiReq);

    /**
     * @Description: 批量保存角色与API的关系
     * @author: tr
     * @Date: 2024/3/20 20:27
     * @param: [sysApiReq]
     * @returnValue: void
     */
    void saveRoleAndApiBatch(SysApiReq sysApiReq);

    /**
     * @Description: 移除角色与权限的关系
     * @author: tr
     * @Date: 2024/3/20 20:04
     * @param: [sysApiReq]
     * @returnValue: void
     */
    void removeRoleAndApi(SysApiReq sysApiReq);
}
