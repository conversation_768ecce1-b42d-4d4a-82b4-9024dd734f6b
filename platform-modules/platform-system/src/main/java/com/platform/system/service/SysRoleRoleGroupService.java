package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysRoleRoleGroup;

/**
 * @Description: 角色与角色组关系Service接口
 * @author: tr
 * @date: 2024年03月01日 14:52
 */
public interface SysRoleRoleGroupService extends IService<SysRoleRoleGroup> {

    /**
     * @Description: 根据角色ID删除角色与角色组的关系
     * @author: tr
     * @Date: 2024/3/20 16:02
     * @param: [roleIds]
     * @returnValue: void
     */
    void removeByRoleIds(Long[] roleIds);
}
