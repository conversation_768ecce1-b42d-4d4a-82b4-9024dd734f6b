package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresNoLogin;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.domain.request.*;
import com.platform.system.domain.response.SmsValidateCodeResp;
import com.platform.system.domain.response.SysMessageResp;
import com.platform.system.service.SysMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 站内信消息前端控制器
 * @author: tr
 * @date: 2024年04月16日 15:20
 */
@Api(tags = "站内信消息前端控制器")
@RestController
@RequestMapping("/message")
public class SysMessageController extends BaseController {

    @Autowired
    private SysMessageService sysMessageService;

    @Log(title = "站内信管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增站内信消息",  notes = "站内信消息")
    @PostMapping("save")
    public ResponseResult save(@Validated @RequestBody SysMessageReq sysMessageReq){
        sysMessageService.save(sysMessageReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "分页查询站内信消息",  notes = "站内信消息")
    @GetMapping("page")
    public ResponseResult<PageResult<SysMessageResp>> page(SysMessagePageReq sysMessagePageReq){
        Page<SysMessageResp> page = PageUtils.startPage(sysMessagePageReq.getPageNum(), sysMessagePageReq.getPageSize());
        List<SysMessageResp> list = sysMessageService.list(sysMessagePageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "查询站内信消息详情",  notes = "站内信消息")
    @GetMapping("getById")
    public ResponseResult<SysMessageResp> getById(Long id){
        SysMessageResp sysMessageResp = sysMessageService.getById(id);
        return ResponseResult.ok(sysMessageResp);
    }

    @ApiOperation(value = "删除消息",  notes = "站内信消息")
    @RequiresPermissions("system:message:remove")
    @Log(title = "站内信管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    public ResponseResult delete(Long[] ids){
        sysMessageService.delete(ids);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "修改消息状态为已读",  notes = "站内信消息")
    @RequiresPermissions("system:message:updateStatus")
    @Log(title = "站内信管理", businessType = BusinessType.UPDATE)
    @PutMapping("updateStatus")
    public ResponseResult updateStatus(Long[] ids){
        sysMessageService.updateStatus(ids);
        return ResponseResult.ok();
    }

    @RequiresNoLogin
    @ApiOperation(value = "发送短信验证码（1-注册验证码）",  notes = "消息")
    @PostMapping("sendSmsValidate")
    public ResponseResult<String> sendSmsValidate(@RequestBody SmsValidateReq smsValidateReq){
        String smsUuid = sysMessageService.sendSmsValidate(smsValidateReq);
        return ResponseResult.ok(smsUuid);
    }

    @RequiresNoLogin
    @ApiOperation(value = "发送短信验证码，根据身份证号码对应的手机号码发送短信",  notes = "消息")
    @GetMapping("sendSmsValidatePwd")
    public ResponseResult<String> sendSmsValidatePwd(String uuid, String certificateNo){
        String uuidNew = sysMessageService.sendSmsValidatePwd(uuid, certificateNo);
        return ResponseResult.ok(uuidNew);
    }

    @RequiresNoLogin
    @ApiOperation(value = "检查短信验证码",  notes = "消息")
    @PostMapping("checkSmsValidateCode")
    public ResponseResult<SmsValidateCodeResp> checkSmsValidateCode(@RequestBody SmsValidateCodeReq smsValidateCodeReq){
        SmsValidateCodeResp smsValidateCodeResp = sysMessageService.checkSmsValidateCode(smsValidateCodeReq);
        return ResponseResult.ok(smsValidateCodeResp);
    }

    @ApiOperation(value = "登录成功，修改密码发送短信验证码",  notes = "消息")
    @PostMapping("sendSmsValidateUpdatePwd")
    public ResponseResult<String> sendSmsValidateUpdatePwd(@RequestBody UpdatePwdMessageReq updatePwdMessageReq){
        String uuidNew = sysMessageService.sendSmsValidateUpdatePwd(updatePwdMessageReq);
        return ResponseResult.ok(uuidNew);
    }

    @ApiOperation(value = "发送短信验证码，返回加密盐值",  notes = "消息")
    @PostMapping("sendSmsValidateEncry")
    public ResponseResult<SmsValidateCodeResp> sendSmsValidateEncry(@RequestBody SmsValidateReq smsValidateReq){
        SmsValidateCodeResp smsValidateCodeResp = sysMessageService.sendSmsValidateEncry(smsValidateReq);
        return ResponseResult.ok(smsValidateCodeResp);
    }
}
