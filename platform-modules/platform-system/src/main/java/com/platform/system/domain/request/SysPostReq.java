package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 岗位表 sys_post
 * 
 * <AUTHOR>
 */
@Data
public class SysPostReq
{
    private static final long serialVersionUID = 1L;

    /** 岗位ID */
    @ApiModelProperty(value = "岗位ID")
    private Long postId;

    /** 岗位编码 */
    @ApiModelProperty(value = "岗位编码")
    @NotBlank(message = "岗位编码不能为空")
    @Size(min = 0, max = 64, message = "岗位编码长度不能超过64个字符")
    private String postCode;

    /** 岗位名称 */
    @ApiModelProperty(value = "岗位名称")
    @NotBlank(message = "岗位名称不能为空")
    @Size(min = 0, max = 50, message = "岗位名称长度不能超过50个字符")
    private String postName;

    /** 岗位排序 */
    @ApiModelProperty(value = "岗位排序")
    @NotNull(message = "显示顺序不能为空")
    private Integer postSort;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1关闭）")
    private String status;

    /** 用户是否存在此岗位标识 默认不存在 */
    @ApiModelProperty(value = "用户是否存在此岗位标识 默认不存在")
    private boolean flag = false;

}
