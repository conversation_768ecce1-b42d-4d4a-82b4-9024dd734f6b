package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysMessage;
import com.platform.system.domain.request.*;
import com.platform.system.domain.response.SmsValidateCodeResp;
import com.platform.system.domain.response.SysMessageResp;

import java.util.List;

/**
 * @Description: 站内信消息Service接口层
 * @author: tr
 * @date: 2024年04月16日 15:12
 */
public interface SysMessageService extends IService<SysMessage> {

    /**
     * @Description: 新增站内信
     * @author: tr
     * @Date: 2024/4/16 16:09
     * @param sysMessageReq
     * @returnValue: void
     */
    void save(SysMessageReq sysMessageReq);

    /**
     * @Description: 查询站内信消息
     * @author: tr
     * @Date: 2024/4/16 16:21
     * @param: [sysMessagePageReq]
     * @returnValue: java.util.List<com.platform.system.domain.response.SysMessageResp>
     */
    List<SysMessageResp> list(SysMessagePageReq sysMessagePageReq);

    /**
     * @Description: 查询站内信消息详情，并将消息标记为已读
     * @author: tr
     * @Date: 2024/4/16 17:15
     * @param: [id]
     * @returnValue: com.platform.system.domain.response.SysMessageResp
     */
    SysMessageResp getById(Long id);

    /**
     * @Description: 批量或单个删除消息
     * @author: tr
     * @Date: 2024/4/16 17:38
     * @param: [ids]
     * @returnValue: void
     */
    void delete(Long[] ids);

    /**
     * @Description: 修改消息状态为已读
     * @author: tr
     * @Date: 2024/4/16 17:46
     * @param: [ids]
     * @returnValue: void
     */
    void updateStatus(Long[] ids);

    /**
     * @Description: 根据创建时间倒序查询最近的数据
     * @author: tr
     * @Date: 2024/4/17 11:35
     * @param: [userId]
     * @returnValue: java.util.List<com.platform.system.domain.response.SysMessageResp>
     */
    List<SysMessageResp> listLastLimit(Long userId);
    
    /**
     * @Description: 发送短信验证码（1-注册验证码）
     * @author: tr
     * @Date: 2024/5/21 19:44
     * @param: [type]
     * @returnValue: void
     */
    String sendSmsValidate(SmsValidateReq smsValidateReq);

    /**
     * @Description: 忘记密码发送短信验证码，根据唯一UUID和身份证号码进行判断，其中uuid从校验身份证号码接口获取
     * @author: tr
     * @Date: 2024/6/11 20:20
     * @param uuid 唯一UUID
     * @param certificateNo 身份证号
     * @returnValue: java.lang.String
     */
    String sendSmsValidatePwd(String uuid, String certificateNo);

    /**
     * @Description: 校验短信验证码的正确与否
     * @author: tr
     * @Date: 2024/6/12 11:25
     * @param: [smsValidateCodeReq]
     * @returnValue: void
     */
    SmsValidateCodeResp checkSmsValidateCode(SmsValidateCodeReq smsValidateCodeReq);

    /**
     * @Description: 登录成功，修改密码发送短信验证码；判断旧密码是否正确，新密码和确认密码是否一致
     * @author: tr
     * @Date: 2024/7/12 10:27
     * @param: [updatePwdMessageReq]
     * @returnValue: java.lang.String
     */
    String sendSmsValidateUpdatePwd(UpdatePwdMessageReq updatePwdMessageReq);

    /**
     * @Description: 发送短信验证码，返回加密盐值
     * @author: tr
     * @Date: 2025/3/5 17:44
     * @param: [smsValidateReq]
     * @returnValue: com.platform.system.domain.response.SmsValidateCodeResp
     */
    SmsValidateCodeResp sendSmsValidateEncry(SmsValidateReq smsValidateReq);
}
