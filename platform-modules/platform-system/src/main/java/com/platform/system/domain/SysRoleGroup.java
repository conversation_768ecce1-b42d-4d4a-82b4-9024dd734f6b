package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

@Data
@ToString
@TableName("sys_role_group")
public class SysRoleGroup  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 角色组名称
	 */
	private String name;

	/**
	 * 角色组编码
	 */
	private String code;

	/**
	 * 组织机构类型
	 */
	private Integer orgType;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态（0正常 1停用）
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableField(
		value = "del_flag",
		fill = FieldFill.INSERT
	)
	private String delFlag;

}
