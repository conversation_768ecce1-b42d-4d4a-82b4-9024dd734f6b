package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.security.annotation.InnerAuth;
import com.platform.system.api.domain.request.SysDataScopeReq;
import com.platform.system.service.SysDataScopeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 数据权限前端控制器
 * @author: tr
 * @date: 2024年06月25日 8:50
 */
@RestController
@RequestMapping("/datascope")
public class SysDataScopeController extends BaseController {

    @Autowired
    private SysDataScopeService sysDataScopeService;

    /**
     * @Description: 新增数据权限，只允许内部访问
     * @author: tr
     * @Date: 2024/6/25 8:52
     * @param: []
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult
     */
    @InnerAuth
    @PostMapping("saveBatch")
    public ResponseResult saveBatch(@RequestBody List<SysDataScopeReq> sysDataScopeReqList){
        sysDataScopeService.saveBatch(sysDataScopeReqList);
        return ResponseResult.ok();
    }

    /**
     * @Description: 删除数据权限，只允许内部访问
     * @author: tr
     * @Date: 2024/6/25 8:54
     * @param: []
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult
     */
    @InnerAuth
    @DeleteMapping("deleteByDataId")
    public ResponseResult deleteByDataId(Long[] dataIds, String dataType){
        sysDataScopeService.deleteByDataId(dataIds, dataType);
        return ResponseResult.ok();
    }
}
