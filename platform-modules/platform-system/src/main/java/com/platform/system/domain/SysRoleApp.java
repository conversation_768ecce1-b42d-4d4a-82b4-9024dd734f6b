package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 角色与应用关系表
 * @author: tr
 * @date: 2024年03月29日 14:23
 */
@Data
@ToString
@TableName("sys_role_app")
public class SysRoleApp extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableField(
        value = "del_flag",
        fill = FieldFill.INSERT
    )
    private String delFlag;

    @TableField(exist = false)
    private String remark;
}
