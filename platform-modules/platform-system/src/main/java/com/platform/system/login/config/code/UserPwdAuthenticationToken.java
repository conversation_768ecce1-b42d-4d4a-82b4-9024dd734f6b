package com.platform.system.login.config.code;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * @Description: 用户密码认证令牌
 * @author: tr
 * @date: 2024年08月26日 14:45
 */
public class UserPwdAuthenticationToken extends AbstractAuthenticationToken {

    private static final long serialVersionUID = 2387092775910246006L;

    private final Object principal;

    public UserPwdAuthenticationToken(Object object) {
        super(null);
        this.principal = object;
        setAuthenticated(false);
    }

    public UserPwdAuthenticationToken(Object principal,
                                      Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    /**
     * @param isAuthenticated
     * @throws IllegalArgumentException
     */
    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                    "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }
        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
    }
}
