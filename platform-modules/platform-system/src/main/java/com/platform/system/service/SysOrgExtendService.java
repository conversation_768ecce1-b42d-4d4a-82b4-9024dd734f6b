package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.response.SysOrgExtendResp;
import com.platform.system.domain.SysOrgExtend;

/**
 * @Description:
 * @author: tr
 * @date: 2024年05月15日 11:27
 */
public interface SysOrgExtendService extends IService<SysOrgExtend> {

    /**
     * @Description: 通过组织机构ID获取组织机构扩展信息
     * @author: tr
     * @Date: 2024/5/15 16:36
     * @param: [orgId]
     * @returnValue: com.platform.system.domain.SysOrgExtend
     */
    SysOrgExtendResp getOneById(Long orgId);
}
