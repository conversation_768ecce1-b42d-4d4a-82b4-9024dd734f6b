package com.platform.system.login.service.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ctdi.base.core.exception.ServiceException;
import com.platform.system.api.model.LoginUser;
import com.platform.system.convert.SysUserConvert;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;


/**
 * @Description: 单点登录用户详情服务实现类
 * @author: tr
 * @date: 2025年02月14日 10:07
 */
@Slf4j
@Service
public class SingleUserDetailServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserService sysUserService;

    @Value("${single.loginUrl}")
    private String loginUrl;

    @Value("${single.clientId}")
    private String clientId;

    @Value("${single.clientSecret}")
    private String clientSecret;

    @Override
    public SysUserDetails loadUserByUsername(String token) throws UsernameNotFoundException {
        // 校验Token的有效性
        String decryptedData = decryptSm4(token, clientSecret);
        Map<String, String> headers = buildHeaders(decryptedData, clientId, new HashMap<>());
        HttpRequest request = HttpUtil.createPost(loginUrl).headerMap(headers, false);

        HttpResponse response = request.execute();
        if (response.getStatus() != 200){
            //登录失败
            log.error("调用token校验接口失败!{}-{}", response.getStatus(), response.body());
            throw new ServiceException("登录失败，请重新登录!");
        }
        String result = response.body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String resultCode = jsonObject.getStr("resultCode");
        if (StrUtil.equals(resultCode, "20000")){
            //根据Token获取用户信息成功
            String encryptedData = jsonObject.getStr("resultData");
            String resultData = decryptSm4(encryptedData, clientSecret);
            JSONObject resultDataJson = JSONUtil.parseObj(resultData);
            LoginUser loginUser = sysUserService.loginUserInfo(resultDataJson.getStr("userName"));

            SysUserDetails sysUserDetails = SysUserConvert.INSTANCE.loginUserToSysUserDetails(loginUser);
            return sysUserDetails;
        }
        log.error("调用获取用户信息接口，未获取到用户信息");
        throw new ServiceException("登录失败，请重新登录!");
    }

    private String decryptSm4(String ciphertext, String key) {
        SM4 sm4 = new SM4(Mode.ECB, Padding.PKCS5Padding, key.getBytes());
        String result = null;
        try {
            result = sm4.decryptStr(ciphertext, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ServiceException("登录失败无权限");
        }
        return result;
    }

    private Map<String, String> buildHeaders(String xbtToken,String clientId, Map<String, String> body) {
        Long timestamp = System.currentTimeMillis();
        String nonceStr = generateNonceStr();
        String sign;
        try {
            sign = generateSign(timestamp, nonceStr, body, clientSecret);
        } catch (UnsupportedEncodingException e) {
            log.error("Failed to generate sign", e);
            throw new RuntimeException("Signature generation failed");
        }
        Map<String, String> headers = new HashMap<>();
        if (xbtToken != null) {
            headers.put("xbtToken", xbtToken);
        }
        headers.put("clientId", clientId);
        headers.put("timestamp", timestamp.toString());
        headers.put("nonceStr", nonceStr);
        headers.put("sign", sign);
        return headers;
    }

    private String generateSign(Long timestamp, String nonceStr, Map<String, String> body, String clientSecret) throws UnsupportedEncodingException {
        // 使用 TreeMap 按键名排序，确保签名一致性
        SortedMap<String, String> hash = new TreeMap<>();
        hash.put("timestamp", timestamp.toString());
        hash.put("nonceStr", nonceStr);
        hash.put("body", com.alibaba.fastjson.JSON.toJSONString(sortMap(body)));

        // 拼接签名字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : hash.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            // 排除空值、sign 和 key 字段
            if (v != null && !v.isEmpty() && !"sign".equals(k) && !"key".equals(k)) {
                sb.append(k).append("=").append(URLEncoder.encode(v, "UTF-8")).append("&");
            }
        }
        sb.append("security=").append(clientSecret);

        // 生成并返回 MD5 签名（大写）
        return DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase();
    }

    private TreeMap<String, Object> sortMap(Map<String, String> body) {
        TreeMap<String, Object> treeMap = new TreeMap<>(body != null ? body : new HashMap<>(0));
        treeMap.forEach((k, v) -> {
            if (v instanceof Map) {
                // 递归处理嵌套 Map
                TreeMap<String, Object> value = sortMap(com.alibaba.fastjson.JSON.parseObject(JSON.toJSONString(v), Map.class));
                treeMap.put(k, value);
            }
        });
        return treeMap;
    }

    public String generateNonceStr() {
        return UUID.randomUUID().toString();
    }
}
