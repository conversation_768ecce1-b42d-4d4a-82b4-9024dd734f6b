package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysUserOrg;

/**
 * @Description: 用户与组织机构Service接口
 * @author: tr
 * @date: 2024年03月18日 20:20
 */
public interface SysUserOrgService extends IService<SysUserOrg> {

    /**
     * @Description: 根据组织机构ID判断是否存在与用户的关系
     * @author: tr
     * @Date: 2024/3/20 16:14
     * @param: [orgId]
     * @returnValue: true--存在，false--不存在
     */
    boolean existByOrgId(Long orgId);
    
    /**
     * @Description: 根据用户ID删除用户与组织机构的关系
     * @author: tr
     * @Date: 2024/3/22 10:52
     * @param: [userId]
     * @returnValue: void
     */
    void deleteByUserId(Long userId);
    
    /**
     * @Description: 根据用户ID查询组织机构的ID数组
     * @author: tr
     * @Date: 2024/3/22 11:28
     * @param: [userId]
     * @returnValue: java.lang.Long[]
     */
    Long[] listByUserId(Long userId);
}
