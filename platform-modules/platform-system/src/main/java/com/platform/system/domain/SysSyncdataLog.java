package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Description: 同步日志记录表
 * @author: tr
 * @date: 2025年03月26日 17:54
 */
@Data
@ToString
@TableName("sys_syncdata_log")
public class SysSyncdataLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 数据ID **/
    private String dataId;

    /** 同步类型（user，dept） **/
    private String syncType;

    /** 同步时间 **/
    private Date syncTime;

    /** 同步状态（0-待同步，1-已下发，9-同步成功，8-下发失败） **/
    private Integer syncStatus;

    /** 批次号 **/
    private String batchNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableField(
            value = "del_flag",
            fill = FieldFill.INSERT
    )
    private String delFlag;
}
