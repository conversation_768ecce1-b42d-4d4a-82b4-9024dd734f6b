package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.api.domain.response.SysApiResp;
import com.platform.system.domain.request.SysApiQueryReq;
import com.platform.system.domain.request.SysApiReq;
import com.platform.system.service.SysApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: API权限配置前端控制器
 * @author: tr
 * @date: 2024年02月29日 16:47
 */
@Api(tags = "API权限配置前端控制器")
@RestController
@RequestMapping("/api")
public class SysApiController extends BaseController {

    @Autowired
    private SysApiService sysApiService;

    @RequiresPermissions("system:api:list")
    @ApiOperation(value = "查询API权限配置",  notes = "API权限配置")
    @GetMapping("list")
    public ResponseResult<List<SysApiResp>> list(SysApiQueryReq sysApiQueryReq){
        List<SysApiResp> list = sysApiService.list(sysApiQueryReq);
        return ResponseResult.ok(list);
    }

    @RequiresPermissions("system:api:listByRoleId")
    @ApiOperation(value = "根据角色ID查询API权限配置",  notes = "API权限配置")
    @GetMapping("listByRoleId")
    public ResponseResult<List<SysApiResp>> listByRoleId(Long roleId){
        List<SysApiResp> list = sysApiService.listByRoleId(roleId);
        return ResponseResult.ok(list);
    }

    @RequiresPermissions("system:api:getById")
    @ApiOperation(value = "根据ID查询API权限配置",  notes = "API权限配置")
    @GetMapping("getById")
    public ResponseResult<SysApiResp> getById(Long id){
        SysApiResp sysApiResp = sysApiService.getById(id);
        return ResponseResult.ok(sysApiResp);
    }

    @RequiresPermissions("system:api:save")
    @Log(title = "API权限管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增API权限配置",  notes = "API权限配置")
    @PostMapping("save")
    public ResponseResult save(@Validated @RequestBody SysApiReq sysApiReq){
        sysApiService.save(sysApiReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:api:update")
    @Log(title = "API权限管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改API权限配置",  notes = "API权限配置")
    @PostMapping("update")
    public ResponseResult update(@Validated @RequestBody SysApiReq sysApiReq){
        sysApiService.update(sysApiReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:api:delete")
    @Log(title = "API权限管理", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除API权限配置",  notes = "API权限配置")
    @DeleteMapping("delete")
    public ResponseResult delete(@RequestParam Long id){
        sysApiService.delete(id);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:api:saveRoleAndApi")
    @Log(title = "API权限管理-保存赋权角色", businessType = BusinessType.INSERT)
    @ApiOperation(value = "API权限管理-保存赋权角色",  notes = "API权限配置")
    @PostMapping("saveRoleAndApi")
    public ResponseResult saveRoleAndApi(@RequestBody SysApiReq sysApiReq){
        sysApiService.saveRoleAndApi(sysApiReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:api:saveRoleAndApiBatch")
    @Log(title = "API权限管理-保存赋权角色", businessType = BusinessType.INSERT)
    @ApiOperation(value = "API权限管理-批量保存赋权角色",  notes = "API权限配置")
    @PostMapping("saveRoleAndApiBatch")
    public ResponseResult saveRoleAndApiBatch(@RequestBody SysApiReq sysApiReq){
        sysApiService.saveRoleAndApiBatch(sysApiReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:api:removeRoleAndApi")
    @Log(title = "API权限管理-移除赋权角色", businessType = BusinessType.INSERT)
    @ApiOperation(value = "API权限管理-移除赋权角色",  notes = "API权限配置")
    @DeleteMapping("removeRoleAndApi")
    public ResponseResult removeRoleAndApi(SysApiReq sysApiReq){
        sysApiService.removeRoleAndApi(sysApiReq);
        return ResponseResult.ok();
    }
}
