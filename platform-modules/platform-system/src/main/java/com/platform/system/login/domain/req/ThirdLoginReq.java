package com.platform.system.login.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户登录对象
 * 
 * <AUTHOR>
 */
@Data
public class ThirdLoginReq
{
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;
    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;

    /** 第三方 appKey **/
    @ApiModelProperty(value = "第三方 appKey")
    private String appKey;


    /** 用户类型 **/
    @ApiModelProperty(value = "用户类型")
    private String userType;
}
