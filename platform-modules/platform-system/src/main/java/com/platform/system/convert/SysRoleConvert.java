package com.platform.system.convert;

import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.api.model.SysRoleDTO;
import com.platform.system.domain.request.SysRoleDataScopeReq;
import com.platform.system.domain.request.SysRoleReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 角色信息转换
 * @author: tr
 * @date: 2024年02月02日 16:05
 */
@Mapper
public interface SysRoleConvert {

    SysRoleConvert INSTANCE = Mappers.getMapper(SysRoleConvert.class);

    SysRole queryReqToDO(SysRoleQueryReq sysRoleQueryReq);

    SysRoleResp doToResp(SysRole sysRole);

    List<SysRoleResp> doListToRespList(List<SysRole> sysRoleList);

    SysRoleDTO doToDTO(SysRole sysRole);
    List<SysRoleDTO> doToDTOList(List<SysRole> sysRoleList);

    SysRole dataScopeReqToDO(SysRoleDataScopeReq sysRoleDataScopeReq);

    SysRole reqToDO(SysRoleReq sysRoleReq);
}
