package com.platform.system.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.system.api.domain.request.SysDictTypePageReq;
import com.platform.system.api.domain.response.SysDictDataResp;
import com.platform.system.api.domain.response.SysDictTypeResp;
import com.platform.system.api.model.SysDictDataDTO;
import com.platform.system.convert.SysDictDataConvert;
import com.platform.system.convert.SysDictTypeConvert;
import com.platform.system.domain.request.SysDictTypeReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.security.utils.DictUtils;
import com.platform.system.api.domain.SysDictData;
import com.platform.system.api.domain.SysDictType;
import com.platform.system.mapper.SysDictDataMapper;
import com.platform.system.mapper.SysDictTypeMapper;
import com.platform.system.service.SysDictTypeService;
import org.springframework.util.CollectionUtils;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysDictTypeServiceImpl extends ServiceImpl<SysDictTypeMapper, SysDictType> implements SysDictTypeService
{
    @Autowired
    private SysDictTypeMapper dictTypeMapper;

    @Autowired
    private SysDictDataMapper dictDataMapper;

    /**
     * 项目启动时，初始化字典到缓存
     */
    @PostConstruct
    public void init()
    {
        loadingDictCache();
    }

    /**
     * 根据条件分页查询字典类型
     * 
     * @param dictType 字典类型信息
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictTypeResp> selectDictTypeList(SysDictTypePageReq sysDictTypePageReq)
    {
        LambdaQueryWrapper<SysDictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(sysDictTypePageReq.getDictName())
                , SysDictType::getDictName, sysDictTypePageReq.getDictName());
        queryWrapper.eq(StrUtil.isNotBlank(sysDictTypePageReq.getStatus())
                , SysDictType::getStatus, sysDictTypePageReq.getStatus());
        queryWrapper.like(StrUtil.isNotBlank(sysDictTypePageReq.getDictType())
                , SysDictType::getDictType, sysDictTypePageReq.getDictType());
        queryWrapper.between(StrUtil.isNotBlank(sysDictTypePageReq.getBeginTime()) && StrUtil.isNotBlank(sysDictTypePageReq.getEndTime())
                , SysDictType::getCreateTime
                , sysDictTypePageReq.getBeginTime() + " 00:00:00", sysDictTypePageReq.getEndTime() + " 23:59:59");
        queryWrapper.in(ObjectUtil.isNotEmpty(sysDictTypePageReq.getDictTypeList()), SysDictType::getDictType, sysDictTypePageReq.getDictTypeList());

        List<SysDictType> sysDictTypeList = list(queryWrapper);
        List<SysDictTypeResp> list = SysDictTypeConvert.INSTANCE.doListToRespList(sysDictTypeList);
        return list;
    }

    @Override
    public List<SysDictTypeResp> selectDictTypeListAll(SysDictTypePageReq sysDictTypePageReq) {
        List<SysDictTypeResp> selectDictTypeList = this.selectDictTypeList(sysDictTypePageReq);
        List<SysDictData> sysDictDataList = dictDataMapper.selectList(new LambdaQueryWrapper<>());
        List<SysDictDataResp> sysDictDataRespList = SysDictDataConvert.INSTANCE.doListToRespList(sysDictDataList);
        Map<String, List<SysDictDataResp>> map =
                sysDictDataRespList.stream().collect(Collectors.groupingBy(SysDictDataResp::getDictType));
        selectDictTypeList.forEach(d ->{
            d.setSysDictDataRespList(MapUtil.get(map, d.getDictType(), List.class));
        });
        return selectDictTypeList;
    }

    /**
     * 根据所有字典类型
     * 
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictTypeResp> selectDictTypeAll()
    {
        List<SysDictType> list = list();
        List<SysDictTypeResp> respList = SysDictTypeConvert.INSTANCE.doListToRespList(list);
        return respList;
    }

    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataByType(String dictType)
    {
        List<SysDictDataDTO> dictDataDto = DictUtils.getDictCache(dictType);
        if (StringUtils.isNotEmpty(dictDataDto))
        {
            return SysDictDataConvert.INSTANCE.DTOToDoList(dictDataDto);
        }
        List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(dictType);
        if (StringUtils.isNotEmpty(dictDatas))
        {
            List<SysDictDataDTO> sysDictDataDTOList = SysDictDataConvert.INSTANCE.doToDTOList(dictDatas);
            DictUtils.setDictCache(dictType, sysDictDataDTOList);
            return dictDatas;
        }
        return null;
    }

    /**
     * 根据字典类型ID查询信息
     * 
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    @Override
    public SysDictTypeResp selectDictTypeById(Long dictId)
    {
        SysDictType sysDictType = getById(dictId);
        SysDictTypeResp sysDictTypeResp = SysDictTypeConvert.INSTANCE.doToResp(sysDictType);
        return sysDictTypeResp;
    }

    /**
     * 批量删除字典类型信息
     * 
     * @param dictIds 需要删除的字典ID
     */
    @Override
    public void deleteDictTypeByIds(Long[] dictIds)
    {
        for (Long dictId : dictIds)
        {
            SysDictTypeResp dictType = selectDictTypeById(dictId);
            if (dictDataMapper.countDictDataByType(dictType.getDictType()) > 0)
            {
                throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
            }
            dictTypeMapper.deleteDictTypeById(dictId);
            DictUtils.removeDictCache(dictType.getDictType());
        }
    }

    /**
     * 加载字典缓存数据
     */
    @Override
    public void loadingDictCache()
    {
        SysDictData dictData = new SysDictData();
        dictData.setStatus("0");
        Map<String, List<SysDictData>> dictDataMap = dictDataMapper.selectDictDataList(dictData).stream().collect(Collectors.groupingBy(SysDictData::getDictType));
        for (Map.Entry<String, List<SysDictData>> entry : dictDataMap.entrySet())
        {
            List<SysDictDataDTO> sysDictDataDTOList = SysDictDataConvert.INSTANCE.doToDTOList(entry.getValue());
            DictUtils.setDictCache(entry.getKey(), sysDictDataDTOList.stream().sorted(Comparator.comparing(SysDictDataDTO::getDictSort)).collect(Collectors.toList()));
        }
    }

    /**
     * 清空字典缓存数据
     */
    @Override
    public void clearDictCache()
    {
        DictUtils.clearDictCache();
    }

    /**
     * 重置字典缓存数据
     */
    @Override
    public void resetDictCache()
    {
        clearDictCache();
        loadingDictCache();
    }

    /**
     * 新增保存字典类型信息
     * 
     * @param dict 字典类型信息
     * @return 结果
     */
    @Override
    public int insertDictType(SysDictType dict)
    {
        boolean flag = save(dict);
        if (flag)
        {
            DictUtils.setDictCache(dict.getDictType(), null);
        }
        return flag ? 1 : 0;
    }

    /**
     * 修改保存字典类型信息
     * 
     * @param dict 字典类型信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDictType(SysDictType dict)
    {
        SysDictType oldDict = dictTypeMapper.selectDictTypeById(dict.getDictId());
        dictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
        boolean flag = updateById(dict);
        if (flag)
        {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(dict.getDictType());
            List<SysDictDataDTO> sysDictDataDTOList = SysDictDataConvert.INSTANCE.doToDTOList(dictDatas);
            if (ObjectUtil.isEmpty(sysDictDataDTOList)){
                sysDictDataDTOList = new ArrayList<>();
            }
            DictUtils.setDictCache(dict.getDictType(), sysDictDataDTOList);
        }
        return flag ? 1 : 0;
    }

    /**
     * 校验字典类型称是否唯一
     * 
     * @param dict 字典类型
     * @return 结果
     */
    @Override
    public boolean checkDictTypeUnique(SysDictType dict)
    {
        Long dictId = StringUtils.isNull(dict.getDictId()) ? -1L : dict.getDictId();
        SysDictType dictType = dictTypeMapper.checkDictTypeUnique(dict.getDictType());
        if (StringUtils.isNotNull(dictType) && dictType.getDictId().longValue() != dictId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 通过字典类型和字典值查询字段数据
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典数据
     */
    @Override
    public SysDictDataResp selectDictDataByDictTypeAndDictValue(String dictType, String dictValue) {
        if(StringUtils.isBlank(dictType) || StringUtils.isBlank(dictValue)){
            return null;
        }
        List<SysDictData> sysDictDataList = selectDictDataByType(dictType);
        if(CollectionUtils.isEmpty(sysDictDataList)){
            return null;
        }
        List<SysDictDataResp> list = SysDictDataConvert.INSTANCE.doListToRespList(sysDictDataList);
        return list.stream().filter(item -> item.getDictValue().equals(dictValue)).findFirst().orElse(null);
    }

    @Override
    public String batchSaveDictType(List<SysDictTypeReq> sysDictTypeReqList) {
        if (ObjectUtil.isEmpty(sysDictTypeReqList)){
            throw new ServiceException("导入数据不能为空！");
        }
        //判断字典类型是否重复
        //先判断上传的文件中字典类型是否重复
        Map<String, Long> dictTypeMap =
                sysDictTypeReqList.stream().collect(Collectors.groupingBy(SysDictTypeReq::getDictType, Collectors.counting()));
        dictTypeMap.forEach((key, value) -> {if (value > 1){
            throw new ServiceException("字典类型["+ key +"]不能重复，请核对导入的数据");
        }});
        //判断字典类型是否已在数据库中存在
        List<String> dictTypeStrList = sysDictTypeReqList.stream().map(d -> d.getDictType()).collect(Collectors.toList());
        LambdaQueryWrapper<SysDictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysDictType::getDictType, dictTypeStrList);
        List<SysDictType> dictTypeList = list(queryWrapper);
        if (ObjectUtil.isNotEmpty(dictTypeList)){
            dictTypeStrList = dictTypeList.stream().map(d -> d.getDictType()).collect(Collectors.toList());
            String dictTypeStr = String.join(Constants.COMMA, dictTypeStrList);
            throw new ServiceException("字典类型["+ dictTypeStr +"]已存在，请核对导入的数据！");
        }

        List<SysDictType> list = SysDictTypeConvert.INSTANCE.reqListToDoList(sysDictTypeReqList);

        saveBatch(list);
        return "导入成功！";
    }
}
