package com.platform.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.api.domain.response.SysMenuResp;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.convert.SysMenuConvert;
import com.platform.system.domain.SysApp;
import com.platform.system.domain.request.SysAppPageReq;
import com.platform.system.domain.request.SysMenuQueryReq;
import com.platform.system.domain.request.SysMenuReq;
import com.platform.system.domain.response.SysAppResp;
import com.platform.system.service.SysAppService;
import com.platform.system.service.SysRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysRole;
import com.platform.system.domain.SysMenu;
import com.platform.system.domain.vo.MetaVo;
import com.platform.system.domain.vo.RouterVo;
import com.platform.system.domain.vo.TreeSelect;
import com.platform.system.mapper.SysMenuMapper;
import com.platform.system.mapper.SysRoleMapper;
import com.platform.system.service.SysMenuService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 菜单 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService
{
    public static final String PREMISSION_STRING = "perms[\"{0}\"]";

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private CacheClient redisService;

    @Autowired
    private SysAppService sysAppService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    /**
     * 根据用户查询系统菜单列表
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    public List<SysMenuResp> selectMenuList(Long userId)
    {
        return selectMenuList(new SysMenuQueryReq(), userId);
    }

    /**
     * 查询系统菜单列表
     * 
     * @param sysMenuQueryReq 菜单信息
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    public List<SysMenuResp> selectMenuList(SysMenuQueryReq sysMenuQueryReq, Long userId)
    {
        List<SysMenuResp> list = listMenu(sysMenuQueryReq);
        list.forEach(m ->{
            if (ObjectUtil.equals(m.getParentId(), BusinessConstants.PARENT_ID)){
                m.setParentId(m.getAppId());
            }
        });
        //查询应用信息
        SysAppPageReq sysAppPageReq = new SysAppPageReq();
        sysAppPageReq.setType(sysMenuQueryReq.getType());
        List<SysAppResp> sysAppRespList = sysAppService.list(sysAppPageReq);
        sysAppRespList.forEach(a ->{
            SysMenuResp sysMenuResp = new SysMenuResp();
            sysMenuResp.setMenuId(a.getId());
            sysMenuResp.setParentId(BusinessConstants.PARENT_ID);
            sysMenuResp.setMenuName(a.getName());
            sysMenuResp.setOrderNum(a.getOrderNum());
            sysMenuResp.setAppId(a.getId());
            sysMenuResp.setMenuType(UserConstants.TYPE_APP);
            sysMenuResp.setCreateTime(a.getCreateTime());
            list.add(sysMenuResp);
        });
        return list;
    }

    @Override
    public List<SysMenuResp> listMenu(SysMenuQueryReq sysMenuQueryReq) {
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(sysMenuQueryReq.getMenuName()), SysMenu::getMenuName, sysMenuQueryReq.getMenuName());
        queryWrapper.eq(StrUtil.isNotBlank(sysMenuQueryReq.getVisible()), SysMenu::getVisible, sysMenuQueryReq.getVisible());
        queryWrapper.eq(StrUtil.isNotBlank(sysMenuQueryReq.getStatus()), SysMenu::getStatus, sysMenuQueryReq.getStatus());
        queryWrapper.eq(StrUtil.isNotBlank(sysMenuQueryReq.getType()), SysMenu::getType, sysMenuQueryReq.getType());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysMenuQueryReq.getAppId()), SysMenu::getAppId, sysMenuQueryReq.getAppId());
        queryWrapper.orderByAsc(SysMenu::getParentId, SysMenu::getOrderNum);
        List<SysMenu> menuList = list(queryWrapper);
        List<SysMenuResp> list = SysMenuConvert.INSTANCE.doListToRespList(menuList);
        return list;
    }

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(Long userId)
    {
        List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = perms.stream()
                .filter(StringUtils::isNotEmpty) // 过滤空字符串
                .flatMap(s -> Arrays.stream(s.trim().split(","))) // 将每个字符串分割并转换为流
                .collect(Collectors.toSet()); // 收集结果到Set中
        return permsSet;
    }

    /**
     * 根据角色ID查询权限
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByRoleId(Long roleId)
    {
        List<String> perms = menuMapper.selectMenuPermsByRoleId(roleId);
        Set<String> permsSet = perms.stream()
                .filter(StringUtils::isNotEmpty) // 过滤空字符串
                .flatMap(s -> Arrays.stream(s.trim().split(","))) // 将每个字符串分割并转换为流
                .collect(Collectors.toSet()); // 收集结果到Set中
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     * 
     * @param userId 用户名称
     * @return 菜单列表
     */
    @Override
    public List<SysMenuResp> selectMenuTreeByUserId(Long userId, String appCode)
    {
        Map<String, SysApp> codeObjMap = redisService.getCacheMap(CacheConstants.APP_CODE_OBJECT_MAP);
        SysApp sysApp = codeObjMap.get(appCode);
        Long appId = ObjectUtil.isNotNull(sysApp) ? sysApp.getId() : null;

        List<SysMenu> menus = null;
        if (SecurityUtils.isAdmin(SecurityUtils.getUserType()))
        {
            menus = menuMapper.selectMenuTreeAll(appId);
        }else{
            menus = menuMapper.selectMenuTreeByUserId(userId, appId);
        }
        List<SysMenuResp> list = SysMenuConvert.INSTANCE.doListToRespList(menus);
        return getChildPerms(list, 0);
    }

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return menuMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
    }

    /**
     * 构建前端路由所需要的菜单
     * 
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenuResp> menus)
    {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenuResp menu : menus)
        {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setQuery(menu.getQuery());
            router.setAppId(menu.getAppId());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
            List<SysMenuResp> cMenus = menu.getChildren();
            if (!cMenus.isEmpty() && cMenus.size() > 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType()))
            {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            }
            else if (isMenuFrame(menu))
            {
                router = new RouterVo();
                router.setPath(getRouterPath(menu));
                router.setComponent(menu.getComponent());
                router.setName(StringUtils.capitalize(menu.getPath()));
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
                router.setQuery(menu.getQuery());
            }
            else if (menu.getParentId().intValue() == 0 && isInnerLink(menu))
            {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<SysMenuResp> buildMenuTree(List<SysMenuResp> menus)
    {
        List<SysMenuResp> returnList = new ArrayList<>();
        List<Long> tempList = menus.stream().map(SysMenuResp::getMenuId).collect(Collectors.toList());
        for (Iterator<SysMenuResp> iterator = menus.iterator(); iterator.hasNext();)
        {
            SysMenuResp menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId()))
            {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenuResp> menus)
    {
        List<SysMenuResp> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @Override
    public SysMenuResp selectMenuById(Long menuId)
    {
        SysMenu sysMenu = getById(menuId);
        SysMenuResp sysMenuResp = SysMenuConvert.INSTANCE.doToResp(sysMenu);
        return sysMenuResp;
    }

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(Long menuId)
    {
        int result = menuMapper.hasChildByMenuId(menuId);
        return result > 0;
    }

    /**
     * 新增保存菜单信息
     * 
     * @param sysMenuReq 菜单信息
     * @return 结果
     */
    @Override
    public int insertMenu(SysMenuReq sysMenuReq)
    {
        SysMenu menu = SysMenuConvert.INSTANCE.reqToDO(sysMenuReq);

        if (!this.checkMenuNameUnique(menu))
        {
            throw new ServiceException("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
        {
            throw new ServiceException("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }

        boolean flag = save(menu);
        if (flag){
            freshCache();
            return 1;
        }
        return 0;
    }

    /**
     * 修改保存菜单信息
     * 
     * @param sysMenuReq 菜单信息
     * @return 结果
     */
    @Override
    public int updateMenu(SysMenuReq sysMenuReq)
    {
        SysMenu menu = SysMenuConvert.INSTANCE.reqToDO(sysMenuReq);

        if (!this.checkMenuNameUnique(menu))
        {
            throw new ServiceException("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
        {
            throw new ServiceException("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        else if (menu.getMenuId().equals(menu.getParentId()))
        {
            throw new ServiceException("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }

        menu.setQuery(StrUtil.isBlank(menu.getQuery()) ? "" : menu.getQuery());
        //parentId不允许修改
        menu.setParentId(null);
        boolean flag = updateById(menu);
        if (flag){
            freshCache();
            return 1;
        }
        return 0;
    }

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMenuById(Long menuId)
    {
        boolean flag = removeById(menuId);
        if (flag){
            //菜单删除成功，同时删除，菜单与角色的关联关系
            sysRoleMenuService.removeByMenuId(menuId);
        }
        freshCache();
        return flag ? 1 : 0;
    }

    /**
     * 校验菜单名称是否唯一
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean checkMenuNameUnique(SysMenu menu)
    {
        Long menuId = StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();

        //查询同一应用下菜单名称是否重复
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysMenu::getAppId, menu.getAppId());
        queryWrapper.eq(SysMenu::getMenuName, menu.getMenuName());
        queryWrapper.eq(SysMenu::getParentId, menuId);
        queryWrapper.last("limit 1");
        SysMenu info = getOne(queryWrapper);

        if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public List<SysMenuResp> listByAppCode(SysMenuQueryReq sysMenuQueryReq) {
        String appCode = sysMenuQueryReq.getAppCode();
        if (StrUtil.isNotBlank(appCode)){
            Map<String, Object> codeObjMap = redisService.getCacheMap(CacheConstants.APP_CODE_OBJECT_MAP);
            SysApp sysApp = (SysApp) codeObjMap.get(appCode);
            sysMenuQueryReq.setAppId(sysApp.getId());
        }
        List<SysMenuResp> list = listMenu(sysMenuQueryReq);
        return list;
    }

    /**
     * 获取路由名称
     * 
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenuResp menu)
    {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu))
        {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     * 
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenuResp menu)
    {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (menu.getParentId().intValue() != 0 && isInnerLink(menu))
        {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
                && UserConstants.NO_FRAME.equals(menu.getIsFrame()))
        {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu))
        {
            if (StrUtil.isNotBlank(menu.getPath())){
                if (menu.getPath().indexOf("/") != 0){
                    routerPath = "/" + menu.getPath();
                }
            }else{
                routerPath = "/";
            }
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     * 
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenuResp menu)
    {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu))
        {
            component = menu.getComponent();
        }
        else if (StringUtils.isEmpty(menu.getComponent()) && menu.getParentId().intValue() != 0 && isInnerLink(menu))
        {
            component = UserConstants.INNER_LINK;
        }
        else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu))
        {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(SysMenuResp menu)
    {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType())
                && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为内链组件
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(SysMenuResp menu)
    {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenuResp menu)
    {
        return menu.getParentId().intValue() != 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     * 
     * @param list 分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<SysMenuResp> getChildPerms(List<SysMenuResp> list, int parentId)
    {
        List<SysMenuResp> returnList = new ArrayList<>();
        for (Iterator<SysMenuResp> iterator = list.iterator(); iterator.hasNext();)
        {
            SysMenuResp t = iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId)
            {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     * 
     * @param list
     * @param t
     */
    private void recursionFn(List<SysMenuResp> list, SysMenuResp t)
    {
        // 得到子节点列表
        List<SysMenuResp> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenuResp tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenuResp> getChildList(List<SysMenuResp> list, SysMenuResp t)
    {
        List<SysMenuResp> tlist = new ArrayList<>();
        Iterator<SysMenuResp> it = list.iterator();
        while (it.hasNext())
        {
            SysMenuResp n =  it.next();
            if (n.getParentId().longValue() == t.getMenuId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenuResp> list, SysMenuResp t)
    {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 内链域名特殊字符替换
     * 
     * @return
     */
    public String innerLinkReplaceEach(String path)
    {
        return StringUtils.replaceEach(path, new String[] { Constants.HTTP, Constants.HTTPS, Constants.WWW, "." },
                new String[] { "", "", "", "/" });
    }

    /**
     * @Description: 刷新缓存信息
     * @author: tr
     * @Date: 2024/3/6 19:15
     * @param: []
     * @returnValue: void
     */
    private void freshCache() {
        List<SysMenu> menuList = menuMapper.selectMenuList(new SysMenu());
        List<SysMenuResp> list = SysMenuConvert.INSTANCE.doListToRespList(menuList);
        boolean flag = redisService.hasKey(CacheConstants.SYSTEM_MENU);
        if (flag){
            redisService.deleteObject(CacheConstants.SYSTEM_MENU);
        }
        redisService.setCacheList(CacheConstants.SYSTEM_MENU, list);
    }
}
