package com.platform.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.system.domain.request.SysPostPageReq;
import com.platform.system.domain.request.SysPostReq;
import com.platform.system.domain.response.SysPostResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.service.SysPostService;

/**
 * 岗位信息操作处理
 * 
 * <AUTHOR>
 */
@Api(tags = "岗位信息前端控制器")
@RestController
@RequestMapping("/post")
public class SysPostController extends BaseController
{
    @Autowired
    private SysPostService postService;

    /**
     * 获取岗位列表
     */
    @ApiOperation(value = "获取岗位列表",  notes = "岗位信息")
    @RequiresPermissions("system:post:list")
    @GetMapping("/list")
    public ResponseResult<PageResult<SysPostResp>> list(SysPostPageReq sysPostPageReq)
    {
        Page<SysPostResp> page = PageUtils.startPage(sysPostPageReq.getPageNum(), sysPostPageReq.getPageSize());
        List<SysPostResp> list = postService.selectPostList(sysPostPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "导出岗位",  notes = "岗位信息")
    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:post:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPostPageReq sysPostPageReq)
    {
        List<SysPostResp> list = postService.selectPostList(sysPostPageReq);
        ExcelUtil<SysPostResp> util = new ExcelUtil<>(SysPostResp.class);
        util.exportExcel(response, list, "岗位数据");
    }

    /**
     * 根据岗位编号获取详细信息
     */
    @ApiOperation(value = "根据岗位编号获取详细信息",  notes = "岗位信息")
    @RequiresPermissions("system:post:query")
    @GetMapping(value = "/getInfo")
    public ResponseResult getInfo(Long postId)
    {
        return ResponseResult.ok(postService.selectPostById(postId));
    }

    /**
     * 新增岗位
     */
    @ApiOperation(value = "新增岗位",  notes = "岗位信息")
    @RequiresPermissions("system:post:add")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ResponseResult add(@Validated @RequestBody SysPostReq sysPostReq)
    {
        if (!postService.checkPostNameUnique(sysPostReq))
        {
            return ResponseResult.fail("新增岗位'" + sysPostReq.getPostName() + "'失败，岗位名称已存在");
        }
        else if (!postService.checkPostCodeUnique(sysPostReq))
        {
            return ResponseResult.fail("新增岗位'" + sysPostReq.getPostName() + "'失败，岗位编码已存在");
        }
        return postService.insertPost(sysPostReq)>0?ResponseResult.ok():ResponseResult.fail("新增岗位失败！");
    }

    /**
     * 修改岗位
     */
    @ApiOperation(value = "修改岗位",  notes = "岗位信息")
    @RequiresPermissions("system:post:edit")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult edit(@Validated @RequestBody SysPostReq sysPostReq)
    {
        if (!postService.checkPostNameUnique(sysPostReq))
        {
            return ResponseResult.fail("修改岗位'" + sysPostReq.getPostName() + "'失败，岗位名称已存在");
        }
        else if (!postService.checkPostCodeUnique(sysPostReq))
        {
            return ResponseResult.fail("修改岗位'" + sysPostReq.getPostName() + "'失败，岗位编码已存在");
        }
        return postService.updatePost(sysPostReq)>0?ResponseResult.ok():ResponseResult.fail("修改岗位失败！");
    }

    /**
     * 删除岗位
     */
    @ApiOperation(value = "删除岗位",  notes = "岗位信息")
    @RequiresPermissions("system:post:remove")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam("postIds") Long[] postIds)
    {
        return postService.deletePostByIds(postIds)>0?ResponseResult.ok():ResponseResult.fail("删除岗位失败！");
    }

    /**
     * 获取岗位选择框列表
     */
    @ApiOperation(value = "获取岗位选择框列表",  notes = "岗位信息")
    @GetMapping("/optionselect")
    public ResponseResult<List<SysPostResp>> optionselect()
    {
        List<SysPostResp> posts = postService.selectPostAll();
        return ResponseResult.ok(posts);
    }
}
