package com.platform.system.openapi.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.openapi.service.MenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 菜单OpenApi接口Controller层
 * @author: tr
 * @date: 2024年08月21日 16:34
 */
@RestController
@RequestMapping("/openapi/v1/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;

    @GetMapping("/listAll")
    public ResponseResult<String> listAll(HttpServletRequest request){
        String content = menuService.listAll(request.getHeader("code"));
        return ResponseResult.ok(content);
    }
}
