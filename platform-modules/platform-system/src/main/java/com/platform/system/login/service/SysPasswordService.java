package com.platform.system.login.service;


/**
 * @Description:
 * @author: tr
 * @date: 2024年08月30日 8:50
 */
public interface SysPasswordService {

    /**
     * @Description: 密码验证服务
     * @author: tr
     * @Date: 2024/8/30 8:51
     * @param username 用户名
     * @param rawPassword 原始密码
     * @param password 待校验密码
     * @returnValue: void
     */
    void validate(String username, String rawPassword, String password);
}
