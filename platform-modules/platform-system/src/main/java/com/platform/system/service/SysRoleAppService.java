package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysRoleApp;

import java.util.List;

/**
 * @Description: 角色与应用关系Service接口
 * @author: tr
 * @date: 2024年03月29日 14:51
 */
public interface SysRoleAppService extends IService<SysRoleApp> {

    /**
     * @Description: 根据角色ID移除角色与应用关系
     * @author: tr
     * @Date: 2024/3/29 14:55
     * @param: [roleId]
     * @returnValue: void
     */
    void removeByRoleId(Long roleId);

    /**
     * @Description: 根据角色ID组移除角色与应用关系
     * @author: tr
     * @Date: 2024/4/1 15:41
     * @param: [roleIds]
     * @returnValue: void
     */
    void removeByRoleIds(Long[] roleIds);

    /**
     * @Description: 根据角色ID查询角色下的应用信息
     * @author: tr
     * @Date: 2024/4/1 16:08
     * @param: [roleId]
     * @returnValue: java.util.List<com.platform.system.domain.SysRoleApp>
     */
    List<SysRoleApp> listByRoleId(Long roleId);
}
