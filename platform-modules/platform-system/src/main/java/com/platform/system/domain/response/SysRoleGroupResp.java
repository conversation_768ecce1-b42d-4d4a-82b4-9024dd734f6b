package com.platform.system.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ApiModel("角色组新增或修改入参")
@Data
@ToString
public class SysRoleGroupResp {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	 * 角色组名称
	 */
	@ApiModelProperty("角色组名称")
	private String name;

	/**
	 * 角色组编码
	 */
	@ApiModelProperty("角色组编码")
	private String code;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/** 创建人 **/
	@ApiModelProperty("创建人")
	private String createBy;

	/** 创建时间 **/
	@ApiModelProperty("创建时间")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date createTime;
}
