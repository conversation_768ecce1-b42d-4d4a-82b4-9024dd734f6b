package com.platform.system.login.filter;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.core.exception.ServiceException;
import com.platform.common.core.config.properties.JwtProperties;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.utils.JwtUtils;
import com.platform.common.security.context.SecurityContextHolder;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.model.LoginUser;
import com.platform.system.utils.ResponseUtils;
import io.jsonwebtoken.Claims;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月27日 16:12
 */
@Component
public class JwtFilter extends OncePerRequestFilter {



    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest
            , @NotNull HttpServletResponse httpServletResponse
            , @NotNull FilterChain filterChain) throws ServletException, IOException {
        String uri = httpServletRequest.getRequestURI();
        if (Arrays.asList(JwtProperties.antMatchers).stream().anyMatch(uri::startsWith)){
            //白名单中的请求直接放行
            filterChain.doFilter(httpServletRequest, httpServletResponse);
            return;
        }
        //获取请求头的Token
        String token = SecurityUtils.getToken();
        //判断是否有值
        if (StringUtils.hasText(token)) {
            //解析Token
            try{
                LoginUser loginUser = TokenUtils.checkToken(token);

                UsernamePasswordAuthenticationToken authenticationToken =
                        new UsernamePasswordAuthenticationToken(loginUser.getUserName(), null, new ArrayList<>());
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);

                Claims claims = JwtUtils.parseToken(token);
                String userkey = JwtUtils.getUserKey(claims);
                String userId = JwtUtils.getUserId(claims);
                SecurityContextHolder.setUserId(userId);
                SecurityContextHolder.setUserName(loginUser.getUserName());
                SecurityContextHolder.setUserKey(userkey);
            }catch (ServiceException e){
                ResponseUtils.responseJson(httpServletResponse, ResponseResult.fail(ExceptionEnum.TOKEN_EXPIRE.getCode(), e.getMessage()));
                return;
            }
        }
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }
}
