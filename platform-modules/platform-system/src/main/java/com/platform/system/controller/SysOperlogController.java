package com.platform.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.system.api.domain.request.SysOperLogReq;
import com.platform.system.domain.request.SysOperLogPageReq;
import com.platform.system.domain.response.SysOperLogResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.InnerAuth;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.service.SysOperLogService;

/**
 * 操作日志记录
 * 
 * <AUTHOR>
 */
@Api(tags = "操作日志记录前端控制器")
@RestController
@RequestMapping("/operlog")
public class SysOperlogController extends BaseController
{
    @Autowired
    private SysOperLogService operLogService;

    @ApiOperation(value = "获取操作日志记录列表",  notes = "操作日志记录")
    @RequiresPermissions("system:operlog:list")
    @GetMapping("/list")
    public ResponseResult<PageResult<SysOperLogResp>> list(SysOperLogPageReq sysOperLogPageReq)
    {
        Page<SysOperLogResp> page = PageUtils.startPage(sysOperLogPageReq.getPageNum(), sysOperLogPageReq.getPageSize());
        List<SysOperLogResp> list = operLogService.selectOperLogList(sysOperLogPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "导出操作日志记录",  notes = "操作日志记录")
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:operlog:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperLogPageReq sysOperLogPageReq)
    {
        List<SysOperLogResp> list = operLogService.selectOperLogList(sysOperLogPageReq);
        ExcelUtil<SysOperLogResp> util = new ExcelUtil<>(SysOperLogResp.class);
        util.exportExcel(response, list, "操作日志");
    }

    @ApiOperation(value = "根据操作日志ID移除操作日志",  notes = "操作日志记录")
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:operlog:remove")
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam("operIds") Long[] operIds)
    {
        return operLogService.deleteOperLogByIds(operIds)>0?ResponseResult.ok():ResponseResult.fail("删除失败！");
    }

    @ApiOperation(value = "清空操作日志",  notes = "操作日志记录")
    @RequiresPermissions("system:operlog:remove")
    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public ResponseResult clean()
    {
        operLogService.cleanOperLog();
        return ResponseResult.ok();
    }

    @ApiOperation(value = "新增操作日志",  notes = "操作日志记录")
    @InnerAuth
    @PostMapping("/save")
    public ResponseResult add(@RequestBody SysOperLogReq sysOperLogReq)
    {
        return operLogService.insertOperlog(sysOperLogReq)==1?ResponseResult.ok():ResponseResult.fail("新增失败！");
    }
}
