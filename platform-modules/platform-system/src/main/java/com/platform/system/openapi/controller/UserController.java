package com.platform.system.openapi.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.openapi.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 用户OpenApi接口Controller层
 * @author: tr
 * @date: 2024年04月09日 9:56
 */
@RestController
@RequestMapping("/openapi")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * @Description: 查询用户列表信息
     * @author: tr
     * @Date: 2024/4/9 11:22
     * @param: [updateDate]
     * @returnValue: 返回加密的字符串
     */
    @GetMapping("/v1/user/listAll")
    public ResponseResult<String> listAllV1(String updateDate, HttpServletRequest request){
        String content = userService.listAllV1(updateDate, request.getHeader("code"));
        return ResponseResult.ok(content);
    }

    @GetMapping("/v2/user/listAll")
    public ResponseResult<String> listAllV2(@RequestParam(name = "updateDate", required = false) String updateDate
            ,@RequestParam("pageNum") Integer pageNum
            ,@RequestParam("pageSize") Integer pageSize
            , HttpServletRequest request){
        String content = userService.listAllV2(updateDate, request.getHeader("code"), pageNum, pageSize);
        return ResponseResult.ok(content);
    }

    @GetMapping("/v1/user/getUserInfo")
    public ResponseResult<String> getUserInfo(Long userId, HttpServletRequest request){
        String content = userService.getUserInfo(userId, request.getHeader("code"));
        return ResponseResult.ok(content);
    }
}
