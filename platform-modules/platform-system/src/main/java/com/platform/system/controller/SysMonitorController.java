package com.platform.system.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.domain.response.SysCacheResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @Description: 系统监控前端控制器
 * @author: tr
 * @date: 2023年12月11日 14:51
 */
@Api(tags = "系统监控前端控制器")
@RestController
@RequestMapping("/monitor")
public class SysMonitorController extends BaseController {

    private final static List<SysCacheResp> caches = new ArrayList<SysCacheResp>();
    {
        caches.add(new SysCacheResp(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
        caches.add(new SysCacheResp(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
        caches.add(new SysCacheResp(CacheConstants.SYS_DICT_KEY, "数据字典"));
        caches.add(new SysCacheResp(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
        caches.add(new SysCacheResp(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
        caches.add(new SysCacheResp(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
        caches.add(new SysCacheResp(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
    }

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @RequiresPermissions("monitor:cache:list")
    @ApiOperation(value = "获取缓存信息",  notes = "系统监控")
    @GetMapping("cache")
    public ResponseResult getInfo(){
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", dbSize);

        List<Map<String, String>> pieList = new ArrayList<>();
        commandStats.stringPropertyNames().forEach(key -> {
            Map<String, String> data = new HashMap<>(2);
            String property = commandStats.getProperty(key);
            data.put("name", StringUtils.removeStart(key, "cmdstat_"));
            data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
            pieList.add(data);
        });
        result.put("commandStats", pieList);
        return ResponseResult.ok(result);
    }

    @RequiresPermissions("monitor:cache:list")
    @GetMapping("cache/getNames")
    public ResponseResult cache()
    {
        return ResponseResult.ok(caches);
    }

    @RequiresPermissions("monitor:cache:list")
    @GetMapping("cache/getKeys/{cacheName}")
    public ResponseResult getCacheKeys(@PathVariable String cacheName)
    {
        Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        return ResponseResult.ok(cacheKeys);
    }

    @RequiresPermissions("monitor:cache:list")
    @GetMapping("cache/getValue/{cacheName}/{cacheKey}")
    public ResponseResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
        String cacheValue = redisTemplate.opsForValue().get(cacheKey);
        SysCacheResp sysCache = new SysCacheResp(cacheName, cacheKey, cacheValue);
        return ResponseResult.ok(sysCache);
    }

    @RequiresPermissions("monitor:cache:list")
    @DeleteMapping("cache/clearCacheName/{cacheName}")
    public ResponseResult clearCacheName(@PathVariable String cacheName)
    {
        Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        redisTemplate.delete(cacheKeys);
        return ResponseResult.ok();
    }

    @RequiresPermissions("monitor:cache:list")
    @DeleteMapping("cache/clearCacheKey/{cacheKey}")
    public ResponseResult clearCacheKey(@PathVariable String cacheKey)
    {
        redisTemplate.delete(cacheKey);
        return ResponseResult.ok();
    }

    @RequiresPermissions("monitor:cache:list")
    @DeleteMapping("cache/clearCacheAll")
    public ResponseResult clearCacheAll()
    {
        Collection<String> cacheKeys = redisTemplate.keys("*");
        redisTemplate.delete(cacheKeys);
        return ResponseResult.ok();
    }
}
