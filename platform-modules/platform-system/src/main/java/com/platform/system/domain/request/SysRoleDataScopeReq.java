package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;

/**
 * 角色数据权限请求入参
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysRoleDataScopeReq
{
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
    @ApiModelProperty(value = "数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）")
    private String dataScope;


    @Size(min = 0, max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 部门组（数据权限） */
    @ApiModelProperty(value = "部门组（数据权限）")
    private Long[] deptIds;
}
