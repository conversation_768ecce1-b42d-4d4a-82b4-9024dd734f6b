package com.platform.system.service;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.response.SysMenuResp;
import com.platform.system.domain.SysMenu;
import com.platform.system.domain.request.SysMenuQueryReq;
import com.platform.system.domain.request.SysMenuReq;
import com.platform.system.domain.vo.RouterVo;
import com.platform.system.domain.vo.TreeSelect;

/**
 * 菜单 业务层
 * 
 * <AUTHOR>
 */
public interface SysMenuService extends IService<SysMenu>
{
    /**
     * 根据用户查询系统菜单列表
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenuResp> selectMenuList(Long userId);

    /**
     * 根据用户查询系统菜单列表
     * 
     * @param sysMenuQueryReq 菜单信息
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenuResp> selectMenuList(SysMenuQueryReq sysMenuQueryReq, Long userId);

    /**
     * @Description: 查询菜单列表
     * @author: tr
     * @Date: 2024/4/8 15:42
     * @param: [sysMenuQueryReq]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysMenuResp>
     */
    List<SysMenuResp> listMenu(SysMenuQueryReq sysMenuQueryReq);

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据角色ID查询权限
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    public Set<String> selectMenuPermsByRoleId(Long roleId);

    /**
     * 根据用户ID查询菜单树信息
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenuResp> selectMenuTreeByUserId(Long userId, String appCode);

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     * 
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<SysMenuResp> menus);

    /**
     * 构建前端所需要树结构
     * 
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<SysMenuResp> buildMenuTree(List<SysMenuResp> menus);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenuResp> menus);

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenuResp selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean hasChildByMenuId(Long menuId);

    /**
     * 新增保存菜单信息
     * 
     * @param sysMenuReq 菜单信息
     * @return 结果
     */
    public int insertMenu(SysMenuReq sysMenuReq);

    /**
     * 修改保存菜单信息
     * 
     * @param sysMenuReq 菜单信息
     * @return 结果
     */
    public int updateMenu(SysMenuReq sysMenuReq);

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    public int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean checkMenuNameUnique(SysMenu menu);

    List<SysMenuResp> listByAppCode(SysMenuQueryReq sysMenuQueryReq);
}
