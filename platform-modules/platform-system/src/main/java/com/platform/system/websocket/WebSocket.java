package com.platform.system.websocket;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.DateUtils;
import com.google.common.collect.Lists;
import com.platform.common.core.utils.JwtUtils;
import com.platform.system.domain.response.SysMessageResp;
import com.platform.system.service.SysMessageService;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: WebSocket打开和关闭类
 * @author: tr
 * @Date: 2024/4/17 10:47
 */
@Slf4j
@ServerEndpoint("/webSocket/msg")
@Component
public class WebSocket {

    /**
     * 与某个客户端的连接对话，需要通过它来给客户端发送消息
     */
    private Session session;

    private static final String TOKEN = "token";

    public Session getSession() {
        return session;
    }

    private static SysMessageService sysMessageService;

    @Autowired
    public void setMessageService(SysMessageService messageService) {
        this.sysMessageService = messageService;
    }


    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        Map<String, List<String>> params = session.getRequestParameterMap();
        if (!params.containsKey(TOKEN)) {
            throw new ServiceException("请求参数Token不存在");
        }
        String token = params.get(TOKEN).get(0);

        Claims claims = JwtUtils.parseToken(token);
        String userId = JwtUtils.getUserId(claims);

        //session对应websocket
        WebsocketSet.getWebsocketMap().put(session.getId(), this);
        //session对应userId用于关闭删除
        WebsocketSet.getSessionUserMap().put(session.getId(), userId);
        //用户对应session集合
        if (WebsocketSet.getUserSessionMap().containsKey(userId)) {
            WebsocketSet.getUserSessionMap().get(userId).add(session.getId());
        } else {
            WebsocketSet.getUserSessionMap().put(userId, Lists.newArrayList(session.getId()));
        }

        List<SysMessageResp> list = sysMessageService.listLastLimit(Long.valueOf(userId));
        if(ObjectUtil.isNotEmpty(list)){
            String msg = JSON.toJSONStringWithDateFormat(list, DateUtils.YYYY_MM_DD_HH_MM_SS, SerializerFeature.WriteDateUseDateFormat);
            try {
                session.getBasicRemote().sendText(msg);
            } catch (Exception e) {
                log.error("WebSocket推送消息异常", e);
            }
        }
        log.info("[WebSocket] 连接成功，当前连接人数为：={}", WebsocketSet.getWebsocketMap().size() + "用户数：" + WebsocketSet.getUserSessionMap().size() + "session数：" + WebsocketSet.getSessionUserMap().size());
    }

    @OnClose
    public void onClose(Session session) {
        String userId = WebsocketSet.getSessionUserMap().get(session.getId());
        if (StringUtils.isNotEmpty(userId)) {
            if (WebsocketSet.getUserSessionMap().containsKey(userId)) {
                WebsocketSet.getUserSessionMap().get(userId).removeIf(s -> (
                        s.equals(session.getId())
                ));
                if (ObjectUtil.isEmpty(WebsocketSet.getUserSessionMap().get(userId))) {
                    WebsocketSet.getUserSessionMap().remove(userId);
                }
            }
            WebsocketSet.getSessionUserMap().remove(session.getId());
            WebsocketSet.getWebsocketMap().remove(session.getId());
        }
        log.info("[WebSocket] 退出成功，当前连接人数为：={}", WebsocketSet.getWebsocketMap().size() + "用户数：" + WebsocketSet.getUserSessionMap().size() + "session数：" + WebsocketSet.getSessionUserMap().size());
    }

    @OnMessage
    public void onMessage(String message) {
        log.info("[WebSocket] 收到消息：{}", message);

    }
}

