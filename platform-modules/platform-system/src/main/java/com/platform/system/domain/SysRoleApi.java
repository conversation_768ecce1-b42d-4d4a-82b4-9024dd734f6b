package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 角色和API权限关系实体类
 * @author: tr
 * @Date: 2024/3/6 15:49
 */
@Data
@ToString
@TableName("sys_role_api")
public class SysRoleApi extends BaseEntity{

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 角色ID
	 */
	private Long roleId;

	/**
	 * apiId
	 */
	private Long apiId;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableField(
		value = "del_flag",
		fill = FieldFill.INSERT
	)
	private String delFlag;

	@TableField(exist = false)
	private String remark;
}
