package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ctdi.base.biz.page.PageResult;
import com.platform.system.api.domain.request.SysOrgPageReq;
import com.platform.system.api.domain.request.SysOrgReq;
import com.platform.system.api.domain.response.SysOrgResp;
import com.platform.system.domain.SysOrg;

import java.util.List;

/**
 * @Description: 组织机构Service接口
 * @author: tr
 * @date: 2024年03月15日 11:07
 */
public interface SysOrgService extends IService<SysOrg> {

    /**
     * @Description: 查询机构列表信息
     * @author: tr
     * @Date: 2024/3/18 9:13
     * @param: [sysOrgPageReq]
     * @returnValue: java.util.List<com.platform.system.domain.response.SysOrgResp>
     */
    List<SysOrgResp> list(SysOrgPageReq sysOrgPageReq);

    /**
     * @Description: 查询单个组织机构信息
     * @author: tr
     * @Date: 2024/3/19 12:34
     * @param: [id]
     * @returnValue: com.platform.system.domain.response.SysOrgResp
     */
    SysOrgResp getById(Long id);

    /**
     * @Description: 新增组织机构信息
     * @author: tr
     * @Date: 2024/3/18 9:13
     * @param: [sysOrgReq]
     * @returnValue: void
     */
    void save(SysOrgReq sysOrgReq);

    /**
     * @Description: 修改组织机构信息
     * @author: tr
     * @Date: 2024/3/18 9:14
     * @param: [sysOrgReq]
     * @returnValue: void
     */
    void update(SysOrgReq sysOrgReq);
    
    /**
     * @Description: 逻辑删除组织机构信息
     * @author: tr
     * @Date: 2024/3/18 9:14
     * @param: [id]
     * @returnValue: void
     */
    void delete(Long id);

    /**
     * @Description: 查询用户ID所属的组织机构信息
     * @author: tr
     * @Date: 2024/3/20 11:07
     * @param: [userId]
     * @returnValue: java.util.List<com.platform.system.domain.SysOrg>
     */
    List<SysOrgResp> listByUserId(Long userId);

    /**
     * @Description: 根据机构ID集合查询
     * @author: tr
     * @Date: 2024/9/13 11:24
     * @param: [ordIds]
     * @returnValue: java.util.List<com.platform.system.domain.SysOrg>
     */
    List<SysOrg> listByOrdIds(List<Long> ordIds);

    /**
     * @Description: 批量导入组织机构数据
     * @author: tr
     * @Date: 2024/10/16 15:48
     * @param: [list]
     * @returnValue: void
     */
    String batchSaveOrg(List<SysOrgReq> list);
}
