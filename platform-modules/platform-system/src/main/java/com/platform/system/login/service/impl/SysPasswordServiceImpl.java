package com.platform.system.login.service.impl;

import com.common.middleware.cache.CacheClient;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.login.service.SysPasswordService;
import com.platform.system.service.SysLogininforService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 登录密码方法
 * 
 * <AUTHOR>
 */
@Component
public class SysPasswordServiceImpl implements SysPasswordService {
    @Autowired
    private CacheClient redisService;

    private int maxRetryCount = CacheConstants.PASSWORD_MAX_RETRY_COUNT;

    private Long lockTime = CacheConstants.PASSWORD_LOCK_TIME;

    @Autowired
    private SysLogininforService sysLogininforService;

    /**
     * 登录账户密码错误次数缓存键名
     * 
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username)
    {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    @Override
    public void validate(String username, String rawPassword, String password){
        Integer retryCount = redisService.getCacheObject(getCacheKey(username));

        if (retryCount == null)
        {
            retryCount = 0;
        }

        if (retryCount >= Integer.valueOf(maxRetryCount).intValue())
        {
            String errMsg = String.format(ExceptionEnum.PWD_ERROR_COUNT_LOCK.getMsg(), maxRetryCount, lockTime);
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL,errMsg);
            throw new InternalAuthenticationServiceException(errMsg);
        }

        if (!matches(rawPassword, password))
        {
            retryCount = retryCount + 1;
            sysLogininforService.saveLogininfor(username, Constants.LOGIN_FAIL, String.format(ExceptionEnum.PWD_ERROR_COUNT.getMsg(), retryCount));
            redisService.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            throw new InternalAuthenticationServiceException(ExceptionEnum.USER_PWD_EROOR.getMsg());
        }
        else
        {
            clearLoginRecordCache(username);
        }
    }

    public boolean matches(String rawPassword, String password)
    {
        return SecurityUtils.matchesPassword(password, rawPassword);
    }

    public void clearLoginRecordCache(String loginName)
    {
        if (redisService.hasKey(getCacheKey(loginName)))
        {
            redisService.deleteObject(getCacheKey(loginName));
        }
    }
}
