package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.system.api.domain.request.SysDataScopeReq;
import com.platform.system.convert.SysDataScopeConvert;
import com.platform.system.domain.SysDataScope;
import com.platform.system.mapper.SysDataScopeMapper;
import com.platform.system.service.SysDataScopeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 数据权限Service实现类
 * @author: tr
 * @date: 2024年06月24日 19:33
 */
@Service
public class SysDataScopeServiceImpl extends ServiceImpl<SysDataScopeMapper, SysDataScope>
        implements SysDataScopeService {

    @Override
    public void saveBatch(List<SysDataScopeReq> sysDataScopeReqList) {
        //数据不大，可直接执行新增操作，预估数据量5条以内
        for (SysDataScopeReq sysDataScopeReq : sysDataScopeReqList){
            SysDataScope sysDataScope = SysDataScopeConvert.INSTANCE.reqToDO(sysDataScopeReq);
            save(sysDataScope);
        }
    }

    @Override
    public void deleteByDataId(Long[] dataIds, String dataType) {
        for (Long dataId : dataIds){
            LambdaUpdateWrapper<SysDataScope> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SysDataScope::getDataId, dataId);
            updateWrapper.eq(SysDataScope::getDataType, dataType);
            updateWrapper.set(SysDataScope::getDelFlag, DelFlagEnum.DELETED.getCode());
            update(new SysDataScope(), updateWrapper);
        }
    }
}
