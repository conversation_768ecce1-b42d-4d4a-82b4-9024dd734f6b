package com.platform.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@Data
@ToString
@TableName("sys_role_role_group")
public class SysRoleRoleGroup {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 角色ID
	 */
	private Long roleId;

	/**
	 * 角色组ID
	 */
	private Long roleGroupId;

}
