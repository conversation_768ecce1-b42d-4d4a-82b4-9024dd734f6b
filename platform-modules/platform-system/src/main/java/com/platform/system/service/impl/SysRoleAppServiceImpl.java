package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.domain.SysRoleApp;
import com.platform.system.mapper.SysRoleAppMapper;
import com.platform.system.service.SysRoleAppService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 角色与应用关系Service实现类
 * @author: tr
 * @date: 2024年03月29日 14:52
 */
@Service
public class SysRoleAppServiceImpl extends ServiceImpl<SysRoleAppMapper, SysRoleApp> implements SysRoleAppService {

    @Override
    public void removeByRoleId(Long roleId) {
        LambdaUpdateWrapper<SysRoleApp> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysRoleApp::getRoleId, roleId);
        remove(updateWrapper);
    }

    @Override
    public void removeByRoleIds(Long[] roleIds) {
        LambdaUpdateWrapper<SysRoleApp> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SysRoleApp::getRoleId, roleIds);
        remove(updateWrapper);
    }

    @Override
    public List<SysRoleApp> listByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleApp> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysRoleApp::getRoleId, roleId);
        List<SysRoleApp> list = list(queryWrapper);
        return list;
    }
}
