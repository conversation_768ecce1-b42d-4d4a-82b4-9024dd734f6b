package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.domain.SysAppKeyPair;
import com.platform.system.service.SysAppKeyPairService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "appkey密钥管理表前端控制器")
@RestController
@RequestMapping("/key-pair")
public class SysAppKeyPairController extends BaseController {


    @Autowired
    private SysAppKeyPairService sysAppKeyPairService;


    /**
     * 获取所有可用的已配置的appKey-appSecret密钥对信息
     */
    @ApiOperation(value = "获取所有可用的已配置的appKey-appSecret密钥对信息",  notes = "获取所有可用的已配置的appKey-appSecret密钥对信息")
    @GetMapping("/list")
    public ResponseResult<List<SysAppKeyPair>> list() {
        List<SysAppKeyPair> list = sysAppKeyPairService.selectAllEnabled();
        return ResponseResult.ok(list);
    }


    /**
     * 获取所有可用的已配置的appKey-appSecret密钥对信息
     */
    @ApiOperation(value = "获取所有可用的已配置的appKey-appSecret密钥对信息",  notes = "获取所有可用的已配置的appKey-appSecret密钥对信息")
    @GetMapping("/{appKey}")
    public ResponseResult getInfo(@PathVariable("appKey") String appKey) {
        return ResponseResult.ok(sysAppKeyPairService.selectByAppKey(appKey));
    }

    /**
     * 获取所有可用的已配置的appKey-appSecret密钥对信息
     */
    @ApiOperation(value = "刷新appKey-appSecret密钥对缓存信息",  notes = "刷新appKey-appSecret密钥对缓存信息")
    @GetMapping("/refresh")
    public ResponseResult getInfo() {
        int result  = sysAppKeyPairService.refresh();
        return result == 0 ? ResponseResult.ok() :ResponseResult.fail("刷新缓存失败");
    }
}
