package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.domain.SysUserExtend;
import com.platform.system.mapper.SysUserExtendMapper;
import com.platform.system.service.SysUserExtendService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 用户扩展信息表Service实现层
 * @author: tr
 * @date: 2024年05月14日 17:22
 */
@Service
public class SysUserExtendServiceImpl extends ServiceImpl<SysUserExtendMapper, SysUserExtend> implements SysUserExtendService {

    @Override
    public List<SysUserExtend> listByIds(List<Long> idList) {
        LambdaQueryWrapper<SysUserExtend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUserExtend::getId, idList);
        List<SysUserExtend> list = list(queryWrapper);
        return list;
    }
}
