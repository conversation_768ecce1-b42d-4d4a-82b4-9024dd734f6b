package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
public class SysDeptReq
{
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /** 父部门ID */
    @ApiModelProperty(value = "父部门ID")
    private Long parentId;

    /** 祖级列表 */
    @ApiModelProperty(value = "祖级列表")
    private String ancestors;

    /** 部门名称 */
    @ApiModelProperty(value = "部门名称")
    @NotBlank(message = "部门名称不能为空")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5_a-zA-Z0-9_]{2,30}", message = "部门名称格式不正确，支持中英文字母、数字和下划线组合，长度2-30个字符")
    private String deptName;

    /** 显示顺序 */
    @ApiModelProperty(value = "显示排序")
    @NotNull(message = "显示排序不能为空")
    @Max(value = 100000, message = "显示排序不能超过100000")
    @Min(value = 0, message = "显示排序不能小于0")
    private Integer orderNum;

    /** 负责人 */
    @ApiModelProperty(value = "负责人")
    @Size(min = 0, max = 20, message = "负责人长度不能超过20个字符")
    @Pattern(regexp = "^$|^[\\u4e00-\\u9fa5_a-zA-Z0-9_]{2,20}", message = "负责人格式不正确，支持中英文字母、数字和下划线组合，长度不超过20个字符")
    private String leader;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    @Pattern(regexp = "^$|^1[3-9]\\d{9}$|^1[3-9]\\d{1}[-\\s]\\d{4}[-\\s]\\d{4}$|^\\(1[3-9]\\d{1}\\)\\d{4}-\\d{4}$|^0\\d{3}-\\d{7}$|^0\\d{2}-\\d{8}$", message = "联系电话格式不正确")
    private String phone;

    /** 邮箱 */
    @ApiModelProperty(value = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /** 部门状态:0正常,1停用 */
    @ApiModelProperty(value = "部门状态:0正常,1停用")
    private String status;

    /** 机构Id 部门所属机构 */
    @NotNull(message = "所属机构不能为空")
    @ApiModelProperty(value = "机构Id 部门所属机构")
    private Long orgId;

}
