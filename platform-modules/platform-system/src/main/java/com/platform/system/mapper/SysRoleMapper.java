package com.platform.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import org.apache.ibatis.annotations.Mapper;

/**
 * 角色表 数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole>
{

    /**
     * 根据用户ID查询角色
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<SysRole> selectRolePermissionByUserId(Long userId);

    /**
     * 通过角色ID查询角色
     * 
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    public SysRole selectRoleById(Long roleId);

    /**
     * 根据用户ID查询角色
     * 
     * @param userName 用户名
     * @return 角色列表
     */
    public List<SysRole> selectRolesByUserName(String userName);

    /**
     * 校验角色名称是否唯一
     * 
     * @param roleName 角色名称
     * @return 角色信息
     */
    public SysRole checkRoleNameUnique(String roleName);

    /**
     * 校验角色权限是否唯一
     * 
     * @param roleKey 角色权限
     * @return 角色信息
     */
    public SysRole checkRoleKeyUnique(String roleKey);

    /**
     * 批量删除角色信息
     * 
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    public int deleteRoleByIds(Long[] roleIds);

    /**
     * @Description: 根据机构ID查询角色信息
     * @author: tr
     * @Date: 2024/3/22 9:36
     * @param: [orgIds]
     * @returnValue: java.util.List<com.platform.system.api.domain.SysRole>
     */
    List<SysRole> listRoleByOrgIds(Long[] orgIds);
}
