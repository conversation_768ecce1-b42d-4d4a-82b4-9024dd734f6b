package com.platform.system.login.provider;

import com.platform.system.login.config.single.SingleAuthenticationToken;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.login.domain.req.SingleLoginReq;
import com.platform.system.login.service.impl.SingleUserDetailServiceImpl;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * @Description: 单点登录认证验证提供者
 * @author: tr
 * @date: 2025年02月14日 08:43
 */
@Component
public class SingleAuthenticationProvider implements AuthenticationProvider {

    private SingleUserDetailServiceImpl userDetailService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SingleAuthenticationToken authenticationToken = (SingleAuthenticationToken) authentication;
        SingleLoginReq singleLoginReq = (SingleLoginReq)authentication.getPrincipal();

        SysUserDetails sysUserDetails = userDetailService.loadUserByUsername(singleLoginReq.getToken());

        SingleAuthenticationToken singleAuthenticationToken =
                new SingleAuthenticationToken(sysUserDetails, authenticationToken.getAuthorities());

        return singleAuthenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(SingleAuthenticationToken.class);
    }

    public void setUserDetailsService(SingleUserDetailServiceImpl userDetailsService) {
        this.userDetailService = userDetailsService;
    }
}
