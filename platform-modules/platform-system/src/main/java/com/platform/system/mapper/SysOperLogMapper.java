package com.platform.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.platform.system.api.domain.SysOperLog;

/**
 * 操作日志 数据层
 * 
 * <AUTHOR>
 */
public interface SysOperLogMapper extends BaseMapper<SysOperLog>
{
    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();
}
