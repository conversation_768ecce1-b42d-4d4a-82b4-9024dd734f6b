package com.platform.system.login.handler;

import cn.hutool.core.map.MapUtil;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.toolbox.service.TokenService;
import com.platform.system.api.model.LoginUser;
import com.platform.system.convert.SysUserConvert;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.service.SysLogininforService;
import com.platform.system.utils.ResponseUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Description: 用户登录成功处理器
 * @author: tr
 * @date: 2024年08月26日 15:59
 */
@Component
public class UserLoinSuccessHandler implements AuthenticationSuccessHandler {

    @Resource
    private TokenService tokenService;

    @Resource
    private SysLogininforService sysLogininforService;
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication){
        SysUserDetails sysUserDetails = (SysUserDetails)authentication.getPrincipal();

        LoginUser loginUser = SysUserConvert.INSTANCE.sysUserDetailsToLoginUser(sysUserDetails);
        Map<String, Object> rspMap = tokenService.createToken(loginUser);

        sysLogininforService.saveLogininfor(MapUtil.getStr(rspMap, "username", "")
                , Constants.LOGIN_SUCCESS, ExceptionEnum.LOGIN_SUCCESS.getMsg());

        ResponseUtils.responseJson(response, ResponseResult.ok(rspMap));
    }
}
