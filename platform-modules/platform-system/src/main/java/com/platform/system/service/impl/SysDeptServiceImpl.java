package com.platform.system.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.common.starter.share.constant.Constants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.core.enums.SyncTypeEnum;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.convert.SysDeptConvert;
import com.platform.system.domain.SysOrg;
import com.platform.system.domain.SysSyncdataLog;
import com.platform.system.domain.request.SysDeptReq;
import com.platform.system.mapper.SysOrgMapper;
import com.platform.system.service.SysSyncdataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.text.Convert;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysDept;
import com.platform.system.api.domain.SysRole;
import com.platform.system.domain.vo.TreeSelect;
import com.platform.system.mapper.SysDeptMapper;
import com.platform.system.mapper.SysRoleMapper;
import com.platform.system.service.SysDeptService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 部门管理 服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService
{
    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysSyncdataLogService sysSyncdataLogService;

    @Autowired
    private SysOrgMapper sysOrgMapper;

    /**
     * 查询部门管理数据
     * 
     * @param sysDeptQueryReq 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<SysDeptResp> selectDeptList(SysDeptQueryReq sysDeptQueryReq)
    {
        LambdaQueryWrapper<SysDept> queryWrapper = new LambdaQueryWrapper<>();
        //删除标识是空的，就查询未删除的数据
        queryWrapper.eq(StrUtil.isEmpty(sysDeptQueryReq.getDelFlag()), SysDept::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysDeptQueryReq.getDeptId()), SysDept::getDeptId, sysDeptQueryReq.getDeptId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysDeptQueryReq.getParentId()), SysDept::getParentId, sysDeptQueryReq.getParentId());
        queryWrapper.like(StrUtil.isNotEmpty(sysDeptQueryReq.getDeptName()), SysDept::getDeptName, sysDeptQueryReq.getDeptName());
        queryWrapper.eq(StrUtil.isNotEmpty(sysDeptQueryReq.getStatus()), SysDept::getStatus, sysDeptQueryReq.getStatus());
        queryWrapper.in(ObjectUtil.isNotEmpty(sysDeptQueryReq.getDeptIdList()), SysDept::getDeptId, sysDeptQueryReq.getDeptIdList());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysDeptQueryReq.getOrgId()), SysDept::getOrgId, sysDeptQueryReq.getOrgId());
        queryWrapper.orderByAsc(SysDept::getParentId, SysDept::getOrderNum);
        List<SysDept> sysDeptList = list(queryWrapper);

        List<SysDeptResp> list = SysDeptConvert.INSTANCE.doListToRespList(sysDeptList);
        list.forEach(d ->{
            setSysDeptResp(d);
        });
        return list;
    }

    /**
     * 查询部门树结构信息
     * 
     * @param sysDeptQueryReq 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelect> selectDeptTreeList(SysDeptQueryReq sysDeptQueryReq)
    {
        List<SysDeptResp> depts = SpringUtils.getAopProxy(this).selectDeptList(sysDeptQueryReq);
        return buildDeptTreeSelect(depts);
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDeptResp> buildDeptTree(List<SysDeptResp> depts)
    {
        List<SysDeptResp> returnList = new ArrayList<>();
        List<Long> tempList = depts.stream().map(SysDeptResp::getDeptId).collect(Collectors.toList());
        for (SysDeptResp dept : depts)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDeptResp> depts)
    {
        List<SysDeptResp> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     * 
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDeptResp selectDeptById(Long deptId)
    {
        LambdaQueryWrapper<SysDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDept::getDeptId, deptId);
        SysDept sysDept = getOne(queryWrapper);
        SysDeptResp sysDeptResp = SysDeptConvert.INSTANCE.doToResp(sysDept);
        setSysDeptResp(sysDeptResp);
        return sysDeptResp;
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     * 
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId)
    {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId)
    {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     * 
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId)
    {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     * 
     * @param sysDeptReq 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(SysDeptReq sysDeptReq)
    {
        Long deptId = StringUtils.isNull(sysDeptReq.getDeptId()) ? -1L : sysDeptReq.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(sysDeptReq.getDeptName(), sysDeptReq.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     * 
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId)
    {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserType()))
        {
            SysDeptQueryReq dept = new SysDeptQueryReq();
            dept.setDeptId(deptId);
            List<SysDeptResp> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts))
            {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     * 
     * @param sysDeptReq 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDeptReq sysDeptReq)
    {
        Long parentId = sysDeptReq.getParentId();
        Long orgId = sysDeptReq.getOrgId();
        SysDept dept = SysDeptConvert.INSTANCE.reqToDO(sysDeptReq);
        if (parentId != null && !ObjectUtil.equal(parentId, BusinessConstants.PARENT_ID)){
            SysDept info = deptMapper.selectDeptById(sysDeptReq.getParentId());
            // 如果父节点不为正常状态,则不允许新增子节点
            if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
            {
                throw new ServiceException("部门停用，不允许新增");
            }
            dept.setAncestors(info.getAncestors() + Constants.COMMA + sysDeptReq.getParentId());
            //新增部门默认继承上级部门的组织机构ID
            dept.setOrgId(info.getOrgId());
        }else{
            dept.setParentId(BusinessConstants.PARENT_ID);
            //第一个祖级前增加","，方便后续根据部门ID模糊查询祖级节点，不会出现查询错误的问题
            dept.setAncestors(Constants.COMMA + StrUtil.toString(dept.getParentId()));
            SysOrg sysOrg = sysOrgMapper.selectById(orgId);
            if (ObjectUtil.isNotEmpty(sysOrg)){
                //新增部门是第一级部门的话，采用前端传入的机构ID
                dept.setOrgId(sysOrg.getId());
            }else {
                throw new ServiceException("机构不存在，请确认后重试");
            }
        }

        boolean flag = save(dept);
        if (flag){
            SysSyncdataLog log = new SysSyncdataLog();
            log.setDataId(StrUtil.toString(dept.getDeptId()));
            log.setSyncType(SyncTypeEnum.DEPT.getCode());
            sysSyncdataLogService.saveLog(log);
            return 1;
        }
        return 0;
    }

    /**
     * 修改保存部门信息
     * 
     * @param sysDeptReq 部门信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDept(SysDeptReq sysDeptReq)
    {
        SysDept dept = SysDeptConvert.INSTANCE.reqToDO(sysDeptReq);
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
        {
            if (!ObjectUtil.equal(oldDept.getOrgId(), newParentDept.getOrgId())){
                throw new ServiceException("不允许跨机构移动部门");
            }

            String newAncestors = newParentDept.getAncestors() + Constants.COMMA + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            dept.setOrgId(newParentDept.getOrgId());
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        boolean flag = updateById(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors()))
        {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        if (flag){
            SysSyncdataLog log = new SysSyncdataLog();
            log.setDataId(StrUtil.toString(dept.getDeptId()));
            log.setSyncType(SyncTypeEnum.DEPT.getCode());
            sysSyncdataLogService.saveLog(log);
            return 1;
        }
        return 0;
    }

    /**
     * 修改该部门的父级部门状态
     * 
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept)
    {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     * 
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
    {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId)
    {
        int index = deptMapper.deleteDeptById(deptId);
        if (index > 0 ){
            SysSyncdataLog log = new SysSyncdataLog();
            log.setDataId(StrUtil.toString(deptId));
            log.setSyncType(SyncTypeEnum.DEPT.getCode());
            sysSyncdataLogService.saveLog(log);
        }
        return index;
    }

    @Override
    public List<SysDept> selectDeptAndChildDept(Long deptId) {
        return deptMapper.selectDeptAndChildDept(deptId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDeptResp> list, SysDeptResp t)
    {
        // 得到子节点列表
        List<SysDeptResp> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDeptResp tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDeptResp> getChildList(List<SysDeptResp> list, SysDeptResp t)
    {
        List<SysDeptResp> tlist = new ArrayList<>();
        Iterator<SysDeptResp> it = list.iterator();
        while (it.hasNext())
        {
            SysDeptResp n = it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDeptResp> list, SysDeptResp t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * @Description: 设置部门的返回对象
     * @author: tr
     * @Date: 2024/7/1 15:30
     * @param: [sysDeptResp]
     * @returnValue: void
     */
    private void setSysDeptResp(SysDeptResp sysDeptResp){
        if (ObjectUtil.isNotEmpty(sysDeptResp)){
            String ancestors = sysDeptResp.getAncestors();
            if (StrUtil.isNotBlank(ancestors)){
                String str = ancestors.substring(0, 1);
                if (StrUtil.equals(str, Constants.COMMA)){
                    //第一个字符是","，则截取掉
                    ancestors = ancestors.substring(1);
                }
                sysDeptResp.setAncestors(ancestors);
            }
        }
    }
}
