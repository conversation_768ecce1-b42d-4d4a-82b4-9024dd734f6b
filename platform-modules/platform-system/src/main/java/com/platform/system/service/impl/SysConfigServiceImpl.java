package com.platform.system.service.impl;

import java.util.Collection;
import java.util.List;
import javax.annotation.PostConstruct;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.convert.SysConfigConvert;
import com.platform.system.domain.request.SysConfigPageReq;
import com.platform.system.domain.request.SysConfigReq;
import com.platform.system.domain.response.SysConfigResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.text.Convert;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.system.domain.SysConfig;
import com.platform.system.mapper.SysConfigMapper;
import com.platform.system.service.SysConfigService;

/**
 * 参数配置 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

    @Autowired
    private CacheClient redisService;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init()
    {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     * 
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public SysConfig selectConfigById(Long configId)
    {
        SysConfig config = getById(configId);
        return config;
    }

    /**
     * 根据键名查询参数配置信息
     * 
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey)
    {
        String configValue = Convert.toStr(redisService.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue))
        {
            return configValue;
        }
        SysConfig retConfig = getConfigByKey(configKey);
        if (StringUtils.isNotNull(retConfig))
        {
            redisService.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 查询参数配置列表
     * 
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfigResp> selectConfigList(SysConfigPageReq sysConfigPageReq)
    {
        LambdaQueryWrapper<SysConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotEmpty(sysConfigPageReq.getConfigName()), SysConfig::getConfigName, sysConfigPageReq.getConfigName());
        queryWrapper.eq(StrUtil.isNotEmpty(sysConfigPageReq.getConfigType()), SysConfig::getConfigType, sysConfigPageReq.getConfigType());
        queryWrapper.like(StrUtil.isNotEmpty(sysConfigPageReq.getConfigKey()), SysConfig::getConfigKey, sysConfigPageReq.getConfigKey());
        List<SysConfig> list = list(queryWrapper);
        List<SysConfigResp> sysConfigRespList = SysConfigConvert.INSTANCE.doListToRespList(list);
        return sysConfigRespList;
    }

    /**
     * 新增参数配置
     * 
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(SysConfigReq sysConfigReq)
    {
        SysConfig config = SysConfigConvert.INSTANCE.reqToDO(sysConfigReq);
        boolean flag = save(config);
        if (flag)
        {
            redisService.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
            return 1;
        }
        return 0;
    }

    /**
     * 修改参数配置
     * 
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(SysConfigReq sysConfigReq)
    {
        SysConfig config = SysConfigConvert.INSTANCE.reqToDO(sysConfigReq);

        SysConfig temp = getById(config.getConfigId());
        if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey()))
        {
            redisService.deleteObject(getCacheKey(temp.getConfigKey()));
        }

        boolean flag = updateById(config);
        int row = flag ? 1 : 0;
        if (row > 0)
        {
            redisService.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 批量删除参数信息
     * 
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds)
    {
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType()))
            {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            removeById(configId);
            redisService.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache()
    {
        SysConfigPageReq sysConfigPageReq = new SysConfigPageReq();
        List<SysConfigResp> list = this.selectConfigList(sysConfigPageReq);
        for (SysConfigResp config : list)
        {
            redisService.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache()
    {
        Collection<String> keys = redisService.keys(CacheConstants.SYS_CONFIG_KEY + "*");
        redisService.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache()
    {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     * 
     * @param sysConfigReq 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfigReq sysConfigReq)
    {
        Long configId = StringUtils.isNull(sysConfigReq.getConfigId()) ? -1L : sysConfigReq.getConfigId();
        SysConfig info = getConfigByKey(sysConfigReq.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 设置cache key
     * 
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey)
    {
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }

    /**
     * @Description: 根据configKey查询一条数据
     * @author: tr
     * @Date: 2024/9/24 17:06
     * @param configKey
     * @returnValue: com.platform.system.domain.SysConfig
     */
    private SysConfig getConfigByKey(String configKey){
        LambdaQueryWrapper<SysConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysConfig::getConfigKey, configKey);
        queryWrapper.last("limit 1");
        SysConfig config = getOne(queryWrapper);
        return config;
    }
}
