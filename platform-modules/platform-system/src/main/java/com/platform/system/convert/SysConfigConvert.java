package com.platform.system.convert;

import com.platform.system.domain.SysConfig;
import com.platform.system.domain.request.SysConfigReq;
import com.platform.system.domain.response.SysConfigResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 配置信息转换
 * @author: tr
 * @date: 2024年09月24日 16:47
 */
@Mapper
public interface SysConfigConvert {

    SysConfigConvert INSTANCE = Mappers.getMapper(SysConfigConvert.class);

    SysConfig reqToDO(SysConfigReq sysConfigReq);

    List<SysConfigResp> doListToRespList(List<SysConfig> sysConfigList);
}
