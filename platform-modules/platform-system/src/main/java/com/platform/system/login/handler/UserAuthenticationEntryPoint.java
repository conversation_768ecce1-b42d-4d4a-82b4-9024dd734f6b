package com.platform.system.login.handler;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.utils.ResponseUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * @Description: 用户认证未登录处理器
 * @author: tr
 * @date: 2024年08月26日 16:11
 */
@Component
public class UserAuthenticationEntryPoint implements AuthenticationEntryPoint, Serializable {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e){
        ResponseUtils.responseJson(response, ResponseResult.fail("用户未登录，请登录！"));
    }
}
