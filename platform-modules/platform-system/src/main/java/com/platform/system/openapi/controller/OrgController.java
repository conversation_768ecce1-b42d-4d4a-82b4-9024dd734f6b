package com.platform.system.openapi.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.openapi.service.OrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 组织机构OpenApi接口Controller层
 * @author: tr
 * @date: 2024年04月09日 16:29
 */
@RestController
@RequestMapping("/openapi/v1/org")
public class OrgController {

    @Autowired
    private OrgService orgService;

    @GetMapping("/listAll")
    public ResponseResult<String> listAll(String updateDate, HttpServletRequest request){
        String content = orgService.listAll(updateDate, request.getHeader("code"));
        return ResponseResult.ok(content);
    }
}
