package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.github.pagehelper.Page;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.api.domain.request.SysOrgPageReq;
import com.platform.system.api.domain.request.SysOrgReq;
import com.platform.system.api.domain.response.SysOrgResp;
import com.platform.system.domain.response.SysRoleGroupResp;
import com.platform.system.service.SysOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Description: 组织机构前端控制器
 * @author: tr
 * @date: 2024年03月15日 11:11
 */
@Api(tags = "组织机构前端控制器")
@RestController
@RequestMapping("/org")
public class SysOrgController extends BaseController {

    @Autowired
    private SysOrgService sysOrgService;

    @RequiresPermissions("system:org:page")
    @ApiOperation(value = "查询组织机构信息(分页)",  notes = "组织机构")
    @GetMapping("page")
    public ResponseResult<PageResult<SysOrgResp>> page(SysOrgPageReq sysOrgPageReq){
        Page<SysRoleGroupResp> page = PageUtils.startPage(sysOrgPageReq.getPageNum(), sysOrgPageReq.getPageSize());
        List<SysOrgResp> list = sysOrgService.list(sysOrgPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "查询组织机构信息",  notes = "组织机构")
    @GetMapping("list")
    public ResponseResult<List<SysOrgResp>> list(SysOrgPageReq sysOrgPageReq){
        List<SysOrgResp> list = sysOrgService.list(sysOrgPageReq);
        return ResponseResult.ok(list);
    }

    @RequiresPermissions("system:org:getById")
    @ApiOperation(value = "查询组织机构信息",  notes = "组织机构")
    @GetMapping("getById")
    public ResponseResult<SysOrgResp> getById(Long id){
        SysOrgResp sysOrgResp = sysOrgService.getById(id);
        return ResponseResult.ok(sysOrgResp);
    }

    @RequiresPermissions("system:org:save")
    @Log(title = "组织机构", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增组织机构",  notes = "组织机构")
    @PostMapping("save")
    public ResponseResult save(@Validated @RequestBody SysOrgReq sysOrgReq){
        sysOrgService.save(sysOrgReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:org:update")
    @Log(title = "组织机构", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改组织机构",  notes = "组织机构")
    @PostMapping("update")
    public ResponseResult update(@Validated @RequestBody SysOrgReq sysOrgReq){
        sysOrgService.update(sysOrgReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:org:delete")
    @Log(title = "组织机构", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除组织机构",  notes = "组织机构")
    @DeleteMapping("delete")
    public ResponseResult delete(@RequestParam Long id){
        sysOrgService.delete(id);
        return ResponseResult.ok();
    }

    /**
     * @Description: 根据用户ID查询用户所属的组织机构信息
     * @author: tr
     * @Date: 2024/4/16 9:23
     * @param userId 用户ID主键
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<java.util.List<com.platform.system.api.domain.response.SysOrgResp>>
     */
    @GetMapping("listByUserId")
    public ResponseResult<List<SysOrgResp>> listByUserId(Long userId){
        List<SysOrgResp> sysOrgList = sysOrgService.listByUserId(userId);
        return ResponseResult.ok(sysOrgList);
    }

    @ApiOperation(value = "导入组织机构数据信息",  notes = "组织机构")
    @Log(title = "组织机构", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public ResponseResult<String> importData(MultipartFile file) throws IOException {
        ExcelUtil<SysOrgReq> util = new ExcelUtil<>(SysOrgReq.class);
        List<SysOrgReq> list = util.importExcel(file.getInputStream());
        String msg = sysOrgService.batchSaveOrg(list);
        return ResponseResult.ok(msg);
    }
}
