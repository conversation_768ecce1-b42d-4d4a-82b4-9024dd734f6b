package com.platform.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import com.platform.system.api.domain.request.SysUserPageReq;
import com.platform.system.api.domain.request.SysUserRoleReq;
import com.platform.system.api.domain.response.SysOrgRoleResp;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.convert.SysRoleConvert;
import com.platform.system.domain.request.SysRoleDataScopeReq;
import com.platform.system.domain.request.SysRoleReq;
import com.platform.system.domain.response.SysRoleGroupResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysRole;
import com.platform.system.domain.SysUserRole;
import com.platform.system.service.SysDeptService;
import com.platform.system.service.SysRoleService;
import com.platform.system.service.SysUserService;

/**
 * 角色信息
 * 
 * <AUTHOR>
 */
@Api(tags = "角色信息前端控制器")
@RestController
@RequestMapping("/role")
public class SysRoleController extends BaseController
{
    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysUserService userService;

    @Autowired
    private SysDeptService deptService;

    @ApiOperation(value = "获取角色列表分页",  notes = "角色信息")
    @RequiresPermissions("system:role:page")
    @PostMapping("/page")
    public ResponseResult<PageResult<SysRoleResp>> page(@RequestBody SysRoleQueryReq sysRoleQueryReq)
    {
        Page<SysRoleResp> page = PageUtils.startPage(sysRoleQueryReq.getPageNum(), sysRoleQueryReq.getPageSize());
        List<SysRoleResp> list = roleService.selectRoleList(sysRoleQueryReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "获取角色列表",  notes = "角色信息")
    @RequiresPermissions("system:role:list")
    @PostMapping("/list")
    public ResponseResult<List<SysRoleResp>> list(@RequestBody SysRoleQueryReq sysRoleQueryReq)
    {
        List<SysRoleResp> list = roleService.selectRoleList(sysRoleQueryReq);
        return ResponseResult.ok(list);
    }

    @ApiOperation(value = "导出角色",  notes = "角色信息")
    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:role:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRoleQueryReq role)
    {
        List<SysRoleResp> list = roleService.selectRoleList(role);
        ExcelUtil<SysRoleResp> util = new ExcelUtil<>(SysRoleResp.class);
        util.exportExcel(response, list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息
     */
    @ApiOperation(value = "根据角色ID查询角色信息",  notes = "角色信息")
    @RequiresPermissions("system:role:query")
    @GetMapping(value = "/{roleId}")
    public ResponseResult<SysRoleResp> getInfo(@PathVariable Long roleId)
    {
        roleService.checkRoleDataScope(roleId);
        return ResponseResult.ok(roleService.selectRoleById(roleId));
    }

    /**
     * @Description: 根据角色ID查询单个信息
     * @author: tr
     * @Date: 2024/2/27 16:10
     * @param: [roleId]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<com.platform.system.api.domain.response.SysRoleResp>
     */
    @ApiOperation(value = "根据角色ID查询角色信息",  notes = "角色信息")
    @RequiresPermissions("system:role:query")
    @GetMapping(value = "/getById")
    public ResponseResult<SysRoleResp> getInfoById(@RequestParam Long roleId)
    {
        roleService.checkRoleDataScope(roleId);
        SysRoleResp sysRoleResp = roleService.selectRoleById(roleId);
        return ResponseResult.ok(sysRoleResp);
    }

    /**
     * 新增角色
     */
    @ApiOperation(value = "新增角色",  notes = "角色信息")
    @RequiresPermissions("system:role:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ResponseResult add(@Validated @RequestBody SysRoleReq sysRoleReq)
    {
        return ResponseResult.ok(roleService.insertRole(sysRoleReq));

    }

    /**
     * 修改保存角色
     */
    @ApiOperation(value = "修改角色",  notes = "角色信息")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult edit(@Validated @RequestBody SysRoleReq sysRoleReq)
    {
        return ResponseResult.ok(roleService.updateRole(sysRoleReq));
    }

    /**
     * 修改保存数据权限
     */
    @ApiOperation(value = "修改角色的数据权限",  notes = "角色信息")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public ResponseResult dataScope(@Validated @RequestBody SysRoleDataScopeReq sysRoleDataScopeReq)
    {
        SysRole role = SysRoleConvert.INSTANCE.dataScopeReqToDO(sysRoleDataScopeReq);
        roleService.checkRoleDataScope(sysRoleDataScopeReq.getRoleId());
        return roleService.authDataScope(role)>0?ResponseResult.ok():ResponseResult.fail("修改数据权限失败！");
    }

    /**
     * 状态修改
     */
    @ApiOperation(value = "修改角色状态",  notes = "角色信息")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public ResponseResult changeStatus(@RequestBody SysRoleReq sysRoleReq)
    {
        SysRole role = SysRoleConvert.INSTANCE.reqToDO(sysRoleReq);
        roleService.checkRoleDataScope(role.getRoleId());
        role.setUpdateBy(SecurityUtils.getUsername());
        return roleService.updateRoleStatus(role)==1?ResponseResult.ok():ResponseResult.fail("修改角色状态失败！");
    }

    /**
     * 删除角色
     */
    @ApiOperation(value = "删除角色",  notes = "角色信息")
    @RequiresPermissions("system:role:remove")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam("roleIds") Long[] roleIds)
    {
        return roleService.deleteRoleByIds(roleIds)>0?ResponseResult.ok():ResponseResult.fail("删除角色失败！");
    }

    /**
     * 获取角色选择框列表
     */
    @ApiOperation(value = "获取角色选择框列表",  notes = "角色信息")
    @RequiresPermissions("system:role:query")
    @GetMapping("/optionselect")
    public ResponseResult optionselect()
    {
        return ResponseResult.ok(roleService.selectRoleAll());
    }
    /**
     * 查询已分配用户角色列表
     */
    @ApiOperation(value = "查询已分配用户角色列表",  notes = "角色信息")
    @RequiresPermissions("system:role:page")
    @GetMapping("/authUser/allocatedList")
    public ResponseResult<PageResult<SysUserResp>> allocatedList(SysUserPageReq sysUserPageReq)
    {
        Page<SysRoleGroupResp> page = PageUtils.startPage(sysUserPageReq.getPageNum(), sysUserPageReq.getPageSize());
        List<SysUserResp> list = userService.selectAllocatedList(sysUserPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    /**
     * 查询未分配用户角色列表
     */
    @ApiOperation(value = "查询未分配用户角色列表",  notes = "角色信息")
    @RequiresPermissions("system:role:page")
    @GetMapping("/authUser/unallocatedList")
    public ResponseResult<PageResult<SysUserResp>> unallocatedList(SysUserPageReq sysUserPageReq)
    {
        Page<SysUserResp> page = PageUtils.startPage(sysUserPageReq.getPageNum(), sysUserPageReq.getPageSize());
        List<SysUserResp> list = userService.selectUnallocatedList(sysUserPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    /**
     * 取消授权用户
     */
    @ApiOperation(value = "取消授权用户",  notes = "角色信息")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public ResponseResult cancelAuthUser(@RequestBody SysUserRoleReq sysUserRoleReq)
    {
        return roleService.deleteAuthUser(sysUserRoleReq)>0?ResponseResult.ok():ResponseResult.fail("取消授权用户失败！");
    }

    /**
     * 批量取消授权用户
     */
    @ApiOperation(value = "批量取消授权用户",  notes = "角色信息")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public ResponseResult cancelAuthUserAll(Long roleId, Long[] userIds)
    {
        return roleService.deleteAuthUsers(roleId, userIds)>0?ResponseResult.ok():ResponseResult.fail("批量取消授权用户失败！");
    }

    /**
     * 批量选择用户授权
     */
    @ApiOperation(value = "批量选择用户授权",  notes = "角色信息")
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public ResponseResult selectAuthUserAll(@RequestBody SysUserRole sysUserRole)
    {
        roleService.checkRoleDataScope(sysUserRole.getRoleId());
        return roleService.insertAuthUsers(sysUserRole.getRoleId(), sysUserRole.getUserIds())>0?ResponseResult.ok():ResponseResult.fail("批量选择用户授权失败！");
    }

    /**
     * 获取对应角色部门树列表
     */
    @ApiOperation(value = "获取对应角色部门树列表",  notes = "角色信息")
    @RequiresPermissions("system:role:query")
    @GetMapping(value = "/deptTree/{roleId}")
    public ResponseResult deptTree(@PathVariable("roleId") Long roleId)
    {
        Map<String, Object> result = new HashMap<>();
        result.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        result.put("depts", deptService.selectDeptTreeList(new SysDeptQueryReq()));
        return ResponseResult.ok(result);
    }

    @ApiOperation(value = "根据机构ID查询角色信息",  notes = "角色信息")
    @RequiresPermissions("system:role:listRoleByOrgIds")
    @GetMapping(value = "/listRoleByOrgIds")
    public ResponseResult<List<SysRoleResp>> listRoleByOrgIds(Long[] orgIds){
        List<SysRoleResp> list = roleService.listRoleByOrgIds(orgIds);
        return ResponseResult.ok(list);
    }

    @ApiOperation(value = "根据机构ID查询角色信息",  notes = "角色信息")
    @RequiresPermissions("system:role:listRoleByOrgIds")
    @GetMapping(value = "/listOrgRoles")
    public ResponseResult<List<SysOrgRoleResp>> listOrgRoles(){
        List<SysOrgRoleResp> list = roleService.listOrgRoles();
        return ResponseResult.ok(list);
    }
}
