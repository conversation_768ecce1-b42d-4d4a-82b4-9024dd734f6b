package com.platform.system.openapi.domain.response;

import lombok.Data;
import lombok.ToString;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class DeptResp {

    /** 部门ID */

    private Long deptId;

    /** 父部门ID */
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 部门名称 */
    private String deptName;

    /** 显示顺序 */
    private Integer orderNum;

    /** 负责人 */
    private String leader;

    /** 联系电话 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 部门状态:0正常,1停用 */
    private String status;

}
