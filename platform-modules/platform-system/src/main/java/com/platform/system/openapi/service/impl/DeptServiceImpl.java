package com.platform.system.openapi.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.system.api.domain.SysDept;
import com.platform.system.convert.SysDeptConvert;
import com.platform.system.openapi.domain.response.DeptResp;
import com.platform.system.openapi.domain.response.UserResp;
import com.platform.system.openapi.service.DeptService;
import com.platform.system.service.SysDeptService;
import com.platform.system.utils.EncrypDecrypUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class DeptServiceImpl implements DeptService {

    @Resource
    private SysDeptService sysDeptService;

    @Autowired
    private CacheClient redisService;

    @Override
    public String listAllV1(String updateDate, String code) {
        List<DeptResp> deptRespList = listAll(updateDate);

        return encryptionStr(deptRespList, code);
    }

    @Override
    public String listAllV2(String updateDate, String code, Integer pageNum, Integer pageSize) {
        pageNum = pageNum == null ? 1 : pageNum;
        pageSize = pageSize == null ? 1000 : pageSize;
        if (pageSize > 1000){
            pageSize = 1000;
        }
        Page<DeptResp> page = PageUtils.startPage(pageNum, pageSize);
        List<DeptResp> deptRespList = listAll(updateDate);
        PageResult pageResult = new PageResult(deptRespList, page.getTotal(), page.getPageNum(), page.getPageSize());
        return encryptionStr(pageResult, code);
    }

    private List<DeptResp> listAll(String updateDate){
        LambdaQueryWrapper<SysDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDept::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.ge(StrUtil.isNotBlank(updateDate), SysDept::getUpdateTime, updateDate);
        List<SysDept> sysDeptList = sysDeptService.list(queryWrapper);
        List<DeptResp> deptRespList = SysDeptConvert.INSTANCE.doListToRespListOpenApi(sysDeptList);
        return deptRespList;
    }

    private String encryptionStr(Object data, String code){
        //根据应用编码从缓存中获取key用于加密
        Map<String, String> codeKeyMap = redisService.getCacheMap(CacheConstants.APP_CODE_PUBLIC_KEY_MAP);
        String key = codeKeyMap.get(code);
        String content = EncrypDecrypUtils.encrypt(
                JSONUtil.toJsonStr(data), key);
        return content;
    }
}
