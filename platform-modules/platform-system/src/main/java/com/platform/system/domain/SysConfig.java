package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

/**
 * 参数配置表 sys_config
 * 
 * <AUTHOR>
 */
@Data
public class SysConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 参数ID */
    @TableId(value = "config_id", type = IdType.ASSIGN_ID)
    private Long configId;

    /** 参数名称 */
    private String configName;

    /** 参数键名 */
    private String configKey;

    /** 参数键值 */
    private String configValue;

    /** 系统内置（Y是 N否） */
    private String configType;
}
