package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.request.SysUserSubjectReq;
import com.platform.system.domain.response.SysUserSubjectResp;

/**
 * @Description:
 * @author: tr
 * @date: 2023年12月11日 10:17
 */
public interface SysUserSubject extends IService<com.platform.system.domain.SysUserSubject> {
    
    /**
     * @Description: 保存用户主题信息
     * @author: tr
     * @Date: 2023/12/11 10:37
     * @param: [sysUserSubjectBO]
     * @returnValue: void
     */
    void add(SysUserSubjectReq sysUserSubjectReq);

    /**
     * @Description: 查询当前用户的主题信息
     * @author: tr
     * @Date: 2023/12/11 12:11
     * @param: []
     * @returnValue: com.platform.system.domain.response.SysUserSubjectVO
     */
    SysUserSubjectResp getOneByUser();
}
