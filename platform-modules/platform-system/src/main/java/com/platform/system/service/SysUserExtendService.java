package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysUserExtend;

import java.util.List;

/**
 * @Description: 用户扩展信息表Service接口
 * @author: tr
 * @date: 2024年05月14日 17:20
 */
public interface SysUserExtendService extends IService<SysUserExtend> {

    /**
     * @Description: 根据ID集合查询用户扩展信息集合
     * @author: tr
     * @Date: 2024/6/18 9:56
     * @param: [idList]
     * @returnValue: java.util.List<com.platform.system.domain.SysUserExtend>
     */
    List<SysUserExtend> listByIds(List<Long> idList);
}
