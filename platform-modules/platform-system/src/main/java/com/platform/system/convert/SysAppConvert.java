package com.platform.system.convert;

import com.platform.system.domain.SysApp;
import com.platform.system.domain.request.SysAppReq;
import com.platform.system.domain.response.SysAppResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 应用管理对象转换
 * @author: tr
 * @date: 2024年03月27日 14:39
 */
@Mapper
public interface SysAppConvert {

    SysAppConvert INSTANCE = Mappers.getMapper(SysAppConvert.class);

    SysApp reqToDO(SysAppReq sysAppReq);

    SysAppResp doToResp(SysApp sysApp);

    List<SysAppResp> doListToRespList(List<SysApp> sysAppList);
}
