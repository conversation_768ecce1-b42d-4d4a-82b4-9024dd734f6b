package com.platform.system.login.config.mobilephone;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * @Description: 手机验证码认证令牌
 * @author: tr
 * @date: 2025年02月13日 09:45
 */
public class MobilePhoneAuthenticationToken extends AbstractAuthenticationToken {

    private static final long serialVersionUID = 2387092775910246006L;

    private final Object principal;

    public MobilePhoneAuthenticationToken(Object object) {
        super(null);
        this.principal = object;
        setAuthenticated(false);
    }

    public MobilePhoneAuthenticationToken(Object principal,
                                          Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    /**
     * @param isAuthenticated
     * @throws IllegalArgumentException
     */
    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                    "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }
        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
    }
}
