package com.platform.system.login.config.code;

import com.platform.system.login.filter.UserPwdLoginFilter;
import com.platform.system.login.provider.UserPwdAuthenticationProvider;
import com.platform.system.login.service.SysPasswordService;
import com.platform.system.login.service.impl.UserDetailServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * @Description: 用户名密码认证配置，用于添加userPwdLoginFilter过滤器，以及添加认证成功和认证失败的处理器
 * @author: tr
 * @date: 2024年08月26日 15:22
 */
@Component
public class UserPwdAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    @Autowired
    private AuthenticationSuccessHandler authenticationSuccessHandler;
    @Autowired
    private AuthenticationFailureHandler authenticationFailureHandler;

    @Autowired
    private UserDetailServiceImpl userDetailServiceImpl;
    @Autowired
    private SysPasswordService sysPasswordService;

    @Override
    public void configure(HttpSecurity builder) {
        AuthenticationManager localAuthManager = builder.getSharedObject(AuthenticationManager.class);

        UserPwdLoginFilter jwtUserPwdLoginFilter = new UserPwdLoginFilter(localAuthManager);
        jwtUserPwdLoginFilter.setAuthenticationFailureHandler(authenticationFailureHandler);
        jwtUserPwdLoginFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
        //设置自定义UserPwdAuthenticationProvider的认证器userDetailsService
        UserPwdAuthenticationProvider cloAuthenticationProvider = new UserPwdAuthenticationProvider();
        cloAuthenticationProvider.setUserDetailsService(userDetailServiceImpl, sysPasswordService);
        //在UsernamePasswordAuthenticationFilter过滤前执行
        builder.authenticationProvider(cloAuthenticationProvider).addFilterAfter(jwtUserPwdLoginFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
