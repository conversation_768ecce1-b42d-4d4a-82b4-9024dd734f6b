package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参数配置表 sys_config
 * 
 * <AUTHOR>
 */
@Data
public class SysConfigPageReq extends PageReq
{
    private static final long serialVersionUID = 1L;

    /** 参数名称 */
    @ApiModelProperty(value = "参数名称")
    private String configName;

    /** 参数键名 */
    @ApiModelProperty(value = "参数键名")
    private String configKey;


    /** 系统内置（Y是 N否） */
    @ApiModelProperty(value = "系统内置（Y是 N否）")
    private String configType;

}
