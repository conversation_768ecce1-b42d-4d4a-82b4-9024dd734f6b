package com.platform.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.system.domain.request.SysConfigPageReq;
import com.platform.system.domain.request.SysConfigReq;
import com.platform.system.domain.response.SysConfigResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.service.SysConfigService;

/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
@Api(tags = "参数配置前端控制器")
@RestController
@RequestMapping("/config")
public class SysConfigController extends BaseController
{
    @Autowired
    private SysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @ApiOperation(value = "获取参数配置列表",  notes = "参数配置")
    @RequiresPermissions("system:config:list")
    @GetMapping("/list")
    public ResponseResult<PageResult<SysConfigResp>> list(SysConfigPageReq sysConfigPageReq)
    {
        Page<SysConfigResp> page = PageUtils.startPage(sysConfigPageReq.getPageNum(), sysConfigPageReq.getPageSize());
        List<SysConfigResp> list = configService.selectConfigList(sysConfigPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "参数配置导出",  notes = "参数配置")
    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:config:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysConfigPageReq sysConfigPageReq)
    {
        List<SysConfigResp> list = configService.selectConfigList(sysConfigPageReq);
        ExcelUtil<SysConfigResp> util = new ExcelUtil<>(SysConfigResp.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @ApiOperation(value = "根据参数编号获取详细信息",  notes = "参数配置")
    @GetMapping("/configId")
    public ResponseResult getInfo(Long configId)
    {
        return ResponseResult.ok(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @ApiOperation(value = "根据参数键名查询参数值",  notes = "参数配置")
    @GetMapping("/configKey")
    public ResponseResult getConfigKey(String configKey)
    {
        return ResponseResult.ok(configService.selectConfigByKey(configKey));
    }


    /**
     * 新增参数配置
     */
    @ApiOperation(value = "新增参数配置",  notes = "参数配置")
    @RequiresPermissions("system:config:add")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult add(@Validated @RequestBody SysConfigReq sysConfigReq)
    {
        if (!configService.checkConfigKeyUnique(sysConfigReq))
        {
            return ResponseResult.fail("新增参数'" + sysConfigReq.getConfigName() + "'失败，参数键名已存在");
        }
        return configService.insertConfig(sysConfigReq)>0?ResponseResult.ok():ResponseResult.fail("新增参数失败！");
    }

    /**
     * 修改参数配置
     */
    @ApiOperation(value = "修改参数配置",  notes = "参数配置")
    @RequiresPermissions("system:config:edit")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public ResponseResult edit(@Validated @RequestBody SysConfigReq sysConfigReq)
    {
        if (!configService.checkConfigKeyUnique(sysConfigReq))
        {
            return ResponseResult.fail("修改参数'" + sysConfigReq.getConfigName() + "'失败，参数键名已存在");
        }
        return configService.updateConfig(sysConfigReq)>0?ResponseResult.ok():ResponseResult.fail("修改参数失败！");
    }

    /**
     * 删除参数配置
     */
    @ApiOperation(value = "删除参数配置",  notes = "参数配置")
    @RequiresPermissions("system:config:remove")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/configIds")
    public ResponseResult remove(Long[] configIds)
    {
        configService.deleteConfigByIds(configIds);
        return ResponseResult.ok();
    }

    /**
     * 刷新参数缓存
     */
    @ApiOperation(value = "刷新参数缓存",  notes = "参数配置")
    @RequiresPermissions("system:config:remove")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public ResponseResult refreshCache()
    {
        configService.resetConfigCache();
        return ResponseResult.ok();
    }
}
