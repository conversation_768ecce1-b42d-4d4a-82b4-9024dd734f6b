package com.platform.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

@Data
@ToString
@TableName("sys_message")
public class SysMessage  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 消息归类（1-待办消息，2-系统消息）
	 */
	private Integer type;

	/** 消息类型 **/
	private Integer subType;

	/**
	 * 应用编码
	 */
	private String appCode;

	/**
	 * 扩展主键
	 */
	private String expandId;

	/**
	 * 通知人ID
	 */
	private Long notifierId;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 消息内容
	 */
	private String content;

	/**
	 * 消息状态（0-未读，1-已读）
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	private String delFlag;

	/**
	 * 创建人ID
	 */
	private Long createId;
}
