package com.platform.system.openapi.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.openapi.service.DeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/openapi")
public class DeptController {

    @Autowired
    private DeptService deptService;

    @GetMapping("/v1/dept/listAll")
    public ResponseResult<String> listAllV1(String updateDate, HttpServletRequest request){
        String content = deptService.listAllV1(updateDate, request.getHeader("code"));
        return ResponseResult.ok(content);
    }

    @GetMapping("/v2/dept/listAll")
    public ResponseResult<String> listAllV2(@RequestParam(name = "updateDate", required = false) String updateDate
            ,@RequestParam("pageNum") Integer pageNum
            ,@RequestParam("pageSize") Integer pageSize
            , HttpServletRequest request){
        String content = deptService.listAllV2(updateDate, request.getHeader("code"), pageNum, pageSize);
        return ResponseResult.ok(content);
    }
}
