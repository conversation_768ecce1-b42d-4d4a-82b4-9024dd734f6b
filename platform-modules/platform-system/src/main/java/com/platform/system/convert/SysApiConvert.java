package com.platform.system.convert;

import com.platform.system.api.domain.response.SysApiResp;
import com.platform.system.domain.SysApi;
import com.platform.system.domain.request.SysApiReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: API权限配置信息转换
 * @author: tr
 * @date: 2024年02月29日 17:23
 */
@Mapper
public interface SysApiConvert {

    SysApiConvert INSTANCE = Mappers.getMapper(SysApiConvert.class);

    SysApi reqToDO(SysApiReq sysApiReq);

    SysApiResp doToResp(SysApi sysApi);

    List<SysApiResp> doListToRespList(List<SysApi> sysApiList);

}
