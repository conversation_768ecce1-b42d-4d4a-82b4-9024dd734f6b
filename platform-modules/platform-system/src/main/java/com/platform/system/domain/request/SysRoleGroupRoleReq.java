package com.platform.system.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 角色组新增或移除角色入参
 * @author: tr
 * @date: 2024年03月01日 14:17
 */
@ApiModel("角色组新增或移除角色入参")
@Data
@ToString
public class SysRoleGroupRoleReq {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID，角色组ID")
	private Long id;

	@ApiModelProperty("角色ID的集合")
	private List<Long> roleIdList;

}
