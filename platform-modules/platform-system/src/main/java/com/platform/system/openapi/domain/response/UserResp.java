package com.platform.system.openapi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


/**
 * @Description: OpenApi接口-用户信息返回类
 * @author: tr
 * @date: 2024年03月22日 16:56
 */
@Data
public class UserResp {

    /** 用户ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 用户账号 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户邮箱 */
    private String email;

    /** 手机号码 */
    private String phonenumber;

    /** 用户性别 */
    private String sex;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 证件类型 **/
    private String certificateType;
    /** 证件号码 **/
    private String certificateNo;

}
