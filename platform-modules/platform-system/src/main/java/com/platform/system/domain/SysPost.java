package com.platform.system.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

/**
 * 岗位表 sys_post
 * 
 * <AUTHOR>
 */
@Data
public class SysPost extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 岗位ID */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long postId;

    /** 岗位编码 */
    private String postCode;

    /** 岗位名称 */
    private String postName;

    /** 岗位排序 */
    private Integer postSort;

    /** 状态（0正常 1停用） */
    private String status;
}
