package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 短信验证码入参
 * @author: tr
 * @date: 2025年02月13日 17:36
 */
@Data
@ToString
public class SmsValidateReq {

    /** 手机号码 **/
    @ApiModelProperty("手机号码")
    private String phonenumber;
    /** 类型（1-注册验证码，2-登录验证码，3-设置密码的验证码，4-修改手机号码的验证码，5-忘记密码） **/
    @ApiModelProperty("类型（1-注册验证码，2-登录验证码，3-设置密码的验证码，4-修改手机号码的验证码，5-忘记密码）")
    private String type;
    /** 验证码UUID **/
    @ApiModelProperty("验证码UUID")
    private String validateUuid;
    /** 验证码值 **/
    @ApiModelProperty("验证码值")
    private String validateCode;
}
