package com.platform.system.domain;

import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.ctdi.common.starter.toolbox.annotation.Excel.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * appkey密钥管理表 sys_app_key_pair
 * 
 * <AUTHOR>
 */
@Data
public class SysAppKeyPair extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    private Integer id;

    /** 组织编号 */
    @ApiModelProperty(value = "组织编号")
    private String orgId;

    /** 组织名称 */
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /** 应用编号 */
    @ApiModelProperty(value = "应用编号")
    private String appId;

    /** 应用名称 */
    @ApiModelProperty(value = "应用名称")
    private String appName;


    /** openapi验签时分配的appkey */
    @ApiModelProperty(value = "openapi验签时分配的appkey")
    private String appKey;

    /** openapi验签时分配的appSecret */
    @ApiModelProperty(value = "openapi验签时分配的appSecret")
    private String appSecret;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;
}
