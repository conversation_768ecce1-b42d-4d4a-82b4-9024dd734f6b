package com.platform.system.domain.request;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 字典数据新增或修改入参
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysDictDataReq
{
    private static final long serialVersionUID = 1L;

    /** 字典编码 */
    @ApiModelProperty(value = "字典编码")
    private Long dictCode;

    /** 字典排序 */
    @ApiModelProperty(value = "字典排序")
    @Excel(name = "字典排序", cellType = Excel.ColumnType.NUMERIC)
    private Long dictSort;

    /** 字典标签 */
    @NotBlank(message = "字典标签不能为空")
    @Size(min = 0, max = 100, message = "字典标签长度不能超过100个字符")
    @ApiModelProperty(value = "字典标签")
    @Excel(name = "字典标签")
    private String dictLabel;

    /** 字典键值 */
    @NotBlank(message = "字典键值不能为空")
    @Size(min = 0, max = 100, message = "字典键值长度不能超过100个字符")
    @ApiModelProperty(value = "字典键值")
    @Excel(name = "字典键值")
    private String dictValue;

    /** 字典类型 */
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 0, max = 100, message = "字典类型长度不能超过100个字符")
    @ApiModelProperty(value = "字典类型")
    @Excel(name = "字典类型")
    private String dictType;

    /** 样式属性（其他样式扩展） */
    @Size(min = 0, max = 100, message = "样式属性长度不能超过100个字符")
    @ApiModelProperty(value = "样式属性（其他样式扩展）")
    @Excel(name = "样式属性")
    private String cssClass;

    /** 表格字典样式 */
    @ApiModelProperty(value = "表格字典样式")
    @Excel(name = "回显样式", readConverterExp = "default=默认,primary=主要,success=成功,info=信息,warning=警告,danger=危险")
    private String listClass;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 备注 **/
    @Size(min = 0, max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;
}
