package com.platform.system.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description: 站内信新增入参
 * @author: tr
 * @date: 2024年04月16日 15:35
 */
@ApiModel("站内信新增入参")
@Data
@ToString
public class SysMessageReq {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息归类（1-待办消息，2-系统消息）
	 */
	@NotNull(message = "消息类型不能为空")
	@Max(value = 4, message = "消息类型不能超过4个数字")
	@ApiModelProperty("消息归类（1-待办消息，2-系统消息）")
	private Integer type;

	/**
	 * 应用编码
	 */
	@NotBlank(message = "应用编码不能为空")
	@Size(min = 1, max = 20, message = "应用编码不能超过20个字符")
	@ApiModelProperty("应用编码")
	private String appCode;

	/**
	 * 扩展主键
	 */
	@ApiModelProperty("扩展主键")
	private String expandId;

	/**
	 * 通知人ID数组
	 */
	@NotNull(message = "通知人不能为空")
	@ApiModelProperty("通知人ID数组")
	private Long[] notifierIds;

	/**
	 * 标题
	 */
	@NotBlank(message = "标题不能为空")
	@Size(min = 1, max = 60, message = "标题不能超过60个字符")
	@ApiModelProperty("标题")
	private String title;

	/**
	 * 消息内容
	 */
	@NotBlank(message = "消息内容不能为空")
	@ApiModelProperty("消息内容")
	private String content;

}
