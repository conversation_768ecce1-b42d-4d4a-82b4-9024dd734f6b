package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;


/**
 * @Description: 站内信分页查询入参
 * @author: tr
 * @date: 2024年04月16日 15:35
 */
@ApiModel("站内信分页查询入参")
@Data
@ToString
public class SysMessagePageReq extends PageReq {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息归类（1-待办消息，2-系统消息）
	 */
	@ApiModelProperty("消息归类（1-待办消息，2-系统消息）")
	private Integer type;

	/**
	 * 消息状态（0-未读，1-已读）
	 */
	@ApiModelProperty("消息状态（0-未读，1-已读）")
	private String status;

	/**
	 * 标题
	 */
	@ApiModelProperty("标题")
	private String title;

}
