package com.platform.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.platform.common.security.annotation.EnableCustomConfig;
import com.platform.common.security.annotation.EnablePlatformFeignClients;
import com.ctdi.common.starter.swagger.annotation.EnableCustomSwagger2;

/**
 * 系统模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnablePlatformFeignClients(basePackages = {"com.platform", "com.message"})
@SpringBootApplication
public class PlatFormSystemApplication
{

    public static void main(String[] args)
    {
        SpringApplication.run(PlatFormSystemApplication.class, args);
        System.out.println("系统模块启动成功");
    }
}
