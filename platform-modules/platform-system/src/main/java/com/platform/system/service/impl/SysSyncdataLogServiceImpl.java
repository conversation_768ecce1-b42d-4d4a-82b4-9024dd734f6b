package com.platform.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.common.core.config.properties.CoreProperties;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.core.enums.SyncStatusEnum;
import com.platform.syncdata.api.RemoteSyncdataService;
import com.platform.system.api.domain.request.SysSyncdataLogReq;
import com.platform.system.api.domain.response.SysSyncdataLogResp;
import com.platform.system.convert.SysSyncdataLogConvert;
import com.platform.system.domain.SysSyncdataLog;
import com.platform.system.mapper.SysSyndataLogMapper;
import com.platform.system.service.SysSyncdataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: 数据同步日志记录Service业务层处理
 * @author: tr
 * @date: 2025年03月26日 17:59
 */
@Service
public class SysSyncdataLogServiceImpl extends ServiceImpl<SysSyndataLogMapper, SysSyncdataLog> implements SysSyncdataLogService {

    @Autowired
    private RemoteSyncdataService remoteSyncdataService;

    @Autowired
    private CoreProperties coreProperties;

    @Override
    public void saveLog(SysSyncdataLog log) {
        if (coreProperties.isBizdataIssuedFlag()){
            log.setSyncStatus(SyncStatusEnum.STAY.getCode());
            log.setSyncTime(new Date());
            log.setBatchNumber(StrUtil.toString(System.currentTimeMillis()));
            this.save(log);

            remoteSyncdataService.receiveMsg(log.getBatchNumber());
        }
    }

    @Override
    public void saveLogBatch(List<SysSyncdataLog> list) {
        if (coreProperties.isBizdataIssuedFlag()){
            String batchNumber = StrUtil.toString(System.currentTimeMillis());
            list.forEach(log -> {
                log.setSyncStatus(SyncStatusEnum.STAY.getCode());
                log.setSyncTime(new Date());
                log.setBatchNumber(batchNumber);
            });
            this.saveBatch(list);

            remoteSyncdataService.receiveMsg(batchNumber);
        }
    }

    @Override
    public List<SysSyncdataLogResp> listStayByBatchNumber(String batchNumber) {
        LambdaQueryWrapper<SysSyncdataLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysSyncdataLog::getBatchNumber, batchNumber);
        queryWrapper.eq(SysSyncdataLog::getSyncStatus, SyncStatusEnum.STAY.getCode());
        queryWrapper.eq(SysSyncdataLog::getDelFlag, DelFlagEnum.NO_DELETED.getCode());

        List<SysSyncdataLog> list = list(queryWrapper);
        List<SysSyncdataLogResp> respList = SysSyncdataLogConvert.INSTANCE.doListToRespList(list);
        return respList;
    }

    @Override
    public void updateSyncStatusByBatchNumber(SysSyncdataLogReq sysSyncdataLogReq) {
        LambdaUpdateWrapper<SysSyncdataLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysSyncdataLog::getBatchNumber, sysSyncdataLogReq.getBatchNumber());
        updateWrapper.set(SysSyncdataLog::getSyncStatus, sysSyncdataLogReq.getSyncStatus());
        update(new SysSyncdataLog(), updateWrapper);
    }
}
