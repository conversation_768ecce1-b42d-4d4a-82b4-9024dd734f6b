package com.platform.system.domain.response;

import com.ctdi.common.starter.toolbox.utils.StringUtils;
import lombok.Data;

/**
 * @Description:
 * @author: tr
 * @date: 2023年12月11日 15:07
 */
@Data
public class SysCacheResp {

    /** 缓存名称 */
    private String cacheName = "";

    /** 缓存键名 */
    private String cacheKey = "";

    /** 缓存内容 */
    private String cacheValue = "";

    /** 备注 */
    private String remark = "";

    public SysCacheResp()
    {

    }

    public SysCacheResp(String cacheName, String remark)
    {
        this.cacheName = cacheName;
        this.remark = remark;
    }

    public SysCacheResp(String cacheName, String cacheKey, String cacheValue)
    {
        this.cacheName = StringUtils.replace(cacheName, ":", "");
        this.cacheKey = StringUtils.replace(cacheKey, cacheName, "");
        this.cacheValue = cacheValue;
    }
}
