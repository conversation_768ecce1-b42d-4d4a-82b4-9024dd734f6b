package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.request.SysCountryAddressQueryReq;
import com.platform.system.api.domain.response.SysCountryAddressResp;
import com.platform.system.domain.SysCountryAddress;

import java.util.List;

/**
 * @Description: 全国地区编码Service接口
 * @author: tr
 * @date: 2024年03月18日 14:54
 */
public interface SysCountryAddressService extends IService<SysCountryAddress> {

    /**
     * @Description: 查询地区编码
     * @author: tr
     * @Date: 2024/3/18 15:35
     * @param: [sysCountryAddressQueryReq]
     * @returnValue: java.util.List<com.platform.system.domain.response.SysCountryAddressResp>
     */
    List<SysCountryAddressResp> list(SysCountryAddressQueryReq sysCountryAddressQueryReq);

    /**
     * @Description: 根据code集合查询地区信息
     * @author: tr
     * @Date: 2024/3/20 9:22
     * @param: [idList]
     * @returnValue: java.util.List<com.platform.system.domain.SysCountryAddress>
     */
    List<SysCountryAddress> listByCodes(List<String> idList);

    /**
     * @Description: 通过code查询地址码信息
     * @author: tr
     * @Date: 2024/5/20 18:33
     * @param: [guCode]
     * @returnValue: com.platform.system.domain.SysCountryAddress
     */
    SysCountryAddress getByCode(String code);

    /**
     * @Description: 根据父级code获取所有子级信息
     * @author: tr
     * @Date: 2024/8/16 14:38
     * @param: [codeList]
     * @returnValue: java.util.List<com.platform.system.domain.SysCountryAddress>
     */
    List<SysCountryAddressResp> listByPCodes(String[] codeList);
}
