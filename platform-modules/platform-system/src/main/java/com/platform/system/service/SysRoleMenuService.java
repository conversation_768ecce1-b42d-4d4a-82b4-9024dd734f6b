package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysRoleMenu;

import java.util.List;

/**
 * @Description: 角色与菜单关系的Service接口
 * @author: tr
 * @date: 2024年04月01日 16:15
 */
public interface SysRoleMenuService extends IService<SysRoleMenu> {

    /**
     * @Description: 根据角色ID查询角色下的菜单信息
     * @author: tr
     * @Date: 2024/4/1 16:15
     * @param: [roleId]
     * @returnValue: java.util.List<com.platform.system.domain.SysRoleMenu>
     */
    List<SysRoleMenu> listByRoleId(Long roleId);

    /**
     * @Description: 根据菜单ID删除角色与菜单的关联信息
     * @author: tr
     * @Date: 2024/11/8 10:55
     * @param: [menuId]
     * @returnValue: void
     */
    void removeByMenuId(Long menuId);
}
