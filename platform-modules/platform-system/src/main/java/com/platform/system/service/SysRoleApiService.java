package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysRoleApi;

import java.util.List;

/**
 * @Description: 角色与API关系Service接口
 * @author: tr
 * @date: 2024年03月20日 16:44
 */
public interface SysRoleApiService extends IService<SysRoleApi> {

    /**
     * @Description: 根据角色ID查询API信息
     * @author: tr
     * @Date: 2024/3/20 17:18
     * @param: [roleId]
     * @returnValue: java.util.List<com.platform.system.domain.SysRoleApi>
     */
    List<SysRoleApi> listByRoleId(Long roleId);
}
