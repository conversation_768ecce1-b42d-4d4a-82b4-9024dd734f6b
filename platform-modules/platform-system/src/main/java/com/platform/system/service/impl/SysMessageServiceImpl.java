package com.platform.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.uuid.IdUtils;
import com.message.api.RemoteSmsMessageService;
import com.message.api.domain.request.MessageInfoDTO;
import com.message.api.domain.response.ResponseInteBean;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.core.enums.MessageStatusEnum;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysUser;
import com.platform.system.config.properties.SmsProperties;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.convert.SysMessageConvert;
import com.platform.system.domain.SysMessage;
import com.platform.system.domain.request.*;
import com.platform.system.domain.response.SmsValidateCodeResp;
import com.platform.system.domain.response.SysMessageResp;
import com.platform.system.mapper.SysMessageMapper;
import com.platform.system.service.SysUserService;
import com.platform.system.service.SysMessageService;
import com.platform.system.service.WebSocketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 站内信消息Service服务层
 * @author: tr
 * @date: 2024年04月16日 15:12
 */
@Slf4j
@Service
public class SysMessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessage> implements SysMessageService {

    @Autowired
    @Lazy
    private WebSocketService webSocketService;

    @Autowired
    private CacheClient cacheClient;

    @Autowired
    private RemoteSmsMessageService remoteSmsMessageService;

    @Autowired
    private SmsProperties smsProperties;

    @Autowired
    private SysUserService sysUserService;

    /** 短信验证码忘记密码的类型 **/
    private final Integer FORGET_TYPE = 2;

    /** 短信验证码修改密码的类型 **/
    private final Integer UPDATE_TYPE = 3;

    @Override
    public void save(SysMessageReq sysMessageReq) {
        Long userId = SecurityUtils.getUserId();

        List<SysMessage> list = new ArrayList();
        Long[] notifierIds = sysMessageReq.getNotifierIds();
        for (Long notifierId : notifierIds){
            SysMessage sysMessage = SysMessageConvert.INSTANCE.reqToDO(sysMessageReq);
            sysMessage.setStatus(MessageStatusEnum.UNREAD.getCode());
            sysMessage.setCreateId(userId);
            sysMessage.setNotifierId(notifierId);
            list.add(sysMessage);
        }

        saveBatch(list);

        //向通知人发送WebSocket消息
        for (Long notifierId : notifierIds){
            webSocketService.sendMessage(notifierId);
        }

    }

    @Override
    public List<SysMessageResp> list(SysMessagePageReq sysMessagePageReq) {
        Long userId = SecurityUtils.getUserId();

        LambdaQueryWrapper<SysMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMessage::getStatus, sysMessagePageReq.getStatus());
        //查询全部可见消息或者属于自己的消息
        queryWrapper.and(Wrapper -> Wrapper.isNull(SysMessage::getNotifierId).or().eq(SysMessage::getNotifierId, userId));
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysMessagePageReq.getType()), SysMessage::getType, sysMessagePageReq.getType());
        queryWrapper.like(StrUtil.isNotBlank(sysMessagePageReq.getTitle()), SysMessage::getTitle, sysMessagePageReq.getTitle());
        queryWrapper.between(StrUtil.isNotBlank(sysMessagePageReq.getBeginTime())
                , SysMessage::getCreateTime, sysMessagePageReq.getBeginTime(), sysMessagePageReq.getEndTime());
        queryWrapper.orderByDesc(SysMessage::getCreateTime);
        List<SysMessage> sysMessageList = list(queryWrapper);
        List<SysMessageResp> list = SysMessageConvert.INSTANCE.doListToRespList(sysMessageList);
        return list;
    }

    @Override
    public SysMessageResp getById(Long id) {
        LambdaQueryWrapper<SysMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMessage::getId, id);
        queryWrapper.eq(SysMessage::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysMessage sysMessage = getOne(queryWrapper);
        if (ObjectUtil.isEmpty(sysMessage)){
            throw new ServiceException("数据不存在!");
        }

        //将消息标记为已读
        LambdaUpdateWrapper<SysMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysMessage::getStatus, MessageStatusEnum.READ.getCode());
        updateWrapper.eq(SysMessage::getId, id);
        update(updateWrapper);

        SysMessageResp sysMessageResp = SysMessageConvert.INSTANCE.doToResp(sysMessage);
        return sysMessageResp;
    }

    @Override
    public void delete(Long[] ids) {
        LambdaUpdateWrapper<SysMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysMessage::getDelFlag, DelFlagEnum.DELETED.getCode());
        updateWrapper.in(SysMessage::getId, ids);
        update(updateWrapper);

        Long userId = SecurityUtils.getUserId();
        webSocketService.sendMessage(userId);
    }

    @Override
    public void updateStatus(Long[] ids) {
        Long userId = SecurityUtils.getUserId();

        LambdaUpdateWrapper<SysMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SysMessage::getStatus, MessageStatusEnum.READ.getCode());
        updateWrapper.eq(SysMessage::getNotifierId, userId);
        updateWrapper.in(ObjectUtil.isNotEmpty(ids), SysMessage::getId, ids);
        update(updateWrapper);

        webSocketService.sendMessage(userId);
    }

    @Override
    public List<SysMessageResp> listLastLimit(Long userId) {
        LambdaQueryWrapper<SysMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMessage::getStatus, MessageStatusEnum.UNREAD.getCode());
        queryWrapper.eq(SysMessage::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.eq(SysMessage::getNotifierId, userId);
        queryWrapper.orderByDesc(SysMessage::getCreateTime);
        queryWrapper.last("limit 100");
        List<SysMessage> sysMessageList = list(queryWrapper);
        List<SysMessageResp> list = SysMessageConvert.INSTANCE.doListToRespList(sysMessageList);
        return list;
    }

    @Override
    public String sendSmsValidate(SmsValidateReq smsValidateReq) {
        String phonenumber = smsValidateReq.getPhonenumber();
        String type =  smsValidateReq.getType();

        if (StrUtil.equals(type, "3") || StrUtil.equals(type, "4") || StrUtil.equals(type, "5")){
            //判断手机号码是否存在
            SysUser sysUser = sysUserService
                    .getOne(new LambdaQueryWrapper<SysUser>()
                            .eq(SysUser::getPhonenumber, phonenumber)
                            .eq(SysUser::getDelFlag, DelFlagEnum.NO_DELETED.getCode()));
            if (ObjectUtil.isEmpty(sysUser)){
                throw new ServiceException("手机号码不存在！");
            }
            if (StrUtil.equals(type, "3")){
                if (!StrUtil.equals(SecurityUtils.getLoginUser().getSysUser().getPhonenumber(), phonenumber)){
                    throw new ServiceException("手机号码与当前用户的手机号码不一致");
                }
            }
        }

        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + smsValidateReq.getValidateUuid();
        String captcha = cacheClient.getCacheObject(verifyKey);
        if (StrUtil.isEmpty(captcha))
        {
            throw new ServiceException("验证码已失效");
        }
        cacheClient.deleteObject(verifyKey);

        if (!StrUtil.equalsIgnoreCase(smsValidateReq.getValidateCode(), captcha))
        {
            throw new ServiceException("验证码错误");
        }

        //type=1表示注册用户的短信验证码
        //短信验证码得uuid
        String uuid = IdUtils.simpleUUID();
        String verifySmsKey = CacheConstants.SMS_CODE_KEY+ type + ":" + phonenumber + ":" + uuid;

        String code = RandomStringUtils.randomNumeric(6);

        MessageInfoDTO messageInfoDTO = getMessageInfo(phonenumber
                , String.format(smsProperties.getValidateLoginContent(),code));
        log.info("方法：sendSmsValidate调用短信平台入参：{}", JSONUtil.toJsonStr(messageInfoDTO));
        ResponseInteBean responseInteBean = remoteSmsMessageService.sendMsgBody(messageInfoDTO);
        log.info("方法：sendSmsValidate调用短信平台出参：{}", JSONUtil.toJsonStr(responseInteBean));
        if (responseInteBean.getStatus() == 200){
            //生成并保存验证码到redis中，5分钟后过期
            cacheClient.setCacheObject(verifySmsKey, code, 5L, TimeUnit.MINUTES);
            return uuid;
        }else{
            throw new ServiceException("验证码获取失败！");
        }
    }

    @Override
    public String sendSmsValidatePwd(String uuid, String certificateNo) {
        boolean flag = cacheClient.hasKey(CacheConstants.CARD_UUID_KEY + uuid);
        if (!flag){
            throw new ServiceException("无效的参数，请重新校验身份证！");
        }
        String certificateNoRedis = cacheClient.get(CacheConstants.CARD_UUID_KEY + uuid);
        if (!StrUtil.equals(certificateNo, certificateNoRedis)){
            throw new ServiceException("无效的参数，请重新校验身份证！");
        }
        String phonenumber = cacheClient.get(CacheConstants.PHONENUMBER_UUID_KEY + uuid);
        String code = RandomStringUtils.randomNumeric(6);

        MessageInfoDTO messageInfoDTO = getMessageInfo(phonenumber, String.format(smsProperties.getForgetPwdContent(),code));
        log.info("方法：sendSmsValidatePwd调用短信平台入参：{}", JSONUtil.toJsonStr(messageInfoDTO));
        ResponseInteBean responseInteBean = remoteSmsMessageService.sendMsgBody(messageInfoDTO);
        log.info("方法：sendSmsValidatePwd调用短信平台出参：{}", JSONUtil.toJsonStr(responseInteBean));
        if (responseInteBean.getStatus() == 200){
            //生成并保存验证码到redis中，5分钟后过期

            //短信验证码得uuid
            String uuidNew = IdUtils.simpleUUID();
            String verifyKey = CacheConstants.SMS_CODE_KEY + FORGET_TYPE + ":" + uuidNew;
            cacheClient.setCacheObject(verifyKey, code, 5L, TimeUnit.MINUTES);
            cacheClient.setCacheObject(CacheConstants.CARD_UUID_KEY + uuidNew, certificateNo, 5L, TimeUnit.MINUTES);

            //移除身份证校验存储的两个key
            cacheClient.delete(CacheConstants.CARD_UUID_KEY + uuid);
            cacheClient.delete(CacheConstants.PHONENUMBER_UUID_KEY + uuid);

            return uuidNew;
        }else{
            throw new ServiceException("验证码获取失败！");
        }
    }

    @Override
    public SmsValidateCodeResp checkSmsValidateCode(SmsValidateCodeReq smsValidateCodeReq) {
        String uuid = smsValidateCodeReq.getUuid();
        String code = smsValidateCodeReq.getCode();

        String verifyKey = CacheConstants.SMS_CODE_KEY + FORGET_TYPE + ":" + uuid;
        String codeRedis = cacheClient.get(verifyKey);
        if (StrUtil.isBlank(code) || !StrUtil.equals(code, codeRedis)){
            throw new ServiceException("验证码错误或已失效!");
        }
        String uuidNew = IdUtils.simpleUUID();
        cacheClient.setCacheObject(CacheConstants.CARD_UUID_KEY + uuidNew
                , cacheClient.get(CacheConstants.CARD_UUID_KEY + uuid), 5L, TimeUnit.MINUTES);

        //加密盐值
        String securitySalt = RandomUtil.randomString(BusinessConstants.SECURITY_SALT_CHARS, 32);
        cacheClient.setCacheObject(CacheConstants.FORGET_PASSWORD_SALT + uuidNew
                , securitySalt, 5L, TimeUnit.MINUTES);

        //删除旧的缓存信息
        cacheClient.delete(verifyKey);
        cacheClient.delete(CacheConstants.CARD_UUID_KEY + uuid);

        SmsValidateCodeResp smsValidateCodeResp = new SmsValidateCodeResp();
        smsValidateCodeResp.setUuid(uuidNew);
        smsValidateCodeResp.setSecuritySalt(securitySalt);
        return smsValidateCodeResp;
    }

    @Override
    public String sendSmsValidateUpdatePwd(UpdatePwdMessageReq updatePwdMessageReq) {
        //获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        SysUser sysUser = sysUserService.getById(userId);
        //旧密码加密
        String oldPassword = updatePwdMessageReq.getOldPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, sysUser.getPassword())){
            throw new ServiceException("旧密码输入错误!");
        }
        if (!StrUtil.equals(updatePwdMessageReq.getNewPassword(), updatePwdMessageReq.getAgainNewPassword())){
            throw new ServiceException("两次输入密码不一致!");
        }
        String phonenumber = updatePwdMessageReq.getPhonenumber();
        String code = RandomStringUtils.randomNumeric(6);
        MessageInfoDTO messageInfoDTO = getMessageInfo(phonenumber, String.format(smsProperties.getUpdatePwdContent(), code));
        log.info("方法：sendSmsValidatePwd调用短信平台入参：{}", JSONUtil.toJsonStr(messageInfoDTO));
        ResponseInteBean responseInteBean = remoteSmsMessageService.sendMsgBody(messageInfoDTO);
        log.info("方法：sendSmsValidatePwd调用短信平台出参：{}", JSONUtil.toJsonStr(responseInteBean));
        if (responseInteBean.getStatus() == 200) {
            //生成并保存验证码到redis中，5分钟后过期

            //短信验证码得uuid
            String uuid = IdUtils.simpleUUID();
            String verifyKey = CacheConstants.SMS_CODE_KEY + UPDATE_TYPE + ":" + uuid;
            cacheClient.setCacheObject(verifyKey, code, 5L, TimeUnit.MINUTES);
            return uuid;
        }else{
            throw new ServiceException("短信验证码发送失败！");
        }
    }

    @Override
    public SmsValidateCodeResp sendSmsValidateEncry(SmsValidateReq smsValidateReq) {
        String uuid = sendSmsValidate(smsValidateReq);
        //加密盐值
        String securitySalt = RandomUtil.randomString(BusinessConstants.SECURITY_SALT_CHARS, 32);
        cacheClient.setCacheObject(CacheConstants.FORGET_PASSWORD_SALT + uuid
                , securitySalt, 5L, TimeUnit.MINUTES);

        SmsValidateCodeResp smsValidateCodeResp = new SmsValidateCodeResp();
        smsValidateCodeResp.setUuid(uuid);
        smsValidateCodeResp.setSecuritySalt(securitySalt);
        return smsValidateCodeResp;
    }

    /**
     * @Description: 获取短信消息主体对象
     * @author: tr
     * @Date: 2024/6/11 20:25
     * @param: []
     * @returnValue: com.message.api.domain.request.MessageInfoDTO
     */
    private MessageInfoDTO getMessageInfo(String telephone, String content){
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setMessageType("1");
        messageInfoDTO.setMessageChannelId(smsProperties.getAppKey());
        messageInfoDTO.setMessageChannelSec(smsProperties.getSecretKey());
        messageInfoDTO.setType(smsProperties.getType());

        List<MessageInfoDTO.MsgBody> msgBodyList = new ArrayList<>();
        MessageInfoDTO.MsgBody msgBody = new MessageInfoDTO.MsgBody();
        msgBody.setMobile(telephone);
        msgBody.setContent(content);
        msgBodyList.add(msgBody);
        messageInfoDTO.setList(msgBodyList);
        return messageInfoDTO;
    }
}
