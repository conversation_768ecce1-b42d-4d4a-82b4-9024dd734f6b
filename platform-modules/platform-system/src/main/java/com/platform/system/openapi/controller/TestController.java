package com.platform.system.openapi.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.security.annotation.RequiresNoLogin;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @RequiresNoLogin
    @PostMapping("/createSign")
    public ResponseResult<Map<String, String>> createSign(@RequestBody GenericDTO genericDTO) throws UnsupportedEncodingException {
        //时间戳，精确到毫秒，5分钟有效期
        Long timestamp = System.currentTimeMillis();
        //唯一字符串
        String nonceStr = UUID.randomUUID().toString();
        //私钥，本地保存
        String sign = genericPreSign(timestamp, nonceStr, genericDTO.getBody(), genericDTO.getAppSecret());
        Map<String, String> result = new HashMap<>();
        result.put("timestamp", timestamp.toString());
        result.put("nonceStr", nonceStr);
        result.put("sign", sign);
        return ResponseResult.ok(result);
    }

    @RequiresNoLogin
    @PostMapping("/decryptSm4")
    public ResponseResult decryptSm4(@RequestBody Map<String, String> param) {
        String key = param.get("key");
        String ciphertext = param.get("ciphertext");
        SM4 sm4 = new SM4(Mode.ECB, Padding.PKCS5Padding, key.getBytes());
        String str = sm4.decryptStr(ciphertext, CharsetUtil.CHARSET_UTF_8);
        if ("1".equals(param.get("type"))){
            return ResponseResult.ok(JSONUtil.parseArray(str));
        }
        return ResponseResult.ok(JSONUtil.parseObj(str));
    }

    /**
     * @Description: 生成签名
     * @author: tr
     * @Date: 2024/8/21 22:02
     * @param timestamp 时间戳
     * @param nonceStr 唯一字符串
     * @param body 参数主体
     * @param appSecret 私钥
     * @returnValue: java.lang.String
     */
    public static String genericPreSign(Long timestamp, String nonceStr, Map<String, Object> body, String appSecret) throws UnsupportedEncodingException {
        SortedMap<String, String> hash = new TreeMap<>();
        // 时间戳
        hash.put("timestamp", timestamp.toString());
        // 随机字符串
        hash.put("nonceStr", nonceStr);
        // 参数
        hash.put("body", JSON.toJSONString(sortMap(body)));
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : hash.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
                sb.append(k).append("=").append(URLEncoder.encode(v, "UTF-8")).append("&");
            }
        }
        sb.append("security=").append(appSecret);
        log.info("\r\n签名串：{}", sb.toString());
        return getSignString(sb.toString());
    }
    public static String getSignString(String preString) {
        return DigestUtils.md5DigestAsHex(preString.getBytes()).toUpperCase();
    }
    private static TreeMap<String, Object> sortMap(Map<String, Object> body) {
        TreeMap<String, Object> treeMap = new TreeMap<>(Optional.ofNullable(body).orElse(new HashMap<>(0)));
        treeMap.forEach((k, v) -> {
            if (v instanceof Map) {
                TreeMap value = sortMap(JSON.parseObject(JSON.toJSONString(v), Map.class));
                treeMap.put(k, value);
            }
        });
        return treeMap;
    }

    @Data
    public static class GenericDTO {
        private String appKey;
        private String appSecret;
        private Map<String, Object> body;
    }

    @Data
    public class Request {
        /**
         * appKey
         */
        private String appKey;

        /**
         * 时间戳
         */
        private Long timestamp;

        /**
         * 随机值
         */
        private String nonceStr;

        /**
         * 签名
         */
        private String sign;

        /**
         * token
         */
        private String token;

        /**
         * 每页显示条数
         */
        private Integer pageSize;

        /**
         * 页码
         */
        private Integer pageNum;

        /**
         * 入参
         */
        private Map<String, Object> body;

        public void put(String key, Object val) {
            if (CollectionUtils.isEmpty(body)) {
                this.body = new HashMap<>(64);
            }

            this.body.put(key, val);
        }

        public void put(Map<String, Object> map) {
            if (CollectionUtils.isEmpty(body)) {
                this.body = new HashMap<>(64);
            }
            this.body.putAll(map);
        }
    }
}
