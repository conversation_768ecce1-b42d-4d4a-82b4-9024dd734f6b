package com.platform.system.controller;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.service.SysUserService;
import com.platform.common.core.constant.CacheConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.page.TableDataInfo;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.api.model.LoginUser;
import com.platform.system.domain.SysUserOnline;
import com.platform.system.service.SysUserOnlineService;

/**
 * 在线用户监控
 * 
 * <AUTHOR>
 */
@Api(tags = "在线用户监控前端控制器")
@RestController
@RequestMapping("/online")
public class SysUserOnlineController extends BaseController
{
    @Autowired
    private SysUserOnlineService userOnlineService;

    @Autowired
    private CacheClient redisService;

    @Autowired
    private SysUserService sysUserService;

    @ApiOperation(value = "在线用户监控列表",  notes = "在线用户监控")
    @RequiresPermissions("monitor:online:list")
    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName)
    {
        Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();

        for (String key : keys)
        {
            String userStr = redisService.getCacheObject(key);
            LoginUser user = JSONUtil.toBean(userStr, LoginUser.class);
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName))
            {
                userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
            }
            else if (StringUtils.isNotEmpty(ipaddr))
            {
                userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
            }
            else if (StringUtils.isNotEmpty(userName))
            {
                userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
            }
            else
            {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
            }
        }
        userOnlineList.removeAll(Collections.singleton(null));
        //筛选
        Long userId = SecurityUtils.getUserId();
        if(!SecurityUtils.isAdmin(SecurityUtils.getUserType())){

            Set<Long> deptSet = sysUserService.getPermissionDeptIdsByUserId(userId);
            if(null == deptSet || deptSet.size() == 0 ){
                //仅限本人
                List<SysUserOnline> collect = userOnlineList.stream().filter(d -> userId.equals(d.getUserId())).sorted(Comparator.comparing(SysUserOnline::getLoginTime,Comparator.reverseOrder())).collect(Collectors.toList());
                return getDataTable(collect);
            }
            List<SysUserOnline> userOnlinePermisionList = new ArrayList<>();
            for (SysUserOnline m : userOnlineList ){
                if(deptSet.contains(m.getDeptId())){
                    userOnlinePermisionList.add(m);
                }
            }
            return getDataTable(userOnlinePermisionList.stream().sorted(Comparator.comparing(SysUserOnline::getLoginTime,Comparator.reverseOrder())).collect(Collectors.toList()));
        }

        return getDataTable(userOnlineList.stream().sorted(Comparator.comparing(SysUserOnline::getLoginTime,Comparator.reverseOrder())).collect(Collectors.toList()));
    }

    /**
     * 强退用户
     */
    @ApiOperation(value = "在线用户强制退出",  notes = "在线用户监控")
    @RequiresPermissions("monitor:online:forceLogout")
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    @DeleteMapping("/{userId}")
    public ResponseResult forceLogout(@PathVariable("userId") String userId)
    {
        Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + userId + CacheConstants.SEPARATOR_KEY + "*");
        redisService.deleteObject(keys);
        return ResponseResult.ok();
    }
}
