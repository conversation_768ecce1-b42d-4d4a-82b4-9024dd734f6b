package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

@Data
@ToString
@TableName("sys_data_scope")
public class SysDataScope  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 数据ID
	 */
	private Long dataId;

	/**
	 * 数据类型（1-角色、2-角色组）
	 */
	private String dataType;

	/**
	 * 机构ID
	 */
	private Long orgId;

	/**
	 * 机构编码
	 */
	private String orgCode;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableField(
		value = "del_flag",
		fill = FieldFill.INSERT
	)
	private String delFlag;

}
