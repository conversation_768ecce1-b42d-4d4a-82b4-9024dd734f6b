package com.platform.system.service;

import java.util.List;
import java.util.Set;

import com.platform.system.api.domain.SysUser;
import com.platform.system.api.model.SysApiDTO;
import com.platform.system.domain.SysApi;

/**
 * 权限信息 服务层
 * 
 * <AUTHOR>
 */
public interface SysPermissionService
{
    /**
     * 获取角色数据权限
     * 
     * @param userId 用户Id
     * @return 角色权限信息
     */
    Set<String> getRolePermission(SysUser user);

    /**
     * 获取菜单数据权限
     * 
     * @param userId 用户Id
     * @return 菜单权限信息
     */
    List<SysApiDTO> getMenuPermission(SysUser user);
}
