package com.platform.system.controller;

import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.common.core.config.properties.CoreProperties;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.UserStatusEnum;
import com.platform.common.core.utils.AESUtils;
import com.platform.common.security.annotation.RequiresNoLogin;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.request.SysUserPageReq;
import com.platform.system.api.domain.request.SysUserReq;
import com.platform.system.api.domain.request.SysUserRestPwdReq;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.convert.SysUserConvert;
import com.platform.system.domain.request.*;
import com.platform.system.domain.response.CertificateNoValidateResp;
import com.platform.system.openapi.domain.response.UserResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.InnerAuth;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysRole;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.model.LoginUser;
import com.platform.system.service.SysDeptService;
import com.platform.system.service.SysRoleService;
import com.platform.system.service.SysUserService;

/**
 * 用户信息
 * 
 * <AUTHOR>
 */
@Api(tags = "用户信息前端控制器")
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController
{
    @Autowired
    private SysUserService userService;

    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysDeptService deptService;

    @Autowired
    private CacheClient redisService;

    @Autowired
    private CoreProperties coreProperties;

    /**
     * 获取用户列表
     */
    @ApiOperation(value = "获取用户列表分页",  notes = "用户信息")
    @RequiresPermissions("system:user:page")
    @PostMapping("/page")
    public ResponseResult<PageResult<SysUserResp>> page(@RequestBody SysUserPageReq sysUserPageReq)
    {
        if (StrUtil.isNotBlank(sysUserPageReq.getRoleCode())){
            SysRole sysRole = roleService.getByCode(sysUserPageReq.getRoleCode());
            sysUserPageReq.setRoleId(sysRole.getRoleId());
        }

        Page<SysRoleResp> page = PageUtils.startPage(sysUserPageReq.getPageNum(), sysUserPageReq.getPageSize());
        List<SysUserResp> list = userService.selectUserList(sysUserPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "获取用户列表",  notes = "用户信息")
    @PostMapping("/list")
    public ResponseResult<List<SysUserResp>> list(@RequestBody SysUserPageReq sysUserPageReq)
    {
        if (StrUtil.isNotBlank(sysUserPageReq.getRoleCode())){
            SysRole sysRole = roleService.getByCode(sysUserPageReq.getRoleCode());
            sysUserPageReq.setRoleId(sysRole.getRoleId());
        }

        List<SysUserResp> list = userService.selectUserList(sysUserPageReq);
        return ResponseResult.ok(list);
    }

    @ApiOperation(value = "导出用户",  notes = "用户信息")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody SysUserPageReq sysUserPageReq)
    {
        List<SysUserResp> list = userService.selectUserList(sysUserPageReq);
        ExcelUtil<SysUserResp> util = new ExcelUtil<>(SysUserResp.class);
        util.exportExcel(response, list, "用户数据");
    }

    @ApiOperation(value = "导入用户信息",  notes = "用户信息")
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    public ResponseResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return ResponseResult.ok(message);
    }

    @ApiOperation(value = "用户信息导入模板",  notes = "用户信息")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 获取当前用户信息
     */
    @ApiOperation(value = "根据用户名获取用户详细信息",  notes = "用户信息")
    @InnerAuth
    @GetMapping("/info/{username}")
    public ResponseResult<LoginUser> info(@PathVariable("username") String username)
    {
        LoginUser loginUser = userService.loginUserInfo(username);
        return ResponseResult.ok(loginUser);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @ApiOperation(value = "根据用户编号获取详细信息",  notes = "用户信息")
    @RequiresPermissions("system:user:page")
    @GetMapping(value = { "/", "/{userId}" })
    public ResponseResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        userService.checkUserDataScope(userId);
        if (StringUtils.isNotNull(userId))
        {
            SysUserResp sysUserResp = userService.getUserById(userId);
            sysUserResp.setRoleIds(sysUserResp.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()).toArray(new Long[0]));
            return ResponseResult.ok(sysUserResp);
        }
        return ResponseResult.ok();
    }

    /**
     * 新增用户
     */
    @ApiOperation(value = "新增用户",  notes = "用户信息")
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ResponseResult add(@Validated @RequestBody SysUserReq sysUserReq)
    {
        userService.insertUser(sysUserReq);
        return ResponseResult.ok();
    }

    /**
     * 修改用户
     */
    @ApiOperation(value = "修改用户",  notes = "用户信息")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult edit(@Validated @RequestBody SysUserReq sysUserReq)
    {
        userService.updateUser(sysUserReq);
        return ResponseResult.ok();
    }

    /**
     * 删除用户
     */
    @ApiOperation(value = "删除用户",  notes = "用户信息")
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public ResponseResult remove(@PathVariable Long[] userIds)
    {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId()))
        {
            return ResponseResult.fail("当前用户不能删除");
        }
        return ResponseResult.ok(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @ApiOperation(value = "重置密码",  notes = "用户信息")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public ResponseResult resetPwd(@Validated @RequestBody SysUserRestPwdReq sysUserRestPwdReq)
    {
        SysUser user = new SysUser();
        user.setUserId(sysUserRestPwdReq.getUserId());
        user.setPassword(sysUserRestPwdReq.getPassword());
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(AESUtils.decrypt(user.getPassword(), this.coreProperties.getPwdEncrypKey()));
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return ResponseResult.ok(userService.resetPwd(user));
    }

    /**
     * 重置密码
     */
    @ApiOperation(value = "重置自己密码", notes = "用户信息")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetUserPwd")
    public ResponseResult resetUserPwd(@Validated @RequestBody PwdUpdateReq pwdUpdateReq) {
        SysUser user = userService.getById(pwdUpdateReq.getUserId());
        user.setPassword(AESUtils.decrypt(user.getPassword(), this.coreProperties.getPwdEncrypKey()));
        if (SecurityUtils.matchesPassword(pwdUpdateReq.getOldPassword(), user.getPassword())) {
            user.setUserId(pwdUpdateReq.getUserId());
            user.setPassword(SecurityUtils.encryptPassword(pwdUpdateReq.getNewPassword()));
            user.setUpdateBy(SecurityUtils.getUsername());
            return ResponseResult.ok(userService.resetPwd(user));
        } else {
            return ResponseResult.fail("旧密码错误");
        }
    }

    /**
     * 状态修改
     */
    @ApiOperation(value = "修改用户状态",  notes = "用户信息")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public ResponseResult changeStatus(@RequestBody SysUserReq sysUserReq)
    {
        SysUser user = SysUserConvert.INSTANCE.reqToDO(sysUserReq);
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserStatus(user);

        if(StrUtil.equals(user.getStatus(), UserStatusEnum.DISABLE.getCode())){
            //停用情况下 需要删除该账户在缓存中的token信息
            Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + user.getUserId() + CacheConstants.SEPARATOR_KEY + "*");
            redisService.deleteObject(keys);
        }

        return ResponseResult.ok();
    }

    /**
     * 根据用户编号获取授权角色
     */
    @ApiOperation(value = "根据用户编号获取授权角色",  notes = "用户信息")
    @RequiresPermissions("system:user:page")
    @GetMapping("/authRole/{userId}")
    public ResponseResult authRole(@PathVariable("userId") Long userId)
    {
        Map<String, Object> result = new HashMap<>();
        SysUserResp sysUserResp = userService.selectUserById(userId);
        List<SysRoleResp> roles = roleService.selectRolesByUserId(userId);
        result.put("user", sysUserResp);
        result.put("roles", SecurityUtils.isAdmin(sysUserResp.getUserType()) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ResponseResult.ok(result);
    }

    /**
     * 用户授权角色
     */
    @ApiOperation(value = "用户授权角色",  notes = "用户信息")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public ResponseResult insertAuthRole(@RequestBody SysUserReq sysUserReq)
    {
        SysUser user = SysUserConvert.INSTANCE.reqToDO(sysUserReq);
        userService.checkUserDataScope(user.getUserId());
        userService.insertUserAuth(user.getUserId(), user.getRoleIds());
        return ResponseResult.ok();
    }

    /**
     * 获取部门树列表
     */
    @ApiOperation(value = "获取部门树列表",  notes = "用户信息")
    @RequiresPermissions("system:user:page")
    @GetMapping("/deptTree")
    public ResponseResult deptTree(SysDeptQueryReq sysDeptQueryReq)
    {
        return ResponseResult.ok(deptService.selectDeptTreeList(sysDeptQueryReq));
    }

    /**
     * @Description: 获取当前用户信息
     * @author: tr
     * @Date: 2024/2/6 16:19
     * @param: []
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<com.platform.system.api.model.LoginUser>
     */
    @GetMapping("/getCurrUser")
    public ResponseResult<LoginUser> getCurrUserInfo(){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return ResponseResult.ok(loginUser);
    }

    /**
     * @Description: 根据用户ID查询单个用户
     * @author: tr
     * @Date: 2024/2/27 16:04
     * @param: [userId]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<com.platform.system.api.domain.response.SysUserResp>
     */
    @GetMapping("/getById")
    public ResponseResult<SysUserResp> info(@RequestParam Long userId){
        SysUserResp sysUserResp = userService.selectUserById(userId);
        return ResponseResult.ok(sysUserResp);
    }

    @ApiOperation(value = "根据角色ID集合查询用户信息",  notes = "用户信息")
    @GetMapping("/listByRoleIds")
    public ResponseResult<List<SysUserResp>> listByRoleIds(Long[] roldIdList){
        List<SysUserResp> list = userService.listByRoleIds(roldIdList);
        return ResponseResult.ok(list);
    }

    /**
     * @Description: 根据组织机构ID集合查询用户信息列表
     * @author: tr
     * @Date: 2024/4/16 10:03
     * @param orgIdList 组织机构ID数组
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<java.util.List<com.platform.system.api.domain.response.SysUserResp>>
     */
    @ApiOperation(value = "根据组织机构ID集合查询用户信息列表",  notes = "用户信息")
    @GetMapping("/listByOrgIds")
    public ResponseResult<List<SysUserResp>> listByOrgIds(Long[] orgIdList){
        List<SysUserResp> list = userService.listByOrgIds(orgIdList);
        return ResponseResult.ok(list);
    }

    /**
     * @Description: 根据组织机构ID和角色ID查询用户信息集合
     * @author: tr
     * @Date: 2024/4/16 10:29
     * @param orgIdList 组织机构ID数组
     * @param roldIdList 角色ID数组
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<java.util.List<com.platform.system.api.domain.response.SysUserResp>>
     */
    @GetMapping("/listByOrgIdsAndRoleIds")
    public ResponseResult<List<SysUserResp>> listByOrgIdsAndRoleIds(Long[] orgIdList, Long[] roldIdList){
        List<SysUserResp> list = userService.listByOrgIdsAndRoleIds(orgIdList, roldIdList);
        return ResponseResult.ok(list);
    }

    /**
     * @Description: 根据角色ID查询和组织机构管辖范围地址码查询本级及以上的用户信息
     * @author: tr
     * @Date: 2024/5/20 18:02
     * @param roleCode 角色编码
     * @param guCode 管辖范围地址码
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<java.util.List<com.platform.system.api.domain.response.SysUserResp>>
     */
    @GetMapping("/listByRoleCodeAndGuCode")
    public ResponseResult<List<SysUserResp>> listByRoleCodeAndGuCode(String roleCode, String guCode){
        List<SysUserResp> list = userService.listByRoleCodeAndGuCode(roleCode, guCode);
        return ResponseResult.ok(list);
    }

    /**
     * 批量修改用户信息
     */
    @ApiOperation(value = "批量修改用户信息",  notes = "用户信息")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateBatch")
    public ResponseResult updateBatch(@Validated @RequestBody List<SysUserReq> sysUserReqList)
    {
        userService.updateBatch(sysUserReqList);
        return ResponseResult.ok();
    }

    @RequiresNoLogin
    @ApiOperation(value = "通过证件号码获取手机号码",  notes = "用户信息")
    @PostMapping("/getMobileByCard")
    public ResponseResult<CertificateNoValidateResp> getMobileByCard(@RequestBody CertificateNoValidateReq certificateNoValidateReq){
        CertificateNoValidateResp certificateNoValidateResp = userService.getMobileByCard(certificateNoValidateReq);
        return ResponseResult.ok(certificateNoValidateResp);
    }

    @RequiresNoLogin
    @ApiOperation(value = "忘记密码修改密码",  notes = "用户信息")
    @PutMapping("/updateForgetPwd")
    public ResponseResult updateForgetPwd(@RequestBody ForgetPwdReq forgetPwdReq){
        userService.updateForgetPwd(forgetPwdReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "登录成功，修改密码",  notes = "用户信息")
    @PutMapping("/updatePwd")
    public ResponseResult updatePwd(@Validated @RequestBody UpdatePwdReq updatePwdReq){
        userService.updatePwd(updatePwdReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "登录成功，修改个人信息",  notes = "用户信息")
    @PutMapping("/updatePersonalInfo")
    public ResponseResult updatePersonalInfo(@RequestBody SysUserReq sysUserReq){
        userService.updatePersonalInfo(sysUserReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "通过部门、姓名创建账号", notes = "账号创建")
    @PostMapping("/createUserByDeptAndName")
    @RequiresNoLogin
    public ResponseResult createUserByDeptAndName(@RequestBody SysUserReq sysUserReq) {
        SysUser result = userService.createUserByDeptAndName(sysUserReq.getDeptId(), sysUserReq.getUserName());
        return ResponseResult.ok(result);
    }

    @ApiOperation(value = "设置密码，需先调用/message/sendSmsValidate接口获取验证码", notes = "用户信息")
    @PostMapping("/setupPassword")
    public ResponseResult setupPassword(@RequestBody SetupPwdReq setupPwdReq){
        userService.setupPassword(setupPwdReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "修改手机号码下一步，会返回唯一的UUID", notes = "用户信息")
    @PostMapping("/updatePhonenumberNext")
    public ResponseResult<String> updatePhonenumberNext(@RequestBody UpdatePhonenumberReq updatePhonenumberReq){
        String uuid = userService.updatePhonenumberNext(updatePhonenumberReq);
        return ResponseResult.ok(uuid);
    }

    @ApiOperation(value = "修改手机号码", notes = "用户信息")
    @PostMapping("/updatePhonenumber")
    public ResponseResult updatePhonenumber(@RequestBody UpdatePhonenumberReq updatePhonenumberReq){
        userService.updatePhonenumber(updatePhonenumberReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "忘记密码，需先调用/message/sendSmsValidate接口获取验证码",  notes = "用户信息")
    @PutMapping("/updateForgetPhonenumberPwd")
    public ResponseResult updateForgetPhonenumberPwd(@RequestBody ForgetPwdPhonenumberReq forgetPwdPhonenumberReq){
        userService.updateForgetPhonenumberPwd(forgetPwdPhonenumberReq);
        return ResponseResult.ok();
    }

    @GetMapping("/listByIdList")
    public ResponseResult<List<SysUserResp>> listByIdList(Long[] userIdList){
        List<SysUserResp> list = userService.listByIdList(userIdList);
        return ResponseResult.ok(list);
    }

    @ApiOperation(value = "切换机构和角色",  notes = "用户信息")
    @PutMapping("/switchOrgAndRole")
    public ResponseResult switchOrgAndRole(@RequestBody SwitchOrgAndRoleReq switchOrgAndRoleReq){
        userService.switchOrgAndRole(switchOrgAndRoleReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "校验Token有效性",  notes = "用户信息")
    @GetMapping("/validateToken")
    public ResponseResult<UserResp> validateToken(HttpServletRequest request){
        String token = request.getHeader("Authorization");
        UserResp userResp = userService.validateToken(token);
        return ResponseResult.ok(userResp);
    }
}
