package com.platform.system.login.domain.model;

import com.platform.system.api.model.LoginUser;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月27日 9:03
 */
@Data
public class SysUserDetails extends LoginUser implements UserDetails, Serializable {

    private static final long serialVersionUID = 1L;

    /** 密码 **/
    private String password;

    /**
     * 账号是否过期
     */
    private boolean isAccountNonExpired = true;

    /**
     * 账号是否锁定
     */
    private boolean isAccountNonLocked = true;

    /**
     * 证书是否过期
     */
    private boolean isCredentialsNonExpired = true;

    /**
     * 账号是否有效
     */
    private boolean isEnabled = true;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return new ArrayList();
    }

    @Override
    public boolean isAccountNonExpired() {
        return isAccountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return isAccountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return isCredentialsNonExpired;
    }

    @Override
    public boolean isEnabled() {
        return isEnabled;
    }

//    private String getRequest() {
//        String loginUserId = getUserId();
//        if (StringUtils.isNotEmpty(loginUserId)) {
////            loginUserId = loginUserId.substring(JjwtConfig.tokenPrefix.length());
//        }
//        return loginUserId;
//    }
//
//    private String getUserId() {
//        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
//        assert requestAttributes != null;
//        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
//        // 子线程共享
//        RequestContextHolder.setRequestAttributes(requestAttributes, true);
//        return request.getHeader("Authorization");
//    }

    @Override
    public String getUsername() {
        return super.getUserName();
    }
}
