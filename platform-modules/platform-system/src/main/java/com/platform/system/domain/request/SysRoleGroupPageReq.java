package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 角色组分页查询入参
 * @author: tr
 * @date: 2024年03月01日 10:17
 */
@ApiModel("角色组分页查询入参")
@Data
@ToString
public class SysRoleGroupPageReq extends PageReq {

	private static final long serialVersionUID = 1L;

	/**
	 * 角色组名称
	 */
	@ApiModelProperty("角色组名称")
	private String name;

	/**
	 * 角色组编码
	 */
	@ApiModelProperty("角色组编码")
	private String code;

	/**
	 * 组织机构类型
	 */
	@ApiModelProperty("组织机构类型")
	private Integer orgType;

	/**
	 * 角色组ID
	 */
	@ApiModelProperty("角色组ID")
	private Long id;
}
