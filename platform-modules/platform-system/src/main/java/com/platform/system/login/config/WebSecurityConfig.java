package com.platform.system.login.config;

import com.platform.common.core.config.properties.JwtProperties;
import com.platform.system.login.config.code.UserPwdAuthenticationSecurityConfig;
import com.platform.system.login.config.mobilephone.MobilePhoneAuthenticationSecurityConfig;
import com.platform.system.login.config.single.SingleAuthenticationSecurityConfig;
import com.platform.system.login.config.third.ThirdAuthenticationSecurityConfig;
import com.platform.system.login.filter.JwtFilter;
import com.platform.system.login.handler.UserAccessDeniedHandler;
import com.platform.system.login.handler.UserAuthenticationEntryPoint;
import com.platform.system.login.handler.UserLogoutSuccessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月26日 15:26
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private UserAccessDeniedHandler userAccessDeniedHandler;

    private UserAuthenticationEntryPoint userAuthenticationEntryPoint;

    private UserLogoutSuccessHandler userLogoutSuccessHandler;

    private UserPwdAuthenticationSecurityConfig userPwdAuthenticationSecurityConfig;

    private ThirdAuthenticationSecurityConfig thirdAuthenticationSecurityConfig;

    private MobilePhoneAuthenticationSecurityConfig mobilePhoneAuthenticationSecurityConfig;

    private SingleAuthenticationSecurityConfig singleAuthenticationSecurityConfig;

    private final JwtFilter jwtFilter;

    @Autowired
    public WebSecurityConfig(UserAccessDeniedHandler userAccessDeniedHandler,
                             UserAuthenticationEntryPoint userAuthenticationEntryPoint,
                             UserLogoutSuccessHandler userLogoutSuccessHandler,
                             UserPwdAuthenticationSecurityConfig userPwdAuthenticationSecurityConfig,
                             ThirdAuthenticationSecurityConfig thirdAuthenticationSecurityConfig,
                             MobilePhoneAuthenticationSecurityConfig mobilePhoneAuthenticationSecurityConfig,
                             SingleAuthenticationSecurityConfig singleAuthenticationSecurityConfig,
                             JwtFilter jwtFilter){
        this.userAccessDeniedHandler = userAccessDeniedHandler;
        this.userAuthenticationEntryPoint = userAuthenticationEntryPoint;
        this.userLogoutSuccessHandler = userLogoutSuccessHandler;
        this.userPwdAuthenticationSecurityConfig = userPwdAuthenticationSecurityConfig;
        this.thirdAuthenticationSecurityConfig = thirdAuthenticationSecurityConfig;
        this.mobilePhoneAuthenticationSecurityConfig = mobilePhoneAuthenticationSecurityConfig;
        this.singleAuthenticationSecurityConfig = singleAuthenticationSecurityConfig;
        this.jwtFilter = jwtFilter;

    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
                //登录后,访问没有权限处理类
                .exceptionHandling().accessDeniedHandler(userAccessDeniedHandler)
                //匿名访问,没有权限的处理类
                .authenticationEntryPoint(userAuthenticationEntryPoint)
                //退出登录
                .and()
                .logout().logoutUrl("/logout").logoutSuccessHandler(userLogoutSuccessHandler)
                .and()
                .authorizeRequests()
                // 对于登录login 注册register 验证码captchaImage 允许匿名访问
                .antMatchers(JwtProperties.antMatchers).permitAll()// 获取白名单（不进行权限验证）
                .anyRequest().authenticated()
                //无状态登录，取消session管理
                .and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
                .apply(userPwdAuthenticationSecurityConfig)
                .and().apply(thirdAuthenticationSecurityConfig)
                .and().apply(mobilePhoneAuthenticationSecurityConfig)
                .and().apply(singleAuthenticationSecurityConfig)
                .and().rememberMe();
    }
}
