package com.platform.system.controller;

import cn.hutool.json.JSONUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.domain.request.ReceiveParamsReq;
import com.platform.system.service.ReceiveParamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @author: tr
 * @date: 2024年01月26日 9:47
 */
@Api(tags = "消息通道配置前端控制器")
@RestController
@Slf4j
@RequestMapping("/receive")
public class ReceiveParamsController extends BaseController {

    @Autowired
    ReceiveParamsService receiveParamsService;

    @ApiOperation(value = "接收消息",  notes = "接收消息")
    @PostMapping("/receiveMsg")
    public ResponseResult receiveMsg(@RequestBody ReceiveParamsReq receiveParamsReq){
        receiveParamsService.receiveMsg(receiveParamsReq);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "接收消息",  notes = "接收消息")
    @PostMapping("/receiveMsg1")
    public ResponseResult receiveMsg1(@RequestBody ReceiveParamsReq receiveParamsReq){
        log.info("receiveMsg1-开始：{}", JSONUtil.toJsonStr(receiveParamsReq));
        return ResponseResult.ok();
    }
}
