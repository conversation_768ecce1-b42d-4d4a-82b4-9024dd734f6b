package com.platform.system.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;


/**
 * @Description: 站内信出参
 * @author: tr
 * @date: 2024年04月16日 15:35
 */
@ApiModel("站内信出参")
@Data
@ToString
public class SysMessageResp {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	 * 应用编码名称
	 */
	@ApiModelProperty("应用编码名称")
	private String appCodeName;

	/**
	 * 标题
	 */
	@ApiModelProperty("标题")
	private String title;

	/**
	 * 消息内容
	 */
	@ApiModelProperty("消息内容")
	private String content;

	/** 创建时间 **/
	@ApiModelProperty("创建时间")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date createTime;
}
