package com.platform.system.convert;

import com.platform.system.api.domain.SysDept;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.api.model.SysDeptDTO;
import com.platform.system.domain.request.SysDeptReq;
import com.platform.system.openapi.domain.response.DeptResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 部门信息转换
 * @author: tr
 * @date: 2024年02月02日 16:05
 */
@Mapper
public interface SysDeptConvert {

    SysDeptConvert INSTANCE = Mappers.getMapper(SysDeptConvert.class);

    SysDept queryReqToDO(SysDeptQueryReq sysDeptQueryReq);

    SysDeptResp doToResp(SysDept sysDept);

    List<SysDeptResp> doListToRespList(List<SysDept> sysDeptList);

    SysDeptDTO respToDTO(SysDeptResp sysDeptResp);

    SysDept reqToDO(SysDeptReq sysDeptReq);

    List<DeptResp> doListToRespListOpenApi(List<SysDept> sysDeptList);
}
