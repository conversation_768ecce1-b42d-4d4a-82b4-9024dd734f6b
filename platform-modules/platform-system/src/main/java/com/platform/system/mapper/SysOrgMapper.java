package com.platform.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.platform.common.datascope.annotation.DataScope;
import com.platform.system.api.domain.request.SysOrgPageReq;
import com.platform.system.api.domain.response.SysOrgResp;
import com.platform.system.domain.SysOrg;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 组织机构Mapper层
 * @author: tr
 * @date: 2024年03月15日 10:07
 */
@Mapper
public interface SysOrgMapper extends BaseMapper<SysOrg> {

    /**
     * @Description: 查询用户ID所属的组织机构信息
     * @author: tr
     * @Date: 2024/3/20 11:10
     * @param: [userId]
     * @returnValue: com.platform.system.domain.SysOrg
     */
    List<SysOrgResp> listByUserId(Long userId);

    /**
     * @Description: 根据条件查询组织机构信息
     * @author: tr
     * @Date: 2024/5/15 14:09
     * @param: [sysOrgPageReq]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysOrgResp>
     */
    @DataScope(permissionType = 2, permissionField = "a.id")
    List<SysOrgResp> listAllByCondition(SysOrgPageReq sysOrgPageReq);
}
