package com.platform.system.controller;

import java.util.List;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.security.annotation.RequiresNoLogin;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.domain.request.SysDeptReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.platform.common.core.constant.UserConstants;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.service.SysDeptService;

/**
 * 部门信息
 * 
 * <AUTHOR>
 */
@Api(tags = "部门信息")
@RestController
@RequestMapping("/dept")
public class SysDeptController extends BaseController
{
    @Autowired
    private SysDeptService deptService;

    /**
     * 获取部门列表
     */
    @ApiOperation(value = "获取部门列表",  notes = "部门信息")
    @RequiresPermissions("system:dept:list")
    @PostMapping("/list")
    public ResponseResult<List<SysDeptResp>> list(@RequestBody SysDeptQueryReq sysDeptQueryReq)
    {
        List<SysDeptResp> depts = deptService.selectDeptList(sysDeptQueryReq);
        return ResponseResult.ok(depts);
    }

    /**
     * 获取部门列表:自助创建用户时使用
     */
    @ApiOperation(value = "获取部门列表",  notes = "部门信息")
    @PostMapping("/getlist")
    @RequiresNoLogin
    public ResponseResult<List<SysDeptResp>> getlist(@RequestBody SysDeptQueryReq sysDeptQueryReq)
    {
        List<SysDeptResp> depts = deptService.selectDeptList(sysDeptQueryReq);
        return ResponseResult.ok(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @ApiOperation(value = "查询部门列表（排除节点）",  notes = "部门信息")
    @RequiresPermissions("system:dept:list")
    @GetMapping("/list/exclude/{deptId}")
    public ResponseResult<List<SysDeptResp>> excludeChild(@PathVariable(value = "deptId", required = false) Long deptId, @RequestParam(required = false) Long orgId)
    {
        List<SysDeptResp> depts = deptService.selectDeptList(new SysDeptQueryReq());
        depts.removeIf(d ->
                d.getOrgId().longValue() != orgId ||
                d.getDeptId().longValue() == deptId
                        || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + "")
        );
        return ResponseResult.ok(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @ApiOperation(value = "根据部门编号获取详细信息",  notes = "部门信息")
    @RequiresPermissions("system:dept:query")
    @GetMapping(value = "/{deptId}")
    public ResponseResult<SysDeptResp> getInfo(@PathVariable Long deptId)
    {
        deptService.checkDeptDataScope(deptId);
        return ResponseResult.ok(deptService.selectDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @ApiOperation(value = "新增部门",  notes = "部门信息")
    @RequiresPermissions("system:dept:add")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ResponseResult add(@Validated @RequestBody SysDeptReq sysDeptReq)
    {
        if (!deptService.checkDeptNameUnique(sysDeptReq))
        {
            return ResponseResult.fail("新增部门'" + sysDeptReq.getDeptName() + "'失败，部门名称已存在");
        }
        return ResponseResult.ok(deptService.insertDept(sysDeptReq));
    }

    /**
     * 修改部门
     */
    @ApiOperation(value = "修改部门",  notes = "部门信息")
    @RequiresPermissions("system:dept:edit")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult edit(@Validated @RequestBody SysDeptReq sysDeptReq)
    {
        Long deptId = sysDeptReq.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(sysDeptReq))
        {
            return ResponseResult.fail("修改部门'" + sysDeptReq.getDeptName() + "'失败，部门名称已存在");
        }
        else if (sysDeptReq.getParentId().equals(deptId))
        {
            return ResponseResult.fail("修改部门'" + sysDeptReq.getDeptName() + "'失败，上级部门不能是自己");
        }
        else if (StringUtils.equals(UserConstants.DEPT_DISABLE, sysDeptReq.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0)
        {
            return ResponseResult.fail("该部门包含未停用的子部门！");
        }
        return ResponseResult.ok(deptService.updateDept(sysDeptReq));
    }

    /**
     * 删除部门
     */
    @ApiOperation(value = "删除部门",  notes = "部门信息")
    @RequiresPermissions("system:dept:remove")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public ResponseResult remove(@PathVariable Long deptId)
    {
        if (deptService.hasChildByDeptId(deptId))
        {
            return ResponseResult.fail("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId))
        {
            return ResponseResult.fail("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return ResponseResult.ok(deptService.deleteDeptById(deptId));
    }

    /**
     * @Description: 根据部门ID查询单个部门
     * @author: lizhy
     * @Date: 2024/2/27 16:04
     * @param: [deptId]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<com.platform.system.api.domain.response.SysDeptResp>
     */
    @GetMapping("/getById")
    public ResponseResult<SysDeptResp> info(@RequestParam Long deptId){
        SysDeptResp sysDeptResp = deptService.selectDeptById(deptId);
        return ResponseResult.ok(sysDeptResp);
    }
}
