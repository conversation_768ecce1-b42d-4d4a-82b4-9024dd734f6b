package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.model.LoginUser;
import com.platform.system.domain.request.SysUserSubjectReq;
import com.platform.system.domain.response.SysUserSubjectResp;
import com.platform.system.mapper.SysUserSubjectMapper;
import com.platform.system.service.SysUserSubject;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: 用户主题信息Service层
 * @author: tr
 * @date: 2023年12月11日 10:18
 */
@Service
public class SysUserSubjectService extends ServiceImpl<SysUserSubjectMapper, com.platform.system.domain.SysUserSubject> implements SysUserSubject {

    @Override
    public void add(SysUserSubjectReq sysUserSubjectReq) {
        //获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //新增之前先根据用户ID删除数据
        LambdaUpdateWrapper<com.platform.system.domain.SysUserSubject> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(com.platform.system.domain.SysUserSubject::getUserId, loginUser.getUserId());
        remove(updateWrapper);

        com.platform.system.domain.SysUserSubject sysUserSubject = new com.platform.system.domain.SysUserSubject();
        sysUserSubject.setSubjectInfo(sysUserSubjectReq.getSubjectInfo());
        sysUserSubject.setUserId(loginUser.getUserId());
        sysUserSubject.setCreateBy(loginUser.getUserName());
        sysUserSubject.setCreateTime(new Date());
        sysUserSubject.setUpdateBy(loginUser.getUserName());
        sysUserSubject.setUpdateTime(new Date());
        this.save(sysUserSubject);
    }

    @Override
    public SysUserSubjectResp getOneByUser() {
        LambdaQueryWrapper<com.platform.system.domain.SysUserSubject> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(com.platform.system.domain.SysUserSubject::getUserId, SecurityUtils.getLoginUser().getUserId());
        queryWrapper.eq(com.platform.system.domain.SysUserSubject::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        com.platform.system.domain.SysUserSubject sysUserSubject = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(sysUserSubject)){
            return new SysUserSubjectResp();
        }
        SysUserSubjectResp sysUserSubjectResp = new SysUserSubjectResp();
        BeanUtils.copyProperties(sysUserSubject, sysUserSubjectResp);
        return sysUserSubjectResp;
    }
}
