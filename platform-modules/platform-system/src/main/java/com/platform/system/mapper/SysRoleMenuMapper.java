package com.platform.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.platform.system.domain.SysRoleMenu;
import org.apache.ibatis.annotations.Mapper;

/**
 * 角色与菜单关联表 数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu>
{

    /**
     * 通过角色ID删除角色和菜单关联
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteRoleMenuByRoleId(Long roleId);

    /**
     * 批量删除角色菜单关联信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleMenu(Long[] ids);

}
