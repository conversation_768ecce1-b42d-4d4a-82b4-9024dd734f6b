package com.platform.system.login.provider;

import com.platform.system.login.config.mobilephone.MobilePhoneAuthenticationToken;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.login.domain.req.MobilePhoneReq;
import com.platform.system.login.service.impl.MobilePhoneUserDetailServiceImpl;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * @Description: 手机号码认证验证提供者
 * @author: tr
 * @date: 2025年02月14日 08:43
 */
@Component
public class MobilePhoneAuthenticationProvider implements AuthenticationProvider {

    private MobilePhoneUserDetailServiceImpl userDetailService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        MobilePhoneAuthenticationToken authenticationToken = (MobilePhoneAuthenticationToken) authentication;
        MobilePhoneReq mobilePhoneReq = (MobilePhoneReq)authentication.getPrincipal();

        SysUserDetails sysUserDetails = userDetailService.loadUserByUsername(mobilePhoneReq.getPhonenumber());

        MobilePhoneAuthenticationToken userPwdAuthenticationToken =
                new MobilePhoneAuthenticationToken(sysUserDetails, authenticationToken.getAuthorities());

        return userPwdAuthenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(MobilePhoneAuthenticationToken.class);
    }

    public void setUserDetailsService(MobilePhoneUserDetailServiceImpl userDetailsService) {
        this.userDetailService = userDetailsService;
    }
}
