package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import com.ctdi.common.starter.toolbox.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月01日 19:40
 */
@Data
@ToString
public class SysOperLogPageReq extends PageReq {

    /** 操作模块 */
    @ApiModelProperty(value = "操作模块")
    @Excel(name = "操作模块")
    private String title;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    @ApiModelProperty(value = "业务类型（0其它 1新增 2修改 3删除）")
    @Excel(name = "业务类型", readConverterExp = "0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据")
    private Integer businessType;

    /** 操作人员 */
    @ApiModelProperty(value = "操作人员")
    @Excel(name = "操作人员")
    private String operName;

    /** 操作地址 */
    @ApiModelProperty(value = "操作地址")
    @Excel(name = "操作地址")
    private String operIp;

    /** 操作状态（0正常 1异常） */
    @ApiModelProperty(value = "操作状态（0正常 1异常）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private Integer status;
}
