package com.platform.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.SysDept;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.domain.request.SysDeptReq;
import com.platform.system.domain.vo.TreeSelect;

/**
 * 部门管理 服务层
 * 
 * <AUTHOR>
 */
public interface SysDeptService extends IService<SysDept>
{
    /**
     * 查询部门管理数据
     * 
     * @param sysDeptQueryReq 部门信息
     * @return 部门信息集合
     */
    public List<SysDeptResp> selectDeptList(SysDeptQueryReq sysDeptQueryReq);

    /**
     * 查询部门树结构信息
     * 
     * @param sysDeptQueryReq 部门信息
     * @return 部门树信息集合
     */
    public List<TreeSelect> selectDeptTreeList(SysDeptQueryReq sysDeptQueryReq);

    /**
     * 构建前端所需要树结构
     * 
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDeptResp> buildDeptTree(List<SysDeptResp> depts);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDeptResp> depts);

    /**
     * 根据角色ID查询部门树信息
     * 
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDeptResp selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     * 
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     * 
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     * 
     * @param dept 部门信息
     * @return 结果
     */
    public boolean checkDeptNameUnique(SysDeptReq sysDeptReq);

    /**
     * 校验部门是否有数据权限
     * 
     * @param deptId 部门id
     */
    public void checkDeptDataScope(Long deptId);

    /**
     * 新增保存部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDeptReq sysDeptReq);

    /**
     * 修改保存部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDeptReq sysDeptReq);

    /**
     * 删除部门管理信息
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * @Description: 根据部门ID查询本部门及下级部门
     * @author: tr
     * @Date: 2024/3/20 10:40
     * @param: [deptId]
     * @returnValue: java.util.List<com.platform.system.api.domain.SysDept>
     */
    List<SysDept> selectDeptAndChildDept(Long deptId);
}
