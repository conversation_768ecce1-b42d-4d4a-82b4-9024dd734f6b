package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.api.domain.response.SysOrgExtendResp;
import com.platform.system.convert.SysOrgExtendConvert;
import com.platform.system.domain.SysOrgExtend;
import com.platform.system.mapper.SysOrgExtendMapper;
import com.platform.system.service.SysOrgExtendService;
import org.springframework.stereotype.Service;

/**
 * @Description: 组织机构扩展信息Service层
 * @author: tr
 * @date: 2024年05月15日 11:28
 */
@Service
public class SysOrgExtendServiceImpl extends ServiceImpl<SysOrgExtendMapper, SysOrgExtend> implements SysOrgExtendService {

    @Override
    public SysOrgExtendResp getOneById(Long orgId) {
        LambdaQueryWrapper<SysOrgExtend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrgExtend::getId,orgId);
        SysOrgExtend sysOrgExtend = getOne(queryWrapper);
        SysOrgExtendResp sysOrgExtendResp = SysOrgExtendConvert.INSTANCE.doToResp(sysOrgExtend);
        return sysOrgExtendResp;
    }
}
