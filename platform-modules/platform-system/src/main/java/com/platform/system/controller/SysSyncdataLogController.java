package com.platform.system.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.domain.request.SysSyncdataLogReq;
import com.platform.system.api.domain.response.SysSyncdataLogResp;
import com.platform.system.service.SysSyncdataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 同步数据日志控制器
 * @author: tr
 * @date: 2025年03月26日 20:13
 */
@RestController
@RequestMapping("/syncdata/log")
public class SysSyncdataLogController {

    @Autowired
    private SysSyncdataLogService sysSyncdataLogService;

    @GetMapping("listStayByBatchNumber")
    public ResponseResult<List<SysSyncdataLogResp>> listStayByBatchNumber(String batchNumber) {
        List<SysSyncdataLogResp> list = sysSyncdataLogService.listStayByBatchNumber(batchNumber);
        return ResponseResult.ok(list);
    }

    /**
     * @Description: 根据批次号修改同步状态
     * @author: tr
     * @Date: 2025/3/26 20:25
     * @param: [sysSyndataLogReq]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult
     */
    @PostMapping("updateSyncStatusByBatchNumber")
    public ResponseResult updateSyncStatusByBatchNumber(@RequestBody SysSyncdataLogReq sysSyncdataLogReq) {
        sysSyncdataLogService.updateSyncStatusByBatchNumber(sysSyncdataLogReq);
        return ResponseResult.ok();
    }
}
