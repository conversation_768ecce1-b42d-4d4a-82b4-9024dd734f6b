package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @Description: 设置密码的入参
 * @author: tr
 * @date: 2024年07月12日 10:08
 */
@Data
@ToString
public class SetupPwdReq {

    /** 手机号码 **/
    @ApiModelProperty("手机号码")
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    private String phonenumber;

    /** 类型（1-注册验证码，2-登录验证码，3-设置密码的验证码） **/
    @ApiModelProperty("类型，默认传3")
    private String type;

    /** 唯一uuid **/
    @ApiModelProperty("唯一uuid")
    private String uuid;

    /** 验证码值 **/
    @ApiModelProperty("验证码值")
    private String validateCode;

    /** 新密码 **/
    @ApiModelProperty("新密码")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>]).{10,20}$", message = "密码为10-20位，数字、字符、大写字母和小写字母四者的组合")
    private String newPassword;

    /** 再次输入新密码 **/
    @ApiModelProperty("再次输入新密码")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>]).{10,20}$", message = "密码为10-20位，数字、字符、大写字母和小写字母四者的组合")
    private String againNewPassword;
}
