package com.platform.system.service.impl;

import com.common.middleware.cache.CacheClient;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.google.common.collect.Maps;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.domain.SysAppKeyPair;
import com.platform.system.mapper.SysAppKeyPairMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;

@Service
public class SysAppKeyPairService implements com.platform.system.service.SysAppKeyPairService {

    @Autowired
    private SysAppKeyPairMapper sysAppKeyPairMapper;

    @Autowired
    private CacheClient redisService;

    @PostConstruct
    public void init(){
        loadingAppKeyPairCache();
    }

    @Override
    public List<SysAppKeyPair> selectAllEnabled() {
        return sysAppKeyPairMapper.selectAllEnabled();
    }

    @Override
    public SysAppKeyPair selectByAppKey(String appKey) {
        return sysAppKeyPairMapper.selectByAppKey(appKey);
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingAppKeyPairCache() {
        HashMap<String, String> cacheMap = Maps.newHashMap();
        List<SysAppKeyPair> appKeyPairList = sysAppKeyPairMapper.selectAllEnabled();
        for (SysAppKeyPair appKeyPair : appKeyPairList){

            if(StringUtils.isNotEmpty(appKeyPair.getAppKey()) && StringUtils.isNotEmpty(appKeyPair.getAppSecret()) ){
                cacheMap.put(appKeyPair.getAppKey(),appKeyPair.getAppSecret());
            }

        }
        redisService.setCacheMap(CacheConstants.APP_KEY_PAIR_MAP,cacheMap);
    }

    @Override
    public int refresh() {
        redisService.deleteObject(CacheConstants.APP_KEY_PAIR_MAP);
        loadingAppKeyPairCache();
        return 0;
    }
}
