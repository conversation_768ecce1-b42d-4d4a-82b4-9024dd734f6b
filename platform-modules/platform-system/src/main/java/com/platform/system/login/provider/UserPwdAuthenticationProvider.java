package com.platform.system.login.provider;

import com.platform.system.login.config.code.UserPwdAuthenticationToken;
import com.platform.system.login.domain.model.SysUserDetails;
import com.platform.system.login.domain.req.LoginReq;
import com.platform.system.login.service.SysPasswordService;
import com.platform.system.login.service.impl.UserDetailServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * @Description: 用户密码认证验证提供者
 * @author: tr
 * @date: 2024年08月26日 15:14
 */
@Component
public class UserPwdAuthenticationProvider implements AuthenticationProvider {

    private UserDetailServiceImpl userDetailService;

    private SysPasswordService sysPasswordService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        LoginReq loginReq = (LoginReq)authentication.getPrincipal();

        SysUserDetails sysUserDetails = userDetailService.loadUserByUsername(loginReq.getUsername());

        sysPasswordService.validate(loginReq.getUsername(), sysUserDetails.getPassword(), loginReq.getPassword());

        //密码清空后返回token对象并放入缓存中，以便其他地方使用
        sysUserDetails.setPassword(null);
        UserPwdAuthenticationToken userPwdAuthenticationToken = new UserPwdAuthenticationToken(sysUserDetails, sysUserDetails.getAuthorities());

        return userPwdAuthenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UserPwdAuthenticationToken.class);
    }

    public void setUserDetailsService(UserDetailServiceImpl userDetailsService, SysPasswordService sysPasswordService) {
        this.userDetailService = userDetailsService;
        this.sysPasswordService = sysPasswordService;
    }
}
