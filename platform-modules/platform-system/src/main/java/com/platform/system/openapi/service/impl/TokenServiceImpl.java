package com.platform.system.openapi.service.impl;

import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.model.LoginUser;
import com.platform.system.convert.SysUserConvert;
import com.platform.system.openapi.domain.response.UserResp;
import com.platform.system.openapi.service.TokenService;
import org.springframework.stereotype.Service;

/**
 * @Description: Token的Service服务层
 * @author: tr
 * @date: 2024年03月22日 16:47
 */
@Service
public class TokenServiceImpl implements TokenService {

    @Override
    public UserResp validateToken(String token) {
        LoginUser loginUser = TokenUtils.checkToken(token);
        UserResp userResp = SysUserConvert.INSTANCE.DTOToResp(loginUser.getSysUser());
        return userResp;
    }
}
