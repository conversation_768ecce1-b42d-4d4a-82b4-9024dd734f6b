package com.platform.system.openapi.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.common.middleware.cache.CacheClient;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.system.convert.SysOrgConvert;
import com.platform.system.domain.SysOrg;
import com.platform.system.openapi.domain.response.OrgResp;
import com.platform.system.openapi.service.OrgService;
import com.platform.system.service.SysOrgService;
import com.platform.system.utils.EncrypDecrypUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description: 组织机构OpenApi实现层
 * @author: tr
 * @date: 2024年04月09日 16:35
 */
@Service
public class OrgServiceImpl implements OrgService {

    @Autowired
    private SysOrgService sysOrgService;

    @Autowired
    private CacheClient redisService;

    @Override
    public String listAll(String updateDate, String code) {
        //查询组织机构信息
        LambdaQueryWrapper<SysOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrg::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.ge(StrUtil.isNotBlank(updateDate), SysOrg::getUpdateTime, updateDate);
        List<SysOrg> sysOrgList = sysOrgService.list(queryWrapper);
        List<OrgResp> orgRespList = SysOrgConvert.INSTANCE.doListToRespListOpenApi(sysOrgList);

        //根据应用编码从缓存中获取key用于加密
        Map<String, String> codeKeyMap = redisService.getCacheMap(CacheConstants.APP_CODE_PUBLIC_KEY_MAP);
        String key = codeKeyMap.get(code);
        String content = EncrypDecrypUtils.encrypt(
                JSONUtil.toJsonStr(orgRespList), key);

        return content;
    }
}
