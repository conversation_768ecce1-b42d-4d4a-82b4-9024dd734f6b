package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.domain.request.SysRoleGroupPageReq;
import com.platform.system.domain.request.SysRoleGroupReq;
import com.platform.system.domain.request.SysRoleGroupRoleReq;
import com.platform.system.domain.response.SysRoleGroupResp;
import com.platform.system.service.SysRoleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 角色组前端控制器
 * @author: tr
 * @date: 2024年03月01日 10:48
 */
@Api(tags = "角色组前端控制器")
@RestController
@RequestMapping("/roleGroup")
public class SysRoleGroupController extends BaseController {

    @Autowired
    private SysRoleGroupService sysRoleGroupService;

    @RequiresPermissions("system:roleGroup:page")
    @ApiOperation(value = "分页查询角色组信息",  notes = "角色组")
    @GetMapping("page")
    public ResponseResult<PageResult<SysRoleGroupResp>> page(SysRoleGroupPageReq sysRoleGroupPageReq){
        Page<SysRoleGroupResp> page = PageUtils.startPage(sysRoleGroupPageReq.getPageNum(), sysRoleGroupPageReq.getPageSize());
        List<SysRoleGroupResp> list = sysRoleGroupService.list(sysRoleGroupPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @RequiresPermissions("system:roleGroup:list")
    @ApiOperation(value = "查询角色组信息",  notes = "角色组")
    @GetMapping("list")
    public ResponseResult<List<SysRoleGroupResp>> list(SysRoleGroupPageReq sysRoleGroupPageReq){
        List<SysRoleGroupResp> list = sysRoleGroupService.list(sysRoleGroupPageReq);
        return ResponseResult.ok(list);
    }

    @RequiresPermissions("system:roleGroup:getById")
    @ApiOperation(value = "查询角色组单个信息",  notes = "角色组")
    @GetMapping("getById")
    public ResponseResult<SysRoleGroupResp> getById(Long id){
        SysRoleGroupResp sysRoleGroupResp = sysRoleGroupService.getById(id);
        return ResponseResult.ok(sysRoleGroupResp);
    }

    @RequiresPermissions("system:roleGroup:save")
    @Log(title = "角色组管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增角色组",  notes = "角色组")
    @PostMapping("save")
    public ResponseResult save(@Validated @RequestBody SysRoleGroupReq sysRoleGroupReq){
        sysRoleGroupService.save(sysRoleGroupReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:roleGroup:update")
    @Log(title = "角色组管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改角色组",  notes = "角色组")
    @PostMapping("update")
    public ResponseResult update(@Validated @RequestBody SysRoleGroupReq sysRoleGroupReq){
        sysRoleGroupService.update(sysRoleGroupReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:roleGroup:delete")
    @Log(title = "角色组管理", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除角色组",  notes = "角色组")
    @DeleteMapping("delete")
    public ResponseResult delete(@RequestParam Long id){
        sysRoleGroupService.delete(id);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:roleGroup:saveRole")
    @Log(title = "角色组管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "角色组新增角色",  notes = "角色组")
    @PostMapping("saveRole")
    public ResponseResult saveRole(@RequestBody SysRoleGroupRoleReq sysRoleGroupRoleReq){
        sysRoleGroupService.saveRole(sysRoleGroupRoleReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:roleGroup:listRoleByRoleGroupId")
    @ApiOperation(value = "根据角色组ID查询角色信息",  notes = "角色组")
    @GetMapping("listRoleByRoleGroupId")
    public ResponseResult<List<String>> listRoleByRoleGroupId(Long id){
        return ResponseResult.ok(sysRoleGroupService.listRoleByRoleGroupId(id));
    }

}
