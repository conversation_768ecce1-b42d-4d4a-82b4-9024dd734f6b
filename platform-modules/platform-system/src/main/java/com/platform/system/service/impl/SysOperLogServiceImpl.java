package com.platform.system.service.impl;

import java.util.List;
import java.util.Map;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.common.starter.toolbox.utils.DateUtils;
import com.platform.system.api.domain.request.SysOperLogReq;
import com.platform.system.convert.SysOperLogConvert;
import com.platform.system.domain.request.SysOperLogPageReq;
import com.platform.system.domain.response.SysOperLogResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.platform.system.api.domain.SysOperLog;
import com.platform.system.mapper.SysOperLogMapper;
import com.platform.system.service.SysOperLogService;

/**
 * 操作日志 服务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLog>
        implements SysOperLogService
{
    @Autowired
    private SysOperLogMapper operLogMapper;

    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     * @return 结果
     */
    @Override
    public int insertOperlog(SysOperLogReq sysOperLogReq)
    {
        SysOperLog operLog = SysOperLogConvert.INSTANCE.reqToDO(sysOperLogReq);
        operLog.setOperTime(DateUtils.getNowDate());
        save(operLog);
        return 1;
    }

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLogResp> selectOperLogList(SysOperLogPageReq sysOperLogPageReq)
    {
        LambdaQueryWrapper<SysOperLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(sysOperLogPageReq.getOperIp()), SysOperLog::getOperIp, sysOperLogPageReq.getOperIp());
        queryWrapper.like(StrUtil.isNotBlank(sysOperLogPageReq.getTitle()), SysOperLog::getTitle, sysOperLogPageReq.getTitle());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysOperLogPageReq.getBusinessType()), SysOperLog::getBusinessType, sysOperLogPageReq.getBusinessType());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysOperLogPageReq.getStatus()), SysOperLog::getStatus, sysOperLogPageReq.getStatus());
        queryWrapper.like(StrUtil.isNotBlank(sysOperLogPageReq.getOperName()), SysOperLog::getOperName, sysOperLogPageReq.getOperName());

        Map<String, Object> params = sysOperLogPageReq.getParams();
        String beginTime = MapUtil.getStr(params, "beginTime");
        String endTime = MapUtil.getStr(params, "endTime");
        queryWrapper.between(StrUtil.isNotBlank(beginTime)
                , SysOperLog::getOperTime, DateUtils.parseDate(beginTime + " 00:00:00")
                , DateUtils.parseDate(endTime + " 23:59:59"));
        queryWrapper.orderByDesc(SysOperLog::getOperTime);
        List<SysOperLog> list = list(queryWrapper);

        List<SysOperLogResp> sysOperLogRespList = SysOperLogConvert.INSTANCE.doListToRespList(list);

        return sysOperLogRespList;
    }

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds)
    {
        return operLogMapper.deleteOperLogByIds(operIds);
    }

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLog selectOperLogById(Long operId)
    {
        return operLogMapper.selectOperLogById(operId);
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog()
    {
        operLogMapper.cleanOperLog();
    }
}
