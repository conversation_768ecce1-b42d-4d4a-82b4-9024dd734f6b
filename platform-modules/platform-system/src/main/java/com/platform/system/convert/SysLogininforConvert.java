package com.platform.system.convert;

import com.platform.system.api.domain.SysLogininfor;
import com.platform.system.domain.response.SysLogininforResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 系统登录日志转换类
 * @author: tr
 * @date: 2024年07月01日 19:33
 */
@Mapper
public interface SysLogininforConvert {

    SysLogininforConvert INSTANCE = Mappers.getMapper(SysLogininforConvert.class);

    SysLogininforResp doToResp(SysLogininfor sysLogininfor);

    List<SysLogininforResp> doListToRespList(List<SysLogininfor> sysLogininforList);
}