package com.platform.system.login.filter;

import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.platform.system.login.config.single.SingleAuthenticationToken;
import com.platform.system.login.domain.req.SingleLoginReq;
import com.platform.system.service.SysLogininforService;
import com.platform.system.service.impl.SysLogininforServiceImpl;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * @Description: 单点登录认证登录过滤器
 * @author: tr
 * @date: 2025年02月14日 08:43
 */
public class SingleLoginFilter extends AbstractAuthenticationProcessingFilter {

    public SingleLoginFilter(AuthenticationManager authenticationManager) {
        super(new AntPathRequestMatcher("/login/single", "POST"), authenticationManager);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        //获取POST请求中body的入参信息并转换为LoginReq对象
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        BufferedReader reader = req.getReader();
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line).append('\n');
        }
        String body = stringBuilder.toString();
        SingleLoginReq singleLoginReq = JSONUtil.toBean(body, SingleLoginReq.class);


        SingleAuthenticationToken singleAuthenticationToken = new SingleAuthenticationToken(singleLoginReq);
        setDetails(req, singleAuthenticationToken);
        return this.getAuthenticationManager().authenticate(singleAuthenticationToken);
    }

    protected void setDetails(HttpServletRequest request, SingleAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }
}
