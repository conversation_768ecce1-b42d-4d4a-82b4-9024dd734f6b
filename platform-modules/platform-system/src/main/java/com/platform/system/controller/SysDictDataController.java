package com.platform.system.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.system.api.domain.request.SysDictDataPageReq;
import com.platform.system.api.domain.response.SysDictDataResp;
import com.platform.system.convert.SysDictDataConvert;
import com.platform.system.domain.request.SysDictDataReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.common.starter.toolbox.poi.ExcelUtil;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysDictData;
import com.platform.system.service.SysDictDataService;
import com.platform.system.service.SysDictTypeService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@Api(tags = "数据字典信息")
@RestController
@RequestMapping("/dict/data")
@Slf4j
public class SysDictDataController extends BaseController
{
    @Autowired
    private SysDictDataService dictDataService;
    
    @Autowired
    private SysDictTypeService dictTypeService;

    @ApiOperation(value = "获取字典列表",  notes = "数据字典信息")
    @RequiresPermissions("system:dict:list")
    @GetMapping("/list")
    public ResponseResult<PageResult<SysDictDataResp>> list(SysDictDataPageReq sysDictDataPageReq)
    {
        Page<SysDictDataResp> page = PageUtils.startPage();
        List<SysDictDataResp> list = dictDataService.selectDictDataList(sysDictDataPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @ApiOperation(value = "导出字典信息",  notes = "数据字典信息")
    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:dict:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictDataPageReq sysDictDataPageReq)
    {
        List<SysDictDataResp> list = dictDataService.selectDictDataList(sysDictDataPageReq);
        ExcelUtil<SysDictDataResp> util = new ExcelUtil<>(SysDictDataResp.class);
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */
    @ApiOperation(value = "查询字典数据详细",  notes = "数据字典信息")
    @RequiresPermissions("system:dict:query")
    @GetMapping("/dictCode")
    public ResponseResult<SysDictDataResp> getInfo(Long dictCode)
    {
        return ResponseResult.ok(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @ApiOperation(value = "根据字典类型查询字典数据信息",  notes = "数据字典信息")
    @GetMapping("/type/dictType")
    public ResponseResult dictType(String dictType)
    {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data))
        {
            data = new ArrayList<SysDictData>();
        }
        return ResponseResult.ok(data);
    }

    /**
     * 新增字典类型
     */
    @ApiOperation(value = "新增字典类型",  notes = "数据字典信息")
    @RequiresPermissions("system:dict:add")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult add(@Validated @RequestBody SysDictDataReq sysDictDataReq)
    {
        SysDictData dict = SysDictDataConvert.INSTANCE.reqToDO(sysDictDataReq);
        dict.setCreateBy(SecurityUtils.getUsername());
        return dictDataService.insertDictData(dict)>0?ResponseResult.ok():ResponseResult.fail("新增失败！");
    }

    /**
     * 修改保存字典类型
     */
    @ApiOperation(value = "修改保存字典类型",  notes = "数据字典信息")
    @RequiresPermissions("system:dict:edit")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public ResponseResult edit(@Validated @RequestBody SysDictDataReq sysDictDataReq)
    {
        SysDictData dict = SysDictDataConvert.INSTANCE.reqToDO(sysDictDataReq);
        dict.setUpdateBy(SecurityUtils.getUsername());
        return dictDataService.updateDictData(dict)>0?ResponseResult.ok():ResponseResult.fail("修改失败！");
    }

    /**
     * 删除字典类型
     */
    @ApiOperation(value = "删除字典类型",  notes = "数据字典信息")
    @RequiresPermissions("system:dict:remove")
    @Log(title = "字典数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ResponseResult remove(@RequestParam("dictCodes") Long[] dictCodes)
    {
        dictDataService.deleteDictDataByIds(dictCodes);
        return ResponseResult.ok();
    }

    @GetMapping(value = "/selectDictDataByDictTypeAndDictValue")
    public ResponseResult<SysDictDataResp> selectDictDataByDictTypeAndDictValue(@RequestParam("dictType") String dictType,
                                                                                @RequestParam("dictValue") String dictValue) {
        if (StringUtils.isBlank(dictType) || StringUtils.isBlank(dictValue)) {
            log.warn("selectDictDataByDictTypeAndDictValue param is empty, dictType is {}, dictValue is {}", dictType,
                    dictValue);
            return ResponseResult.fail("参数为空，请检查后重试！");
        }
        return ResponseResult.ok(dictTypeService.selectDictDataByDictTypeAndDictValue(dictType, dictValue));
    }

    @ApiOperation(value = "导入字典数据信息",  notes = "字典数据")
    @Log(title = "字典数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public ResponseResult<String> importData(MultipartFile file) throws IOException {
        ExcelUtil<SysDictDataReq> util = new ExcelUtil<>(SysDictDataReq.class);
        List<SysDictDataReq> list = util.importExcel(file.getInputStream());
        String msg = dictDataService.batchSaveDictData(list);
        return ResponseResult.ok(msg);
    }
}
