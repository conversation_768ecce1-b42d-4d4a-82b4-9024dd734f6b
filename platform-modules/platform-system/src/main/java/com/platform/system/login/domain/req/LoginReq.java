package com.platform.system.login.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月26日 20:09
 */
@Data
public class LoginReq {

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;

    /** 验证码 **/
    @ApiModelProperty(value = "验证码")
    private String code;

    /** 唯一的uuid **/
    @ApiModelProperty(value = "唯一的uuid")
    private String uuid;

    /** 用户类型 **/
    @ApiModelProperty(value = "用户类型")
    private String userType;
}
