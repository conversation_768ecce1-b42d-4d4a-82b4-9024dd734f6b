package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 菜单权限表 sys_menu
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysMenuReq
{
    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    @ApiModelProperty(value = "菜单ID")
    private Long menuId;

    /** 应用ID */
    @ApiModelProperty(value = "菜单ID")
    private Long appId;

    /**
     * 类型（1-PC端，2-APP端）
     */
    @ApiModelProperty(value = "应用ID")
    private String type;

    /** 菜单名称 */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    /** 父菜单名称 */
    @ApiModelProperty(value = "父菜单名称")
    private String parentName;

    /** 父菜单ID */
    @ApiModelProperty(value = "父菜单ID")
    private Long parentId;

    /** 显示顺序 */
    @NotNull(message = "显示顺序不能为空")
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    /** 路由地址 */
    @Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
    @ApiModelProperty(value = "路由地址")
    private String path;

    /** 组件路径 */
    @Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
    @ApiModelProperty(value = "组件路径")
    private String component;

    /** 路由参数 */
    @Size(min = 0, max = 255, message = "路由参数长度不能超过255个字符")
    @ApiModelProperty(value = "路由参数")
    private String query;

    /** 是否为外链（0是 1否） */
    @ApiModelProperty(value = "是否为外链（0是 1否）")
    private String isFrame;

    /** 是否缓存（0缓存 1不缓存） */
    @ApiModelProperty(value = "是否缓存（0缓存 1不缓存）")
    private String isCache;

    /** 类型（M目录 C菜单 F按钮） */
    @NotBlank(message = "菜单类型不能为空")
    @ApiModelProperty(value = "类型（M目录 C菜单 F按钮）")
    private String menuType;

    /** 显示状态（0显示 1隐藏） */
    @ApiModelProperty(value = "显示状态（0显示 1隐藏）")
    private String visible;
    
    /** 菜单状态（0正常 1停用） */
    @ApiModelProperty(value = "菜单状态（0正常 1停用）")
    private String status;

    /** 权限字符串 */
    @Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
    @ApiModelProperty(value = "权限字符串")
    private String perms;

    /** 菜单图标 */
    @Size(min = 0, max = 100, message = "菜单图标长度不能超过100个字符")
    @ApiModelProperty(value = "菜单图标")
    private String icon;

}
