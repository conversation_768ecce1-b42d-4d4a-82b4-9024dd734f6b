package com.platform.system.utils;

import com.ctdi.base.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * @Description:
 * @author: tr
 * @date: 2024年03月22日 17:04
 */
@Slf4j
public class RSAUtils {

    /**
     * AES_ALGORITHM常量定义了对称加密算法AES的名称
     */
    private static final String AES_ALGORITHM = "AES";
    /**
     * AES_KEY_SIZE常量定义了AES密钥的长度
     */
    private static final int AES_KEY_SIZE = 256;

    /**
     * @Description: 公钥加密
     * @author: tr
     * @Date: 2024/3/26 10:44
     * @param content 待加密的文本内容
     * @param publicKeyStr 公钥字符串
     * @returnValue: java.lang.String
     */
    public static String encryptPublicKey(String content, String publicKeyStr) {
        try{
            // 生成随机AES密钥以进行对称加密
            KeyGenerator keyGen = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGen.init(AES_KEY_SIZE);
            SecretKey secretKey = keyGen.generateKey();

            // 使用AES加密明文
            Cipher aesCipher = Cipher.getInstance(AES_ALGORITHM);
            aesCipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = aesCipher.doFinal(content.getBytes(StandardCharsets.UTF_8));

            //字符串publicKey的PublicKey对象
            PublicKey publicKey = getPublicKeyFromString(publicKeyStr);

            // 使用RSA加密AES密钥
            Cipher rsaCipher = Cipher.getInstance(KeyUtils.RSA_ALGORITHM);
            rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedAESKey = rsaCipher.doFinal(secretKey.getEncoded());

            // 组合AES密钥和加密的消息
            String encryptedAESKeyStr = Base64.getEncoder().encodeToString(encryptedAESKey);
            String encryptedMessageStr = Base64.getEncoder().encodeToString(encryptedBytes);
            String encryptedStr = encryptedAESKeyStr + ":" + encryptedMessageStr;
            log.info("加密后字符串：{}", encryptedStr);
            //base64编码
            String base64Str = encodedBase64(encryptedStr);
            log.info("base64编码后字符串：{}", base64Str);
            return base64Str;
        }catch (Exception e){
            throw new ServiceException("RSA加密错误");
        }
    }

    /**
     * @Description: 私钥解密
     * @author: tr
     * @Date: 2024/3/26 10:44
     * @param content 加密的文本内容
     * @param privateKeyStr 私钥
     * @returnValue: java.lang.String
     */
    public static String decryptPrivateKey(String content, String privateKeyStr) {
        try{
            //base64解码
            content = new String(decodeBase64(content), StandardCharsets.UTF_8);
            // 将输入分成加密的AES密钥和加密的消息两部分
            String[] parts = content.split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("无效的输入格式");
            }

            String encryptedAESKeyStr = parts[0];
            String encryptedMessageStr = parts[1];

            PrivateKey privateKey = getPrivateKeyFromString(privateKeyStr);

            // 使用RSA私钥解密AES密钥
            Cipher rsaCipher = Cipher.getInstance(KeyUtils.RSA_ALGORITHM);
            rsaCipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] encryptedAESKey = decodeBase64(encryptedAESKeyStr);
            byte[] decryptedAESKey = rsaCipher.doFinal(encryptedAESKey);

            // 使用AES解密消息
            SecretKey secretKey = new SecretKeySpec(decryptedAESKey, 0, decryptedAESKey.length, AES_ALGORITHM);
            Cipher aesCipher = Cipher.getInstance(AES_ALGORITHM);
            aesCipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = aesCipher.doFinal(decodeBase64(encryptedMessageStr));

            String result = new String(decryptedBytes, StandardCharsets.UTF_8);
            log.info("解密后字符串：{}", result);
            return result;
        }catch (Exception e){
            throw new ServiceException("RSA解密错误");
        }
    }

    /**
     * @Description: base64编码
     * @author: tr
     * @Date: 2024/3/26 11:27
     * @param content 待编码的文本内容
     * @returnValue: java.lang.String
     */
    public static String encodedBase64(String content){
        return Base64.getEncoder().encodeToString(content.getBytes());
    }

    /**
     * @Description: base64解码
     * @author: tr
     * @Date: 2024/3/26 11:28
     * @param encoded 编码后的文本内容
     * @returnValue: byte[]
     */
    public static byte[] decodeBase64(String encoded) {
        Base64.Decoder decoder = Base64.getDecoder();
        return decoder.decode(encoded);
    }

    /**
     * @Description: 从字符串格式的公钥创建 PublicKey 对象
     * @author: tr
     * @Date: 2024/3/26 17:41
     * @param publicKeyString 公钥字符串
     * @returnValue: java.security.PublicKey
     */
    private static PublicKey getPublicKeyFromString(String publicKeyString) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyString);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KeyUtils.RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * @Description: 从字符串格式的私钥创建 PrivateKey 对象
     * @author: tr
     * @Date: 2024/3/26 17:42
     * @param privateKeyString 私钥字符串
     * @returnValue: java.security.PrivateKey
     */
    private static PrivateKey getPrivateKeyFromString(String privateKeyString) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyString);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KeyUtils.RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

}
