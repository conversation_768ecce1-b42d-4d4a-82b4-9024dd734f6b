package com.platform.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.UserConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.system.api.domain.response.SysApiResp;
import com.platform.system.api.model.SysApiDTO;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.convert.SysApiConvert;
import com.platform.system.domain.SysApi;
import com.platform.system.domain.SysRoleApi;
import com.platform.system.domain.request.SysApiQueryReq;
import com.platform.system.domain.request.SysApiReq;
import com.platform.system.domain.request.SysAppPageReq;
import com.platform.system.domain.response.SysAppResp;
import com.platform.system.mapper.SysApiMapper;
import com.platform.system.service.SysApiService;
import com.platform.system.service.SysAppService;
import com.platform.system.service.SysRoleApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: API权限配置Service类
 * @author: tr
 * @date: 2024年02月29日 16:17
 */
@Slf4j
@Service
public class SysApiServiceImpl extends ServiceImpl<SysApiMapper, SysApi> implements SysApiService {

    @Autowired
    private SysRoleApiService roleApiService;

    @Autowired
    private CacheClient redisService;

    @Autowired
    private SysAppService sysAppService;

    @PostConstruct
    public void init()
    {
        freshCache();
    }

    @Override
    public List<SysApiResp> list(SysApiQueryReq sysApiQueryReq) {
        LambdaQueryWrapper<SysApi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(sysApiQueryReq.getAppId()), SysApi::getAppId, sysApiQueryReq.getAppId());
        queryWrapper.like(StrUtil.isNotBlank(sysApiQueryReq.getName()), SysApi::getName, sysApiQueryReq.getName());
        queryWrapper.like(StrUtil.isNotBlank(sysApiQueryReq.getPerms()), SysApi::getName, sysApiQueryReq.getPerms());
        queryWrapper.eq(SysApi::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        List<SysApi> sysApiList = list(queryWrapper);
        List<SysApiResp> list = SysApiConvert.INSTANCE.doListToRespList(sysApiList);
        list.forEach(m ->{
            if (ObjectUtil.equals(m.getParentId(), BusinessConstants.PARENT_ID)){
                m.setParentId(m.getAppId());
            }
        });
        //查询应用信息
        SysAppPageReq sysAppPageReq = new SysAppPageReq();
        sysAppPageReq.setType("1");
        List<SysAppResp> sysAppRespList = sysAppService.list(sysAppPageReq);
        sysAppRespList.forEach(a ->{
            SysApiResp sysApiResp = new SysApiResp();
            sysApiResp.setId(a.getId());
            sysApiResp.setParentId(BusinessConstants.PARENT_ID);
            sysApiResp.setName(a.getName());
            sysApiResp.setOrderNum(a.getOrderNum());
            sysApiResp.setAppId(a.getId());
            sysApiResp.setType(UserConstants.TYPE_APP);
            sysApiResp.setCreateTime(a.getCreateTime());
            list.add(sysApiResp);
        });
        return list;
    }

    @Override
    public List<SysApiResp> listByRoleId(Long roleId) {
        //查询所有API信息
        List<SysApiResp> sysApiRespList = list(new SysApiQueryReq());
        //查询已授权角色的API信息
        List<SysRoleApi> sysRoleApiList = roleApiService.listByRoleId(roleId);
        List<Long> apiIdList = sysRoleApiList.stream().map(s -> s.getApiId()).collect(Collectors.toList());
        sysApiRespList.forEach(a -> {
            a.setIsAuthorization(0);
            if (apiIdList.contains(a.getId())){
                //api已赋权，则进行标记
                a.setIsAuthorization(1);
            }
        });
        return sysApiRespList;
    }

    @Override
    public SysApiResp getById(Long id) {
        LambdaQueryWrapper<SysApi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApi::getId, id);
        queryWrapper.eq(SysApi::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysApi sysApi = getOne(queryWrapper);
        SysApiResp sysApiResp = SysApiConvert.INSTANCE.doToResp(sysApi);
        return sysApiResp;
    }

    @Override
    public void save(SysApiReq sysApiReq) {
        sysApiReq.setId(null);
        checkExist(sysApiReq);
        Long parentId = sysApiReq.getParentId();
        String parentIdPath = sysApiReq.getParentIdPath();

        if (ObjectUtil.isEmpty(parentId)){
            sysApiReq.setParentId(BusinessConstants.PARENT_ID);
        }
        if (StrUtil.isBlank(parentIdPath) || StrUtil.equals(parentIdPath, BusinessConstants.PARENT_ID.toString())){
            sysApiReq.setParentIdPath(BusinessConstants.SPRIT + BusinessConstants.PARENT_ID + BusinessConstants.SPRIT);
        }else{
            sysApiReq.setParentIdPath(sysApiReq.getParentIdPath() + sysApiReq.getParentId() + BusinessConstants.SPRIT);
        }

        SysApi sysApi = SysApiConvert.INSTANCE.reqToDO(sysApiReq);
        save(sysApi);
        freshCache();
    }

    @Override
    public void update(SysApiReq sysApiReq) {
        checkExist(sysApiReq);
        SysApi sysApi = SysApiConvert.INSTANCE.reqToDO(sysApiReq);
        updateById(sysApi);
        freshCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        LambdaQueryWrapper<SysApi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApi::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.eq(SysApi::getParentId, id);
        queryWrapper.last("limit 1");
        SysApi sysApiChild = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysApiChild)){
            throw new ServiceException("存在子目录或子权限，不能删除");
        }

        //删除API时，同时删除API与角色的关系
        LambdaUpdateWrapper<SysRoleApi> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysRoleApi::getApiId, id);
        roleApiService.remove(updateWrapper);

        SysApi sysApi = new SysApi();
        sysApi.setId(id);
        sysApi.setDelFlag(DelFlagEnum.DELETED.getCode());
        updateById(sysApi);
        freshCache();
    }

    @Override
    public List<SysApiDTO> selectApiPermsByRoleId(List<Long> roleIdList) {
        List<SysApiDTO> list = baseMapper.selectApiPermsByRoleId(roleIdList);

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRoleAndApi(SysApiReq sysApiReq) {
        SysRoleApi sysRoleApi = new SysRoleApi();
        sysRoleApi.setId(IdWorker.getId(sysRoleApi));
        sysRoleApi.setRoleId(sysApiReq.getRoleId());
        sysRoleApi.setApiId(sysApiReq.getId());
        roleApiService.save(sysRoleApi);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRoleAndApiBatch(SysApiReq sysApiReq) {
        Long id = sysApiReq.getId();
        Long roleId = sysApiReq.getRoleId();

        //查询API下是否有子API
        List<Long> apiIdList = findAllChildApiIds(id);

        //先移除角色下的API，然后在新增
        removeRoleAndApi(sysApiReq);

        apiIdList.add(id);
        List<SysRoleApi> sysRoleApiList = new ArrayList<>();
        apiIdList.forEach(a -> {
            SysRoleApi sysRoleApi = new SysRoleApi();
            sysRoleApi.setId(IdWorker.getId(sysRoleApi));
            sysRoleApi.setRoleId(roleId);
            sysRoleApi.setApiId(a);
            sysRoleApiList.add(sysRoleApi);
        });
        roleApiService.saveBatch(sysRoleApiList);
    }

    @Override
    public void removeRoleAndApi(SysApiReq sysApiReq) {
        Long id = sysApiReq.getId();
        Long roleId = sysApiReq.getRoleId();
        //查询API下是否有子API
        List<Long> apiIdList = findAllChildApiIds(id);
        apiIdList.add(id);
        removeRoleAndApi(roleId, apiIdList);
    }

    /**
     * @Description: 根据父级ID查询子级的API集合
     * @author: tr
     * @Date: 2024/3/20 20:13
     * @param: [id]
     * @returnValue: java.util.List<java.lang.Long>
     */
    private List<Long> findAllChildApiIds(Long id){
        //查下ID是否为应用ID，如果是应用ID，则对应用下的所有API权限授权
        SysAppResp sysAppResp = sysAppService.getById(id);
        LambdaQueryWrapper<SysApi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApi::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        if (ObjectUtil.isNotEmpty(sysAppResp)){
            queryWrapper.in(SysApi::getAppId, id);
        }else{
            queryWrapper.like(SysApi::getParentIdPath, BusinessConstants.SPRIT + id + BusinessConstants.SPRIT);
        }
        List<SysApi> sysApiList = list(queryWrapper);
        List<Long> apiIdList = sysApiList.stream().map(s->s.getId()).collect(Collectors.toList());
        return apiIdList;
    }

    /**
     * @Description: 检查名称或权限字符是否存在
     * @author: tr
     * @Date: 2024/3/1 9:25
     * @param: [sysApiReq]
     * @returnValue: boolean
     */
    private void checkExist(SysApiReq sysApiReq){
        String name = sysApiReq.getName();
        String perms = sysApiReq.getPerms();
        Long id = sysApiReq.getId();
        LambdaQueryWrapper<SysApi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(sysApiReq.getAppId()), SysApi::getAppId, sysApiReq.getAppId());
        queryWrapper.eq(SysApi::getParentId, sysApiReq.getParentId());
        queryWrapper.eq(SysApi::getName, name);
        queryWrapper.eq(SysApi::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.ne(ObjectUtil.isNotNull(id), SysApi::getId, id);
        queryWrapper.last("limit 1");
        SysApi sysApi = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysApi)){
            throw new ServiceException("API名称已存在!");
        }

        queryWrapper.clear();
        queryWrapper.eq(ObjectUtil.isNotNull(sysApiReq.getAppId()), SysApi::getAppId, sysApiReq.getAppId());
        queryWrapper.eq(SysApi::getPerms, perms);
        queryWrapper.eq(SysApi::getRequestMethod, sysApiReq.getRequestMethod());
        queryWrapper.eq(SysApi::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.ne(ObjectUtil.isNotNull(id), SysApi::getId, id);
        queryWrapper.last("limit 1");
        sysApi = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysApi)){
            throw new ServiceException("API权限字符或路径已存在!");
        }
    }

    /**
     * @Description: 刷新缓存
     * @author: tr
     * @Date: 2024/3/6 15:01
     * @param: []
     * @returnValue: void
     */
    private void freshCache() {
        List<SysApiResp> list = list(new SysApiQueryReq());
        boolean flag = redisService.hasKey(CacheConstants.SYSTEM_API);
        if (flag){
            redisService.deleteObject(CacheConstants.SYSTEM_API);
        }
        if (ObjectUtil.isNotEmpty(list)){
            redisService.setCacheObject(CacheConstants.SYSTEM_API, JSONUtil.toJsonStr(list));
        }
    }

    /**
     * @Description: 批量移除角色下的API权限
     * @author: tr
     * @Date: 2025/7/16 19:19
     * @param: roleId 角色ID
     * @param: apiIdList API集合
     * @returnValue: void
     */
    private void removeRoleAndApi(Long roleId, List<Long> apiIdList) {
        LambdaUpdateWrapper<SysRoleApi> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysRoleApi::getRoleId, roleId);
        updateWrapper.in(SysRoleApi::getApiId, apiIdList);
        roleApiService.remove(updateWrapper);
    }
}
