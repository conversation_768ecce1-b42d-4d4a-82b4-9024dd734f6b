package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 系统登录日志分页查询请求入参
 * @author: tr
 * @date: 2024年07月01日 19:25
 */
@Data
@ToString
public class SysLogininforPageReq extends PageReq {

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 状态 0成功 1失败 */
    @ApiModelProperty(value = "状态 0成功 1失败")
    private String status;

    /** 地址 */
    @ApiModelProperty(value = "地址")
    private String ipaddr;

}
