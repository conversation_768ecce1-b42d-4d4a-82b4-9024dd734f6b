package com.platform.system.convert;

import com.platform.system.api.domain.response.SysCountryAddressResp;
import com.platform.system.domain.SysCountryAddress;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 全国地区编码转换类
 * @author: tr
 * @date: 2024年03月18日 16:26
 */
@Mapper
public interface SysCountryAddressConvert {

    SysCountryAddressConvert INSTANCE = Mappers.getMapper(SysCountryAddressConvert.class);

    SysCountryAddressResp doToResp(SysCountryAddress sysCountryAddress);

    List<SysCountryAddressResp> doListToRespList(List<SysCountryAddress> sysCountryAddressList);
}
