package com.platform.system.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ctdi.common.starter.toolbox.utils.DateUtils;
import com.platform.system.domain.response.SysMessageResp;
import com.platform.system.service.SysMessageService;
import com.platform.system.service.WebSocketService;
import com.platform.system.websocket.WebsocketSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentMap;

/**
 * @Description: WebSocket服务实现层
 * @author: tr
 * @date: 2024年04月17日 10:52
 */
@Slf4j
@Service
public class WebSocketServiceImpl implements WebSocketService {

    @Autowired
    private SysMessageService sysMessageService;

    @Override
    public void sendMessage(Long userId) {
        String userIdStr = StrUtil.toString(userId);
        if (WebsocketSet.getUserSessionMap().containsKey(userIdStr)) {
            List<String> sessionList = WebsocketSet.getUserSessionMap().get(userIdStr);
            sessionList.stream().forEach(sessionId -> {
                try {
                    if (WebsocketSet.getWebsocketMap().containsKey(sessionId)) {
                        List<SysMessageResp> list = sysMessageService.listLastLimit(userId);
                        String msg = JSON.toJSONStringWithDateFormat(list, DateUtils.YYYY_MM_DD_HH_MM_SS, SerializerFeature.WriteDateUseDateFormat);
                        WebsocketSet.getWebsocketMap().get(sessionId).getSession().getBasicRemote().sendText(msg);
                    }
                } catch (Exception e) {
                    log.error("WebSocket推送消息异常", e);
                }
            });
        }
    }
}
