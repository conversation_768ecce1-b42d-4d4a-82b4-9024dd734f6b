package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.domain.response.SysMenuResp;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.domain.response.SysAppResp;
import com.platform.system.domain.vo.RouterVo;
import com.platform.system.service.SysMenuService;
import com.platform.system.service.SysPermissionService;
import com.platform.system.service.SysUserService;
import com.platform.system.service.SysAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description:
 * @author: tr
 * @date: 2023年12月05日 10:25
 */
@Api(tags = "公共前端控制器")
@RestController
public class SysCommonController  extends BaseController {

    @Autowired
    private SysMenuService menuService;

    @Autowired
    private SysUserService userService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private SysAppService sysAppService;

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @ApiOperation(value = "获取路由信息",  notes = "菜单信息")
    @GetMapping("getRouters")
    public ResponseResult<List<RouterVo>> getRouters(String appCode)
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenuResp> menus = menuService.selectMenuTreeByUserId(userId, appCode);
        return ResponseResult.ok(menuService.buildMenus(menus));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @ApiOperation(value = "获取用户信息",  notes = "用户信息")
    @GetMapping("getInfo")
    public ResponseResult getInfo()
    {
        SysUserResp sysUserResp = userService.selectUserById(SecurityUtils.getUserId());
        SysUser user = new SysUser();
        user.setUserType(sysUserResp.getUserType());
        user.setUserId(sysUserResp.getUserId());
        user.setRoles(sysUserResp.getRoles());
        user.setLastLoginRole(sysUserResp.getLastLoginRole());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        Map<String, Object> result = new HashMap<>();
        result.put("user", sysUserResp);
        result.put("roles", roles);
        return ResponseResult.ok(result);
    }

    @ApiOperation(value = "获取用户的应用信息",  notes = "应用信息")
    @GetMapping("getAppInfo")
    public ResponseResult<List<SysAppResp>> getAppInfo(String type){
        List<SysAppResp> list = sysAppService.listUserApp(type);
        return ResponseResult.ok(list);
    }
}
