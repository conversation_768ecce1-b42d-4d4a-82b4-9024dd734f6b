package com.platform.system.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 切换机构和角色的入参
 * @author: tr
 * @date: 2024年06月12日 9:26
 */
@Data
@ToString
public class SwitchOrgAndRoleReq {

    /** 最后登陆的orgID **/
    @ApiModelProperty("最后登陆的orgID")
    private Long lastLoginOrgId;

    /** 最后登陆的角色 **/
    @ApiModelProperty("最后登陆的角色")
    private String lastLoginRole;
}
