package com.platform.system.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("API权限配置")
@Data
@ToString
public class SysApiQueryReq extends PageReq {

	private static final long serialVersionUID = 1L;

	/** 应用ID */
	@ApiModelProperty("应用ID")
	private Long appId;

	/**
	 * API名称
	 */
	@ApiModelProperty("API名称")
	private String name;

	/**
	 * API权限字符或路径
	 */
	@ApiModelProperty("API权限字符或路径")
	private String perms;

}
