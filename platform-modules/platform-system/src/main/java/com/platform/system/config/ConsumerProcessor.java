package com.platform.system.config;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * @Description: 消费者的通道配置
 * @author: tr
 * @date: 2024年01月30日 15:43
 */
@Component
public interface ConsumerProcessor {

    /** 与配置文件中的bindings对应，消息接收者的信息 **/
    String SEND_PARAMS_CONSUMER = "platform-send-params-input";

    @Input(ConsumerProcessor.SEND_PARAMS_CONSUMER)
    SubscribableChannel sendParamsInput();
}
