package com.platform.system.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.api.domain.request.SysEncRuleReq;
import com.platform.system.domain.request.SysConfigPageReq;
import com.platform.system.domain.response.SysConfigResp;
import com.platform.system.domain.vo.SysEncRule;
import com.platform.system.mapper.SysEncRuleMapper;
import com.platform.system.service.SysEncRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 加密规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SysEncRuleServiceImpl extends ServiceImpl<SysEncRuleMapper, SysEncRule> implements SysEncRuleService {

    private final SysEncRuleMapper sysEncRuleMapper;

    private final CacheClient redisService;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init()
    {
        resetConfigCache();
    }

    /**
     * 翻页
     *
     * @param entity
     * @param pageable
     * @return
     */
    @Override
    public ResponseResult<List<SysEncRule>> page(SysEncRule entity, Pageable pageable) {
        //当前页
        int current = pageable.getPageNumber();
        //每页个数
        int size = pageable.getPageSize();
        log.debug("page current : {} size : {}", current, size);
        IPage<SysEncRule> page = new Page<>(current, size);
        //设置条件
        LambdaQueryWrapper<SysEncRule> queryWrapper = new LambdaQueryWrapper<>();
         if(entity.getId()!=null) {
                queryWrapper.eq(SysEncRule::getId, entity.getId());
         }
         if(entity.getColumnName()!=null) {
                queryWrapper.eq(SysEncRule::getColumnName, entity.getColumnName());
         }
         if(entity.getEncType()!=null) {
                queryWrapper.eq(SysEncRule::getEncType, entity.getEncType());
         }
        IPage<SysEncRule> pageData = sysEncRuleMapper.selectPage(page, queryWrapper);
        //返回
        long count = pageData.getTotal();
        return ResponseResult.ok(pageData.getRecords());
    }

    /**
     * 列表查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<SysEncRule> findList(SysEncRule entity) {
        String cache = redisService.get(CacheConstants.SYS_ENC_RULE);
        if (StrUtil.isNotBlank(cache)){
            return JSONUtil.toList(new JSONArray(cache), SysEncRule.class);
        }

        LambdaQueryWrapper<SysEncRule> queryWrapper = new LambdaQueryWrapper<>();
         if(entity.getId()!=null) {
                queryWrapper.eq(SysEncRule::getId, entity.getId());
         }
         if(entity.getColumnName()!=null) {
                queryWrapper.eq(SysEncRule::getColumnName, entity.getColumnName());
         }
         if(entity.getEncType()!=null) {
                queryWrapper.eq(SysEncRule::getEncType, entity.getEncType());
         }
        return sysEncRuleMapper.selectList(queryWrapper);
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public SysEncRule findOne(Integer id) {
        return sysEncRuleMapper.selectById(id);
    }


    /**
     * 新增
     *
     * @param entity the entity to create
     * @return
     */
    @Override
    public SysEncRule create(SysEncRule entity) {
        save(entity);
        loadingConfigCache();
        return entity;
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    public int update(SysEncRule entity) {
        int result = sysEncRuleMapper.updateById(entity);
        loadingConfigCache();
        return result;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    public int delete(Integer id) {
        int result = sysEncRuleMapper.deleteById(id);
        loadingConfigCache();
        return result;
    }


    /**
     * 验证是否存在
     *
     * @param SysEncRuleId
     * @return
     */
    @Override
    public boolean existBySysEncRuleId(Integer SysEncRuleId) {

        if (SysEncRuleId != null) {
            LambdaQueryWrapper<SysEncRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysEncRule::getId, SysEncRuleId);
            List<SysEncRule> result = sysEncRuleMapper.selectList(queryWrapper);

            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    public Boolean insertBatch(List<SysEncRule> dataList) {
        return saveBatch(dataList);
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache()
    {
        SysEncRule sysEncRuleReq = new SysEncRule();
        List<SysEncRule> list = this.findList(sysEncRuleReq);
        redisService.setCacheObject(CacheConstants.SYS_ENC_RULE, JSONUtil.toJsonStr(list));
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache()
    {
        Collection<String> keys = redisService.keys(CacheConstants.SYS_ENC_RULE);
        redisService.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache()
    {
        clearConfigCache();
        loadingConfigCache();
    }


}
