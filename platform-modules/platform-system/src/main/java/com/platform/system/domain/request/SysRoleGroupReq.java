package com.platform.system.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @Description: 角色组新增或修改入参
 * @author: tr
 * @date: 2024年03月01日 10:17
 */
@ApiModel("角色组新增或修改入参")
@Data
@ToString
public class SysRoleGroupReq {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	private Long id;

	/**
	 * 角色组名称
	 */
	@NotBlank(message = "角色组名称不能为空")
	@Size(min = 1, max = 100, message = "角色组名称不能超过100个字符")
	@ApiModelProperty("角色组名称")
	private String name;

	/**
	 * 角色组编码
	 */
	@NotBlank(message = "角色组编码不能为空")
	@Size(min = 1, max = 100, message = "角色组编码不能超过100个字符")
	@ApiModelProperty("角色组编码")
	private String code;

	/**
	 * 组织机构类型
	 */
	private Integer orgType;

	/**
	 * 备注
	 */
	@Size(min = 0, max = 500, message = "角色组描述长度不能超过500个字符")
	@ApiModelProperty("备注")
	private String remark;

}
