package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

@Data
@ToString
@TableName("sys_app")
public class SysApp  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 应用名称
	 */
	private String name;

	/**
	 * 应用编码
	 */
	private String code;

	/**
	 * 图标地址
	 */
	private String icon;

	/**
	 * 类型（1-PC端，2-APP端）
	 */
	private String type;

	/**
	 * 跳转地址
	 */
	private String url;

	/**
	 * 显示顺序
	 */
	private Integer orderNum;

	/**
	 * 外链标识，0-否，1-是
	 */
	private Integer frameFlag;

	/**
	 * 公钥
	 */
	private String publicKey;

	/**
	 * 私钥
	 */
	private String privateKey;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态（0正常 1停用）
	 */
	private String status;

	/**
	 * 业务归属
	 */
	private String bizSense;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableField(
		value = "del_flag",
		fill = FieldFill.INSERT
	)
	private String delFlag;

}
