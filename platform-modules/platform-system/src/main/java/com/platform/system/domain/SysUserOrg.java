package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

@Data
@ToString
@TableName("sys_user_org")
public class SysUserOrg  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 机构ID
	 */
	private Long orgId;

	/**
	 * 机构编码
	 */
	private String orgCode;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableField(
		value = "del_flag",
		fill = FieldFill.INSERT
	)
	private String delFlag;

	@TableField(exist = false)
	private String remark;
}
