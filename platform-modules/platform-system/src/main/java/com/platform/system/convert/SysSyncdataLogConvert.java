package com.platform.system.convert;

import com.platform.system.api.domain.response.SysSyncdataLogResp;
import com.platform.system.domain.SysSyncdataLog;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 同步数据日志转换类
 * @author: tr
 * @date: 2025年03月26日 20:19
 */
@Mapper
public interface SysSyncdataLogConvert {

    SysSyncdataLogConvert INSTANCE = Mappers.getMapper(SysSyncdataLogConvert.class);

    List<SysSyncdataLogResp> doListToRespList(List<SysSyncdataLog> sysSyncdataLogList);
}
