package com.platform.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctdi.base.core.exception.ServiceException;
import com.platform.common.core.constant.SystemConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.datascope.annotation.DataPermission;
import com.platform.common.datascope.annotation.DataScope;
import com.platform.common.datascope.config.ThreadLocalKeyConfig;
import com.platform.system.convert.SysRoleGroupConvert;
import com.platform.system.domain.SysOrg;
import com.platform.system.domain.SysRoleGroup;
import com.platform.system.domain.SysRoleRoleGroup;
import com.platform.system.domain.request.SysRoleGroupPageReq;
import com.platform.system.domain.request.SysRoleGroupReq;
import com.platform.system.domain.request.SysRoleGroupRoleReq;
import com.platform.system.domain.response.SysRoleGroupResp;
import com.platform.system.mapper.SysRoleGroupMapper;
import com.platform.system.service.SysOrgService;
import com.platform.system.service.SysRoleGroupService;
import com.platform.system.service.SysRoleRoleGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 角色组Service实现类
 * @author: tr
 * @date: 2024年03月01日 10:44
 */
@Service
public class SysRoleGroupServiceImpl extends ServiceImpl<SysRoleGroupMapper, SysRoleGroup> implements SysRoleGroupService {

    @Autowired
    private SysRoleRoleGroupService sysRoleRoleGroupService;

    @Autowired
    private SysOrgService orgService;

    @Autowired
    private ThreadLocalKeyConfig threadLocalKeyConfig;

    @DataScope(permissionField = "id", dataType = SystemConstants.DATA_TYPE_ROLE_GROUP)
    @Override
    public List<SysRoleGroupResp> list(SysRoleGroupPageReq sysRoleGroupPageReq) {
        LambdaQueryWrapper<SysRoleGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(sysRoleGroupPageReq.getName()), SysRoleGroup::getName, sysRoleGroupPageReq.getName());
        queryWrapper.like(StrUtil.isNotBlank(sysRoleGroupPageReq.getCode()), SysRoleGroup::getCode, sysRoleGroupPageReq.getCode());
        queryWrapper.eq(ObjectUtil.isNotEmpty(sysRoleGroupPageReq.getOrgType()), SysRoleGroup::getOrgType, sysRoleGroupPageReq.getOrgType());
        queryWrapper.eq(SysRoleGroup::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.orderByDesc(SysRoleGroup::getCreateTime);
        List<SysRoleGroup> sysRoleGroupList = list(queryWrapper);
        List<SysRoleGroupResp> list = SysRoleGroupConvert.INSTANCE.doListToRespList(sysRoleGroupList);
        return list;
    }

    @Override
    public SysRoleGroupResp getById(Long id) {
        LambdaQueryWrapper<SysRoleGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRoleGroup::getId, id);
        queryWrapper.eq(SysRoleGroup::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        SysRoleGroup sysRoleGroup = getOne(queryWrapper);
        SysRoleGroupResp sysRoleGroupResp = SysRoleGroupConvert.INSTANCE.doToResp(sysRoleGroup);
        return sysRoleGroupResp;
    }

    @DataPermission(dataType = SystemConstants.DATA_TYPE_ROLE_GROUP, operType = SystemConstants.OPER_TYPE_INSERT)
    @Override
    public void save(SysRoleGroupReq sysRoleGroupReq) {
        sysRoleGroupReq.setId(null);
        checkExist(sysRoleGroupReq);
        SysRoleGroup sysRoleGroup = SysRoleGroupConvert.INSTANCE.reqToDO(sysRoleGroupReq);
        save(sysRoleGroup);

        threadLocalKeyConfig.set(sysRoleGroup.getId());
    }

    @Override
    public void update(SysRoleGroupReq sysRoleGroupReq) {
        checkExist(sysRoleGroupReq);
        SysRoleGroup sysRoleGroup = SysRoleGroupConvert.INSTANCE.reqToDO(sysRoleGroupReq);
        updateById(sysRoleGroup);
    }

    @DataPermission(dataType = SystemConstants.DATA_TYPE_ROLE_GROUP, operType = SystemConstants.OPER_TYPE_DELETE)
    @Override
    public void delete(Long id) {
        LambdaQueryWrapper<SysOrg> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orgLambdaQueryWrapper.eq(SysOrg::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        orgLambdaQueryWrapper.eq(SysOrg::getRoleGroupId, id);
        orgLambdaQueryWrapper.last("LIMIT 1");
        SysOrg sysOrg = orgService.getOne(orgLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(sysOrg)){
            throw new ServiceException("当前角色组已关联组织机构，不允许删除!");
        }

        SysRoleGroup sysRoleGroup = new SysRoleGroup();
        sysRoleGroup.setId(id);
        sysRoleGroup.setDelFlag(DelFlagEnum.DELETED.getCode());
        updateById(sysRoleGroup);

        threadLocalKeyConfig.set(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRole(SysRoleGroupRoleReq sysRoleGroupRoleReq) {
        //先根据角色组ID删除角色与角色组的关联关系
        LambdaUpdateWrapper<SysRoleRoleGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysRoleRoleGroup::getRoleGroupId, sysRoleGroupRoleReq.getId());
        sysRoleRoleGroupService.remove(updateWrapper);
        //重新添加角色与角色组的关系
        List<Long> roleIdList = sysRoleGroupRoleReq.getRoleIdList();
        List<SysRoleRoleGroup> sysRoleRoleGroupList = new ArrayList<>();
        roleIdList.forEach(i->{
            SysRoleRoleGroup sysRoleRoleGroup = new SysRoleRoleGroup();
            sysRoleRoleGroup.setRoleGroupId(sysRoleGroupRoleReq.getId());
            sysRoleRoleGroup.setRoleId(i);
            sysRoleRoleGroupList.add(sysRoleRoleGroup);
        });
        sysRoleRoleGroupService.saveBatch(sysRoleRoleGroupList);
    }

    @Override
    public List<String> listRoleByRoleGroupId(Long id) {
        //查询角色组下的角色信息
        LambdaQueryWrapper<SysRoleRoleGroup> roleRoleGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
        roleRoleGroupLambdaQueryWrapper.eq(SysRoleRoleGroup::getRoleGroupId, id);
        List<SysRoleRoleGroup> roleRoleGroupList = sysRoleRoleGroupService.list(roleRoleGroupLambdaQueryWrapper);
        //获取角色ID集合
        List<String> roleIdList = roleRoleGroupList.stream().map(s -> StrUtil.toString(s.getRoleId())).collect(Collectors.toList());

        return roleIdList;
    }

    /**
     * @Description: 检查名称或编码是否存在
     * @author: tr
     * @Date: 2024/3/1 11:32
     * @param: [sysRoleGroupReq]
     * @returnValue: void
     */
    private void checkExist(SysRoleGroupReq sysRoleGroupReq){
        String name = sysRoleGroupReq.getName();
        String code = sysRoleGroupReq.getCode();
        Long id = sysRoleGroupReq.getId();
        LambdaQueryWrapper<SysRoleGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRoleGroup::getName, name);
        queryWrapper.ne(ObjectUtil.isNotNull(id), SysRoleGroup::getId, id);
        queryWrapper.eq(SysRoleGroup::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.last("limit 1");
        SysRoleGroup sysRoleGroup = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysRoleGroup)){
            throw new ServiceException("角色组名称已存在!");
        }

        queryWrapper.clear();
        queryWrapper.eq(SysRoleGroup::getCode, code);
        queryWrapper.ne(ObjectUtil.isNotNull(id), SysRoleGroup::getId, id);
        queryWrapper.eq(SysRoleGroup::getDelFlag, DelFlagEnum.NO_DELETED.getCode());
        queryWrapper.last("limit 1");
        sysRoleGroup = getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(sysRoleGroup)){
            throw new ServiceException("角色组编码已存在!");
        }
    }
}
