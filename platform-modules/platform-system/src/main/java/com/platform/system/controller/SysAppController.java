package com.platform.system.controller;

import com.ctdi.base.biz.controller.BaseController;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.system.domain.request.SysAppPageReq;
import com.platform.system.domain.request.SysAppReq;
import com.platform.system.domain.response.SysAppResp;
import com.platform.system.service.SysAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 应用管理前端控制器
 * @author: tr
 * @date: 2024年03月27日 14:14
 */
@Api(tags = "应用管理前端控制器")
@RestController
@RequestMapping("/app")
public class SysAppController extends BaseController {

    @Autowired
    private SysAppService appService;

    @RequiresPermissions("system:app:page")
    @ApiOperation(value = "查询应用",  notes = "应用管理")
    @GetMapping("page")
    public ResponseResult<PageResult<SysAppResp>> page(SysAppPageReq sysAppPageReq){
        Page<SysAppResp> page = PageUtils.startPage(sysAppPageReq.getPageNum(), sysAppPageReq.getPageSize());
        List<SysAppResp> list = appService.list(sysAppPageReq);
        PageResult pageResult = new PageResult(list, page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.ok(pageResult);
    }

    @RequiresPermissions("system:app:getById")
    @ApiOperation(value = "查询单个应用",  notes = "应用管理")
    @GetMapping("getById")
    public ResponseResult<SysAppResp> getById(Long id){
        SysAppResp sysAppResp = appService.getById(id);
        return ResponseResult.ok(sysAppResp);
    }

    @RequiresPermissions("system:app:save")
    @Log(title = "应用管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增应用",  notes = "应用管理")
    @PostMapping("save")
    public ResponseResult save(@Validated @RequestBody SysAppReq sysAppReq){
        appService.save(sysAppReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:app:update")
    @Log(title = "应用管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改应用",  notes = "应用管理")
    @PostMapping("update")
    public ResponseResult update(@Validated @RequestBody SysAppReq sysAppReq){
        appService.update(sysAppReq);
        return ResponseResult.ok();
    }

    @RequiresPermissions("system:app:delete")
    @Log(title = "应用管理", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除应用",  notes = "应用管理")
    @DeleteMapping("delete")
    public ResponseResult delete(@RequestParam Long id){
        appService.delete(id);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "记录应用访问",  notes = "应用管理")
    @GetMapping("addAppVisit")
    public ResponseResult addAppVisit(@RequestParam Long id){
        appService.addAppVisit(id);
        return ResponseResult.ok();
    }

    @ApiOperation(value = "查询当日应用访问",  notes = "应用管理")
    @GetMapping("getAppVisit")
    public ResponseResult getAppVisit(){
        return ResponseResult.ok(appService.getAppVisit());
    }
}
