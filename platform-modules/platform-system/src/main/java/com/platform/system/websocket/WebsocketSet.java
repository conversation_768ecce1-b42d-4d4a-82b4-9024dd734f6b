package com.platform.system.websocket;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * @Description: Websocket信息存储类
 * @author: tr
 * @Date: 2024/4/17 10:49
 */
public class WebsocketSet {
    private WebsocketSet() {
    }

    /**
     * 用于存所有的连接服务的客户端，这个对象存储是安全的
     */
    private static final ConcurrentHashMap<String, WebSocket> WEBSOCKET_MAP = new ConcurrentHashMap<>();


    /**
     * 用户ID与sessionID的对应关系
     */
    private static final ConcurrentMap<String, List<String>> USER_SESSION_MAP = new ConcurrentHashMap<>();

    /**
     * sessionID与用户ID与的对应关系
     */
    private static final ConcurrentMap<String, String> SESSION_USER_MAP = new ConcurrentHashMap<>();


    public static ConcurrentMap<String, WebSocket> getWebsocketMap() {
        return WEBSOCKET_MAP;
    }

    public static ConcurrentMap<String, List<String>> getUserSessionMap() {
        return USER_SESSION_MAP;
    }

    public static ConcurrentMap<String, String> getSessionUserMap() {
        return SESSION_USER_MAP;
    }
}
