package com.platform.system.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 短信信息属性配置类
 * @author: tr
 * @date: 2024年05月21日 19:51
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "message.sms")
public class SmsProperties {

    /** 短信发送的key **/
    private String appKey;

    /** 短信发送的密钥 **/
    private String secretKey;

    /** 验证码短信内容模板 **/
    private String validateContent;

    /** 忘记密码短信内容模板 **/
    private String forgetPwdContent;

    /** 修改密码短信内容模板 **/
    private String updatePwdContent;

    /** 短信发送的通道类型 **/
    private Integer type;

    /** 手机号码登录验证码短信内容模板 **/
    private String validateLoginContent;
}
