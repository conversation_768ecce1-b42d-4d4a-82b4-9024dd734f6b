package com.platform.system.openapi.service;

/**
 * @Description: 用户OpenApi接口层
 * @author: tr
 * @date: 2024年04月09日 10:54
 */
public interface UserService {

    /**
     * @Description: 查询用户信息列表
     * @author: tr
     * @Date: 2024/4/9 14:09
     * @param updateDate 更新时间
     * @param code 应用编码
     * @returnValue: 返回国密SM4标准的加密字符串
     */
    String listAllV1(String updateDate, String code);

    /**
     * @Description: 分页查询用户信息
     * @author: tr
     * @Date: 2024/11/28 17:09
     * @param updateDate 更新日期
     * @param code 应用编码
     * @param pageNum 当前页码
     * @param pageSize 每页显示条数
     * @returnValue: java.lang.String
     */
    String listAllV2(String updateDate, String code, Integer pageNum, Integer pageSize);
    
    /**
     * @Description: 查询单个用户信息
     * @author: tr
     * @Date: 2024/8/21 15:24
     * @param: [userId]
     * @returnValue: java.lang.String
     */
    String getUserInfo(Long userId, String code);
}
