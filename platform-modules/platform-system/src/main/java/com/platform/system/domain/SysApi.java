package com.platform.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

@Data
@ToString
@TableName("sys_api")
public class SysApi  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/** 应用ID */
	private Long appId;

	/**
	 * 类型（1-目录，2-API权限）
	 */
	private String type;

	/**
	 * 父ID
	 */
	private Long parentId;

	/** 父ID路径，以逗号分隔 **/
	private String parentIdPath;

	/**
	 * API名称
	 */
	private String name;

	private String requestMethod;

	/**
	 * API权限字符或路径
	 */
	private String perms;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态（0正常 1停用）
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	private String delFlag;

}
