package com.platform.system.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.CaptchaException;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.sign.Base64;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.common.starter.toolbox.uuid.IdUtils;
import com.google.code.kaptcha.Producer;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.config.properties.CaptchaProperties;
import com.platform.system.constant.BusinessConstants;
import com.platform.system.domain.response.ValidateCodeResp;
import com.platform.system.service.ValidateCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.FastByteArrayOutputStream;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码实现处理
 *
 * <AUTHOR>
 */
@Service
public class ValidateCodeServiceImpl implements ValidateCodeService
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Resource(name = "captchaProducerNumber")
    private Producer captchaProducerNumber;

    @Autowired
    private CacheClient redisService;

    @Autowired
    private CaptchaProperties captchaProperties;

    /**
     * 生成验证码
     */
    @Override
    public ValidateCodeResp createCaptcha()
    {
        ValidateCodeResp validateCodeResp = new ValidateCodeResp();
        Boolean captchaEnabled = captchaProperties.getEnabled();
        validateCodeResp.setCaptchaEnabled(captchaEnabled);
        if (!captchaEnabled)
        {
            return validateCodeResp;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        String captchaType = captchaProperties.getType();
        // 生成验证码
        if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }else if ("number".equals(captchaType)){
            capStr = code = captchaProducerNumber.createText();
            image = captchaProducerNumber.createImage(capStr);
        }else{
            //默认使用math
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }

        redisService.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            throw new CaptchaException("验证码生成异常!");
        }
        //加密盐值
        String securitySalt = RandomUtil.randomString(BusinessConstants.SECURITY_SALT_CHARS, 32);

        redisService.setCacheObject(CacheConstants.LOGIN_PASSWORD_SALT + uuid
                , securitySalt, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);

        validateCodeResp.setSecuritySalt(securitySalt);
        validateCodeResp.setUuid(uuid);
        validateCodeResp.setImg(Base64.encode(os.toByteArray()));
        return validateCodeResp;
    }

    /**
     * 校验验证码
     */
    @Override
    public void checkCaptcha(String code, String uuid) throws CaptchaException
    {
        if (StringUtils.isEmpty(code))
        {
            throw new CaptchaException("验证码不能为空");
        }
        if (StringUtils.isEmpty(uuid))
        {
            throw new CaptchaException("验证码已失效");
        }
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisService.getCacheObject(verifyKey);
        redisService.deleteObject(verifyKey);

        if (!code.equalsIgnoreCase(captcha))
        {
            throw new CaptchaException("验证码错误");
        }
    }
}
