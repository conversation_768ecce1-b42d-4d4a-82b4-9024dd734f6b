package com.platform.system.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @Description: API权限配置新增或修改入参
 * @author: tr
 * @date: 2024年02月29日 11:17
 */
@ApiModel("API权限配置新增或修改入参")
@Data
@ToString
public class SysApiReq{

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	private Long id;

	/** 应用ID */
	@ApiModelProperty("应用ID")
	private Long appId;

	/**
	 * 类型（1-目录，2-API权限）
	 */
	@ApiModelProperty("类型（1-目录，2-API权限）")
	private String type;

	/**
	 * 父ID
	 */
	@ApiModelProperty("父ID，第一级传0")
	private Long parentId;

	/** 父ID路径，以逗号分隔 **/
	@ApiModelProperty("父ID路径，以逗号分隔，第一级传0")
	private String parentIdPath;

	/**
	 * API名称
	 */
	@NotBlank(message = "权限名称不能为空")
	@Size(min = 1, max = 100, message = "权限名称不能超过100个字符")
	@ApiModelProperty("API名称")
	private String name;

	/**
	 * API权限字符或路径
	 */
	@Size(min = 1, max = 100, message = "权限标识不能超过100个字符")
	@ApiModelProperty("API权限字符或路径")
	private String perms;

	@ApiModelProperty("请求方式（POST、GET、PUT、DELETE）")
	private String requestMethod;

	/**
	 * 备注
	 */
	@Size(min = 0, max = 500, message = "权限描述不能超过500个字符")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 角色ID
	 */
	@ApiModelProperty("角色ID")
	private Long roleId;
}
