package com.platform.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

import java.util.Date;

@Data
@ToString
@TableName("sys_user_extend")
public class SysUserExtend  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 扩展字段字符类型1
	 */
	private String extendStr1;

	/**
	 * 扩展字段字符类型2
	 */
	private String extendStr2;

	/**
	 * 扩展字段字符类型3
	 */
	private String extendStr3;

	/**
	 * 扩展字段字符类型4
	 */
	private String extendStr4;

	/**
	 * 扩展字段字符类型5
	 */
	private String extendStr5;

	/**
	 * 扩展字段字符类型6
	 */
	private String extendStr6;

	/**
	 * 扩展字段字符类型7
	 */
	private String extendStr7;

	/**
	 * 扩展字段字符类型8
	 */
	private String extendStr8;

	/**
	 * 扩展字段字符类型9
	 */
	private String extendStr9;

	/**
	 * 扩展字段字符类型10
	 */
	private String extendStr10;

	/**
	 * 扩展字段整型类型1
	 */
	private Long extendInt1;

	/**
	 * 扩展字段整型类型2
	 */
	private Long extendInt2;

	/**
	 * 扩展字段整型类型3
	 */
	private Long extendInt3;

	/**
	 * 扩展字段小数类型1
	 */
	private Double extendDouble1;

	/**
	 * 扩展字段小数类型2
	 */
	private Double extendDouble2;

	/**
	 * 扩展字段小数类型3
	 */
	private Double extendDouble3;

	/**
	 * 扩展字段时间类型1
	 */
	private Date extendDatetime1;

	/**
	 * 扩展字段时间类型2
	 */
	private Date extendDatetime2;

	/**
	 * 备注
	 */
	private String remark;

}
