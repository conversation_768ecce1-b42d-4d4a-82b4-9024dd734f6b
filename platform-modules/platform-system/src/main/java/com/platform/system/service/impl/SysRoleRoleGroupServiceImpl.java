package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.domain.SysRoleRoleGroup;
import com.platform.system.mapper.SysRoleRoleGroupMapper;
import com.platform.system.service.SysRoleRoleGroupService;
import org.springframework.stereotype.Service;

/**
 * @Description: 角色与角色组关系Service实现类
 * @author: tr
 * @date: 2024年03月01日 14:52
 */
@Service
public class SysRoleRoleGroupServiceImpl extends ServiceImpl<SysRoleRoleGroupMapper, SysRoleRoleGroup>
        implements SysRoleRoleGroupService {

    @Override
    public void removeByRoleIds(Long[] roleIds) {
        LambdaUpdateWrapper<SysRoleRoleGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SysRoleRoleGroup::getRoleId, roleIds);
        remove(updateWrapper);
    }
}
