package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import lombok.Data;

/**
 * @Description: 用户主题信息表
 * @author: tr
 * @date: 2023年12月08日 16:33
 */
@Data
@TableName("sys_user_subject")
public class SysUserSubject extends BaseEntity {

    /** 主键ID */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 主题信息 */
    private String subjectInfo;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
}
