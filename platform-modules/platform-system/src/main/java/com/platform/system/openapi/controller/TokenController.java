package com.platform.system.openapi.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.common.starter.share.constant.Constants;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.utils.JwtUtils;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.common.toolbox.utils.AuthUtils;
import com.platform.system.api.domain.SysLogininfor;
import com.platform.system.openapi.domain.response.UserResp;
import com.platform.system.openapi.service.TokenService;
import com.platform.system.service.SysLogininforService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: OpenAPI接口-token控制层
 * @author: tr
 * @date: 2024年03月22日 16:25
 */
@RestController
@RequestMapping("/openapi/v1")
public class TokenController {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLogininforService sysLogininforService;

    /**
     * @Description: 校验token，并返回当前用户的信息
     * @author: tr
     * @Date: 2024/3/22 17:53
     * @param: [request]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<com.platform.system.openapi.domain.response.UserResp>
     */
    @GetMapping("/validateToken")
    public ResponseResult<UserResp> validateToken(HttpServletRequest request){
        String token = request.getHeader("Authorization");
        UserResp userResp = tokenService.validateToken(token);
        return ResponseResult.ok(userResp);
    }

    @PostMapping("app/outOfLogin")
    public ResponseResult<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtils.logoutByToken(token);
            // 记录用户退出日志

            SysLogininfor logininfor = new SysLogininfor();
            logininfor.setUserName(username);
            logininfor.setIpaddr(IpUtils.getIpAddr());
            logininfor.setMsg("退出成功");
            // 日志状态
            if (StringUtils.equalsAny(Constants.LOGOUT, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
            {
                logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
            }
            else if (Constants.LOGIN_FAIL.equals(Constants.LOGOUT))
            {
                logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
            }
            sysLogininforService.insertLogininfor(logininfor);
        }
        return ResponseResult.ok();
    }
}
