package com.platform.system.openapi.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.page.PageResult;
import com.ctdi.base.biz.utils.PageUtils;
import com.github.pagehelper.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.system.api.domain.SysUser;
import com.platform.system.domain.SysUserOrg;
import com.platform.system.mapper.SysUserMapper;
import com.platform.system.openapi.domain.response.UserResp;
import com.platform.system.openapi.service.UserService;
import com.platform.system.utils.EncrypDecrypUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 用户OpenApi实现层
 * @author: tr
 * @date: 2024年04月09日 10:55
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private CacheClient redisService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public String listAllV1(String updateDate, String code) {
        //查询用户信息
        List<UserResp> userRespList = listAll(updateDate);
        return encryptionStr(userRespList, code);
    }

    @Override
    public String listAllV2(String updateDate, String code, Integer pageNum, Integer pageSize) {
        pageNum = pageNum == null ? 1 : pageNum;
        pageSize = pageSize == null ? 1000 : pageSize;
        if (pageSize > 1000){
            pageSize = 1000;
        }
        Page<UserResp> page = PageUtils.startPage(pageNum, pageSize);
        List<UserResp> userRespList = listAll(updateDate);
        PageResult pageResult = new PageResult(userRespList, page.getTotal(), page.getPageNum(), page.getPageSize());
        return encryptionStr(pageResult, code);
    }

    @Override
    public String getUserInfo(Long userId, String code) {
        List<UserResp> userRespList = sysUserMapper.listUserDept(null, userId);
        UserResp sysUserResp = userRespList.get(0);

        //查询用户与组织机构的关系
        MPJLambdaWrapper<SysUserOrg> wrapper = JoinWrappers.lambda(SysUserOrg.class)
                .selectAll(SysUserOrg.class).leftJoin(SysUser.class, SysUser::getUserId, SysUserOrg::getUserId)
                .eq(SysUser::getDelFlag, DelFlagEnum.NO_DELETED.getCode())
                .eq(SysUser::getUserId, userId);
        List<SysUserOrg> sysUserOrgList = wrapper.list();
        Map<Long, List<Long>> userIdOrgIdList =
                sysUserOrgList.stream()
                        .collect(Collectors.groupingBy(SysUserOrg::getUserId, Collectors.mapping(SysUserOrg::getOrgId, Collectors.toList())));
        return encryptionStr(sysUserResp, code);
    }

    private List<UserResp> listAll(String updateDate){
        //查询用户信息
        List<UserResp> userRespList = sysUserMapper.listUserDept(updateDate, null);

        //查询用户与组织机构的关系
        MPJLambdaWrapper<SysUserOrg> wrapper = JoinWrappers.lambda(SysUserOrg.class)
                .selectAll(SysUserOrg.class).leftJoin(SysUser.class, SysUser::getUserId, SysUserOrg::getUserId)
                .eq(SysUser::getDelFlag, DelFlagEnum.NO_DELETED.getCode())
                .ge(StrUtil.isNotBlank(updateDate), SysUser::getUpdateTime, updateDate);
        List<SysUserOrg> sysUserOrgList = wrapper.list();
        Map<Long, List<Long>> userIdOrgIdList =
                sysUserOrgList.stream()
                        .collect(Collectors.groupingBy(SysUserOrg::getUserId, Collectors.mapping(SysUserOrg::getOrgId, Collectors.toList())));

//        userRespList.forEach(user -> {
//            user.setOrgIds(userIdOrgIdList.get(user.getUserId()));
//        });
        return userRespList;
    }

    /**
     * @Description: 加密字符串
     * @author: tr
     * @Date: 2024/8/21 15:36
     * @param data 待加密的内容
     * @param code 应用编码
     * @returnValue: java.lang.String
     */
    private String encryptionStr(Object data, String code){
        //根据应用编码从缓存中获取key用于加密
        Map<String, String> codeKeyMap = redisService.getCacheMap(CacheConstants.APP_CODE_PUBLIC_KEY_MAP);
        String key = codeKeyMap.get(code);
        String content = EncrypDecrypUtils.encrypt(
                JSONUtil.toJsonStr(data), key);
        return content;
    }
}
