package com.platform.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.SysOperLog;
import com.platform.system.api.domain.request.SysOperLogReq;
import com.platform.system.domain.request.SysOperLogPageReq;
import com.platform.system.domain.response.SysOperLogResp;

/**
 * 操作日志 服务层
 * 
 * <AUTHOR>
 */
public interface SysOperLogService extends IService<SysOperLog>
{
    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     * @return 结果
     */
    public int insertOperlog(SysOperLogReq sysOperLogReq);

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLogResp> selectOperLogList(SysOperLogPageReq sysOperLogPageReq);

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();
}
