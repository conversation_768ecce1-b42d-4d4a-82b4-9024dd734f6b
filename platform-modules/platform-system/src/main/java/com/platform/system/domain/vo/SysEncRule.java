package com.platform.system.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 加密规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SysEncRule对象", description = "加密规则表")
public class SysEncRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表主键
     */
    @ApiModelProperty(value = "表主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String columnName;

    /**
     * 加密类型
     */
    @ApiModelProperty(value = "加密类型")
    private String encType;


}
