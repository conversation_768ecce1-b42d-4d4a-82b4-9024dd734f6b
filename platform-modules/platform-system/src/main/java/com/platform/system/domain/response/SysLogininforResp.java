package com.platform.system.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月01日 19:28
 */
@Data
@ToString
public class SysLogininforResp {

    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "记录ID")
    @Excel(name = "序号", cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long infoId;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    @Excel(name = "用户账号")
    private String userName;

    /** 状态 0成功 1失败 */
    @ApiModelProperty(value = "状态 0成功 1失败")
    @Excel(name = "状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 地址 */
    @ApiModelProperty(value = "地址")
    @Excel(name = "地址")
    private String ipaddr;

    /** 描述 */
    @ApiModelProperty(value = "描述")
    @Excel(name = "描述")
    private String msg;

    /** 访问时间 */
    @ApiModelProperty(value = "访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date accessTime;
}
