package com.platform.system.convert;

import cn.hutool.core.util.StrUtil;
import com.platform.system.api.domain.request.SysUserExtendReq;
import com.platform.system.api.domain.response.SysUserExtendResp;
import com.platform.system.api.model.SysUserExtendDTO;
import com.platform.system.domain.SysUserExtend;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 用户信息扩展数据转换
 * @author: tr
 * @date: 2024年05月14日 17:04
 */
@Mapper
public interface SysUserExtendConvert {

    SysUserExtendConvert INSTANCE = Mappers.getMapper(SysUserExtendConvert.class);

    SysUserExtend reqToDO(SysUserExtendReq sysUserExtendReq);

    SysUserExtendDTO doToDTO(SysUserExtend sysUserExtend);

    @Mapping(source = "extendStr8", target = "extendStr8s", qualifiedByName = "stringToCharArray")
    SysUserExtendResp doToResp(SysUserExtend sysUserExtend);

    List<SysUserExtendResp> doListToRespList(List<SysUserExtend> sysUserExtendList);

    @Named("stringToCharArray")
    default String[] stringToCharArray(String string) {
        if (StrUtil.isNotBlank(string)){
            return string.split(",");
        }
        return null;
    }
}
