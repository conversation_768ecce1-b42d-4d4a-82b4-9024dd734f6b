package com.platform.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.platform.system.domain.SysRoleApi;
import com.platform.system.mapper.SysRoleApiMapper;
import com.platform.system.service.SysRoleApiService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 角色与API关系Service
 * @author: tr
 * @date: 2024年03月20日 16:44
 */
@Service
public class SysRoleApiServiceImpl extends ServiceImpl<SysRoleApiMapper, SysRoleApi> implements SysRoleApiService {

    @Override
    public List<SysRoleApi> listByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleApi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRoleApi::getRoleId, roleId);
        List<SysRoleApi> list = list(queryWrapper);
        return list;
    }
}
