package com.platform.system.convert;

import com.platform.system.api.domain.SysDictData;
import com.platform.system.api.domain.response.SysDictDataResp;
import com.platform.system.api.model.SysDictDataDTO;
import com.platform.system.domain.request.SysDictDataReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * @Description: 字典类型信息转换
 * @author: tr
 * @date: 2024年02月02日 16:05
 */
@Mapper
public interface SysDictDataConvert {

    SysDictDataConvert INSTANCE = Mappers.getMapper(SysDictDataConvert.class);

    SysDictDataDTO doToDTO(SysDictData sysDictData);

    List<SysDictDataDTO> doToDTOList(List<SysDictData> sysDictDataList);

    List<SysDictData> DTOToDoList(List<SysDictDataDTO> sysDictDataDTOList);

    SysDictData reqToDO(SysDictDataReq sysDictDataReq);

    SysDictDataResp doToResp(SysDictData sysDictData);

    List<SysDictDataResp> doListToRespList(List<SysDictData> sysDictDataList);

    List<SysDictData> reqListToDoList(List<SysDictDataReq> sysDictDataReqList);
}
