package com.platform.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.domain.SysUserRole;

import java.util.List;

/**
 * @Description: 用户与角色Service接口
 * @author: tr
 * @date: 2024年04月01日 18:59
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    /**
     * @Description: 根据用户ID查询用户下的角色信息
     * @author: tr
     * @Date: 2024/4/1 19:03
     * @param: [userId]
     * @returnValue: java.util.List<com.platform.system.domain.SysUserRole>
     */
    List<SysUserRole> listByUserId(Long userId);
}
