package com.platform.system.service;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.domain.request.SysUserPageReq;
import com.platform.system.api.domain.request.SysUserReq;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.api.model.LoginUser;
import com.platform.system.domain.request.*;
import com.platform.system.domain.response.CertificateNoValidateResp;
import com.platform.system.openapi.domain.response.UserResp;

/**
 * 用户 业务层
 * 
 * <AUTHOR>
 */
public interface SysUserService extends IService<SysUser>
{
    /**
     * 根据条件分页查询用户列表
     * 
     * @param sysUserPageReq 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserResp> selectUserList(SysUserPageReq sysUserPageReq);

    /**
     * 根据条件分页查询已分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserResp> selectAllocatedList(SysUserPageReq user);

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param sysUserPageReq 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserResp> selectUnallocatedList(SysUserPageReq sysUserPageReq);

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUserResp selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     * 
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     * 
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     * 
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     * 
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     * 
     * @param sysUserReq 用户信息
     * @return 结果
     */
    void insertUser(SysUserReq sysUserReq);

    /**
     * 注册用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     * 
     * @param sysUserReq 用户信息
     * @return 结果
     */
    void updateUser(SysUserReq sysUserReq);

    /**
     * 用户授权角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user);

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     * 
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);

    /**
     * 根据用户ID查询该用户拥有的部门权限
     * @param userId
     * @return
     */
    public Set<Long> getPermissionDeptIdsByUserId(Long userId);

    /**
     * @Description: 根据用户名获取当前登录用户信息
     * @author: tr
     * @Date: 2024/3/20 10:28
     * @param: [username]
     * @returnValue: com.platform.system.api.model.LoginUser
     */
    LoginUser loginUserInfo(String username);
    
    /**
     * @Description: 根据用户ID，查询用户信息
     * @author: tr
     * @Date: 2024/3/22 11:14
     * @param: [id]
     * @returnValue: com.platform.system.api.domain.response.SysUserResp
     */
    SysUserResp getUserById(Long id);

    /**
     * @Description: 根据角色ID集合查询用户信息
     * @author: tr
     * @Date: 2024/4/1 20:11
     * @param: [roldIdList]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysUserResp>
     */
    List<SysUserResp> listByRoleIds(Long[] roldIdList);

    /**
     * @Description: 根据组织机构ID集合查询用户信息列表
     * @author: tr
     * @Date: 2024/4/16 10:04
     * @param orgIdList 组织机构ID数组
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysUserResp>
     */
    List<SysUserResp> listByOrgIds(Long[] orgIdList);

    /**
     * @Description: 根据组织机构ID和角色ID查询用户信息集合
     * @author: tr
     * @Date: 2024/4/16 10:18
     * @param: [orgIdList, roldIdList]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysUserResp>
     */
    List<SysUserResp> listByOrgIdsAndRoleIds(Long[] orgIdList, Long[] roldIdList);

    /**
     * @Description: 根据角色ID查询和组织机构管辖范围地址码查询本级及以上的用户信息
     * @author: tr
     * @Date: 2024/5/20 18:03
     * @param roleCode 角色编码
     * @param guCode 管辖范围地址码
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysUserResp>
     */
    List<SysUserResp> listByRoleCodeAndGuCode(String roleCode, String guCode);

    /**
     * @Description: 批量修改用户信息
     * @author: tr
     * @Date: 2024/5/31 9:57
     * @param: [sysUserReqList]
     * @returnValue: void
     */
    void updateBatch(List<SysUserReq> sysUserReqList);

    /**
     * @Description: 通过证件号码获取用户手机号和唯一uuid，其中手机号码脱敏处理
     * @author: tr
     * @Date: 2024/6/11 19:54
     * @param certificateNoValidateReq 证件号码
     * @returnValue: com.platform.system.domain.response.CertificateNoValidateResp
     */
    CertificateNoValidateResp getMobileByCard(CertificateNoValidateReq certificateNoValidateReq);

    /**
     * @Description: 忘记密码修改密码，根据uuid和certificateNo校验身份的合法性，密码加密传输
     * @author: tr
     * @Date: 2024/6/12 9:31
     * @param: [forgetPwdReq]
     * @returnValue: void
     */
    void updateForgetPwd(ForgetPwdReq forgetPwdReq);

    /**
     * @Description: 登录成功，修改密码，判断旧密码是否正确，新密码和确认密码是否一致，判断短信验证码是否正确
     * @author: tr
     * @Date: 2024/7/12 10:16
     * @param: [updatePwdReq]
     * @returnValue: void
     */
    void updatePwd(UpdatePwdReq updatePwdReq);

    /**
     * @Description: 登录成功，修改个人信息
     * @author: tr
     * @Date: 2024/7/12 10:18
     * @param: [sysUserReq]
     * @returnValue: void
     */
    void updatePersonalInfo(SysUserReq sysUserReq);

    /**
     * @Description: 通过部门和姓名生成用户
     * @author: mbt
     * @Date: 2024/9/12 11:40
     * @param deptId 部门ID
     * @param userName 姓名
     * @returnValue: SysUser
     */
    SysUser createUserByDeptAndName(Long deptId, String userName);

    /**
     * @Description: 设置密码
     * @author: tr
     * @Date: 2025/3/4 14:13
     * @param: [updatePwdMessageReq]
     * @returnValue: void
     */
    void setupPassword(SetupPwdReq setupPwdReq);

    /**
     * @Description: 修改手机号码下一步，返回唯一UUID，用于后续验证手机号码和短信验证码
     * @author: tr
     * @Date: 2025/3/4 15:36
     * @param: [updatePhonenumberReq]
     * @returnValue: java.lang.String
     */
    String updatePhonenumberNext(UpdatePhonenumberReq updatePhonenumberReq);

    /**
     * @Description: 修改手机号码，需先根据原手机号码获取验证码并校验成功后，才能修改
     * @author: tr
     * @Date: 2025/3/4 15:50
     * @param: [updatePhonenumberReq]
     * @returnValue: void
     */
    void updatePhonenumber(UpdatePhonenumberReq updatePhonenumberReq);

    /**
     * @Description: 通过手机号修改密码
     * @author: tr
     * @Date: 2025/3/5 16:30
     * @param: [forgetPwdPhonenumberReq]
     * @returnValue: void
     */
    void updateForgetPhonenumberPwd(ForgetPwdPhonenumberReq forgetPwdPhonenumberReq);

    /**
     * @Description: 根据用户ID集合查询用户列表
     * @author: tr
     * @Date: 2025/3/26 20:51
     * @param: [userIdList]
     * @returnValue: java.util.List<com.platform.system.api.domain.response.SysUserResp>
     */
    List<SysUserResp> listByIdList(Long[] userIdList);

    /**
     * @Description: 切换用户的登陆机构和角色
     * @author: tr
     * @Date: 2025/3/26 20:51
     * @param: [switchOrgAndRoleReq]
     * @returnValue: voidvs
     */
    void switchOrgAndRole(SwitchOrgAndRoleReq switchOrgAndRoleReq);

    /**
     * @Description: 我还没有写描述
     * @author: tr
     * @Date: 2025/7/22 20:32
     * @param: [token]
     * @returnValue: com.platform.system.openapi.domain.response.UserResp
     */
    UserResp validateToken(String token);
}
