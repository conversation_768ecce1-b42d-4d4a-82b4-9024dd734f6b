package com.platform.system.convert;

import com.platform.system.api.domain.SysOperLog;
import com.platform.system.api.domain.request.SysOperLogReq;
import com.platform.system.domain.response.SysOperLogResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月01日 19:50
 */
@Mapper
public interface SysOperLogConvert {

    SysOperLogConvert INSTANCE = Mappers.getMapper(SysOperLogConvert.class);

    SysOperLogResp doToResp(SysOperLog sysOperLog);

    List<SysOperLogResp> doListToRespList(List<SysOperLog> sysOperLogList);

    SysOperLog reqToDO(SysOperLogReq operLog);
}
