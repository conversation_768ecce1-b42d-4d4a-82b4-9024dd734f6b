<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.system.mapper.SysAppKeyPairMapper">
    
    <resultMap type="SysAppKeyPair" id="SysAppKeyPairResult">
    	<id     property="id"      column="id"      />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"     column="org_name"     />
        <result property="appId"   column="app_id"   />
        <result property="appName"    column="app_name"    />
        <result property="appKey"    column="app_key"    />
        <result property="appSecret"    column="app_secret"    />
        <result property="status"             column="status"                />
        <result property="delFlag"            column="del_flag"              />
        <result property="createBy"           column="create_by"             />
        <result property="createTime"         column="create_time"           />
        <result property="updateBy"           column="update_by"             />
        <result property="updateTime"         column="update_time"           />
        <result property="remark"             column="remark"                />
    </resultMap>
    
    <sql id="selectAppKeyPairVo">
        select id, org_id,org_name, app_id, app_name, app_key, app_secret, status,'del_flag',create_by,create_time, update_by, update_time, remark
		from sys_app_key_pair
    </sql>

    <select id="selectAllEnabled"  resultMap="SysAppKeyPairResult">

        <include refid="selectAppKeyPairVo"/>
        where status = '0' and del_flag = '0'

    </select>

    <select id="selectByAppKey" parameterType="java.lang.String" resultMap="SysAppKeyPairResult">
        select <include refid="selectAppKeyPairVo"/>
            where status = '0' and del_flag = '0' and app_key = #{appKey}
    </select>

</mapper>