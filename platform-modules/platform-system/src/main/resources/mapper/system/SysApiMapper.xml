<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.system.mapper.SysApiMapper">

	<!-- 根据角色ID查询角色对应的权限字符 -->
	<select id="selectApiPermsByRoleId" resultType="SysApiDTO">
		select a.request_method, a.perms
		from sys_api a
			 left join sys_role_api ra on a.id = ra.api_id
		where a.del_flag = '0' and a.type = '2'
		  and ra.role_id in
		<foreach collection="list" item="roleId" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</select>

</mapper> 