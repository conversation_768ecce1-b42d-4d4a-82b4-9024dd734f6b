<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.system.mapper.SysOrgMapper">

	<resultMap type="SysOrgResp" id="SysOrgResult">
		<id     property="id"       column="id"      />
		<result property="name"       column="name"      />
		<result property="orgType"     column="org_type"    />
		<result property="parentId"     column="parent_id"    />
		<result property="contactsPerson"        column="contacts_person"        />
		<result property="contactsPhone"  column="contacts_phone"  />
		<result property="contactsWay"          column="contacts_way"          />
		<result property="dzName"       column="dz_name"       />
		<result property="dzCode"     column="dz_code"     />
		<result property="dzRegionLevel"       column="dz_region_level"       />
		<result property="dzDetailAddress"      column="dz_detail_address"     />
		<result property="roleGroupId"      column="role_group_id"     />
		<result property="guCode"    column="gu_code"   />
		<result property="guName"     column="gu_name"    />
		<result property="guRegionLevel"   column="gu_region_level"  />
		<result property="remark"     column="remark"    />
		<result property="extProperties"   column="ext_properties"  />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="orgCode"   column="org_code"  />
		<association property="sysOrgExtendResp"    javaType="SysOrgExtendResp"         resultMap="orgExtendResult" />
	</resultMap>

	<resultMap id="orgExtendResult" type="SysOrgExtendResp">
		<result property="id"    column="extend_id" />
		<result property="extendStr1"    column="extend_str1" />
		<result property="extendStr2"  column="extend_str2" />
		<result property="extendStr3"  column="extend_str3" />
		<result property="extendStr4" column="extend_str4" />
		<result property="extendStr5"  column="extend_str5" />
		<result property="extendStr6"    column="extend_str6" />
		<result property="extendStr7"    column="extend_str7" />
		<result property="extendStr8"  column="extend_str8" />
		<result property="extendStr9"    column="extend_str9" />
		<result property="extendStr10"    column="extend_str10" />
		<result property="extendStr11"    column="extend_str11" />
		<result property="extendStr12"    column="extend_str12" />
		<result property="extendStr13"    column="extend_str13" />
		<result property="extendStr14"    column="extend_str14" />
		<result property="extendStr15"    column="extend_str15" />
		<result property="extendInt1"    column="extend_int1" />
		<result property="extendInt2"    column="extend_int2" />
		<result property="extendInt3"    column="extend_int3" />
		<result property="extendDouble1"    column="extend_double1" />
		<result property="extendDouble2"    column="extend_double2" />
		<result property="extendDouble3"    column="extend_double3" />
		<result property="extendDatetime1"    column="extend_datetime1" />
		<result property="extendDatetime2"    column="extend_datetime2" />
	</resultMap>

	<!-- 查询用户ID所属的组织机构信息 -->
	<select id="listByUserId" resultMap="SysOrgResult">
		select a.*, e.extend_str1, e.extend_str2, e.extend_str3, e.extend_str4, e.extend_str5, e.extend_str6
		, e.extend_str7, e.extend_str8, e.extend_str9, e.extend_str10, e.extend_str11, e.extend_str12, e.extend_str13
		, e.extend_str14, e.extend_str15, e.extend_int1, e.extend_int2
		, e.extend_int3, e.extend_double1, e.extend_double2, e.extend_double3, e.extend_datetime1, e.extend_datetime2
			from sys_org a
		    left join sys_user_org b on a.id = b.org_id
			left join sys_org_extend e on a.id = e.id
		<where>
			b.user_id = #{userId} and a.del_flag = '0'
		</where>
	</select>

	<!-- 根据条件查询组织机构数据 -->
	<select id="listAllByCondition" parameterType="SysOrgPageReq" resultMap="SysOrgResult">
		select a.id, a.name, a.org_type, a.parent_id, a.contacts_person, a.contacts_phone
		       , a.contacts_way, a.dz_name, a.dz_code, a.dz_region_level, a.dz_detail_address
		       , a.role_group_id, a.gu_code, a.gu_name, a.gu_region_level, a.remark, a.ext_properties
		       , a.create_by, a.create_time
		     	, e.id as extend_id
				, e.extend_str1, e.extend_str2, e.extend_str3, e.extend_str4, e.extend_str5, e.extend_str6
				, e.extend_str7, e.extend_str8, e.extend_str9, e.extend_str10, e.extend_str11, e.extend_str12
		        , e.extend_str13, e.extend_str14, e.extend_str15, e.extend_int1, e.extend_int2
				, e.extend_int3, e.extend_double1, e.extend_double2, e.extend_double3, e.extend_datetime1, e.extend_datetime2
		from sys_org a left join sys_org_extend e on a.id = e.id
		<where>
			a.del_flag = '0'
			<if test="orgType != null">
				and a.org_type = #{orgType}
			</if>
			<if test="dzCode != null and dzCode != 0">
				AND a.dz_code = #{dzCode}
			</if>
			<if test="guCode != null and guCode != 0">
				AND a.gu_code = #{guCode}
			</if>
			<if test="name != null and name != ''">
				AND a.name like concat('%', #{name}, '%')
			</if>
			<if test="roleGroupId != null">
				and a.role_group_id = #{roleGroupId}
			</if>
			<if test="guPcode != null and guPcode != 0">
				AND a.gu_pcode_path like concat('%', #{guPcode}, '%')
			</if>
			<if test="extendStr1 != null and extendStr1 != ''">
				AND e.extend_str1 = #{extendStr1}
			</if>
			<if test="extendStr2 != null and extendStr2 != ''">
				AND e.extend_str2 = #{extendStr2}
			</if>
			<if test="extendStr3 != null and extendStr3 != ''">
				AND e.extend_str3 = #{extendStr3}
			</if>
			<if test="extendStr4 != null and extendStr4 != ''">
				AND e.extend_str4 like concat('%', #{extendStr4}, '%')
			</if>
			<if test="extendStr5 != null and extendStr5 != ''">
				AND e.extend_str5 like concat('%', #{extendStr5}, '%')
			</if>
			<if test="extendStr6 != null and extendStr6 != ''">
				AND e.extend_str6 like concat('%', #{extendStr6}, '%')
			</if>
			<if test="extendStr7s != null and extendStr7s.length > 0">
				AND (
				<foreach collection="extendStr7s" index="index" item="item" open="" separator="or" close="">
					e.extend_str7 like concat('%', #{item}, '%')
				</foreach>
				)
			</if>
			<if test="extendInt1 != null">
				AND e.extend_int1 = #{extendInt1}
			</if>
			<if test="extendInt2 != null">
				AND e.extend_int2 = #{extendInt2}
			</if>
			<if test="extendInt3 != null">
				AND e.extend_int3 = #{extendInt3}
			</if>
			<if test="extendDouble1 != null">
				AND e.extend_double1 = #{extendDouble1}
			</if>
			<if test="extendDouble2 != null">
				AND e.extend_double2 = #{extendDouble2}
			</if>
			<if test="extendDouble3 != null">
				AND e.extend_double3 = #{extendDouble3}
			</if>
			<if test="extendDatetime1Begin != null and extendDatetime1End != null and extendDatetime1Begin != '' and extendDatetime1End != ''">
				AND e.extend_datetime1 between #{extendDatetime1Begin} and #{extendDatetime1End}
			</if>
			<if test="extendDatetime1 != null and extendDatetime1 != ''">
				AND e.extend_datetime1 between concat(#{extendDatetime1},' 00:00:00') and concat(#{extendDatetime1},' 23:59:59')
			</if>
			<if test="extendDatetime1Begin != null and extendDatetime1Begin != '' and (extendDatetime1End == null or extendDatetime1End.isEmpty())">
				AND (e.extend_datetime1 &gt;= #{extendDatetime1Begin} or e.extend_datetime1 is null)
			</if>
			<if test="(extendDatetime1Begin == null or extendDatetime1Begin == '') and extendDatetime1End != null and extendDatetime1End != ''">
				AND e.extend_datetime1 &lt;= #{extendDatetime1End}
			</if>
		</where>
	</select>
</mapper> 