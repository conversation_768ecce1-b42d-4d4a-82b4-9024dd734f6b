<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.system.mapper.SysUserMapper">

    <resultMap type="SysUserResp" id="SysUserExtendResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
        <result property="certificateType"       column="certificate_type"       />
        <result property="certificateNo"       column="certificate_no"       />
        <result property="extProperties"       column="ext_properties"       />
		<result property="delFlag" column="del_flag" />
        <association property="dept"    javaType="SysDept"         resultMap="deptResult" />
		<association property="sysUserExtendResp"    javaType="SysUserExtendResp"         resultMap="userExtendResult" />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="userType"     column="user_type"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="certificateType"       column="certificate_type"       />
		<result property="certificateNo"       column="certificate_no"       />
		<result property="extProperties"       column="ext_properties"       />
		<result property="updatePwdFlag" column="update_pwd_flag"/>
		<result property="lastLoginOrgId"       column="last_login_org_id"       />
		<result property="lastLoginRole" column="last_login_role"/>
		<association property="dept"    javaType="SysDept"         resultMap="deptResult" />
		<collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
	</resultMap>

	<resultMap id="userExtendResult" type="SysUserExtendResp">
		<result property="extendStr1"    column="extend_str1" />
		<result property="extendStr2"  column="extend_str2" />
		<result property="extendStr3"  column="extend_str3" />
		<result property="extendStr4" column="extend_str4" />
		<result property="extendStr5"  column="extend_str5" />
		<result property="extendStr6"    column="extend_str6" />
		<result property="extendStr7"    column="extend_str7" />
		<result property="extendStr8"  column="extend_str8" />
		<result property="extendStr9"    column="extend_str9" />
		<result property="extendStr10"    column="extend_str10" />
		<result property="extendInt1"    column="extend_int1" />
		<result property="extendInt2"    column="extend_int2" />
		<result property="extendInt3"    column="extend_int3" />
		<result property="extendDouble1"    column="extend_double1" />
		<result property="extendDouble2"    column="extend_double2" />
		<result property="extendDouble3"    column="extend_double3" />
		<result property="extendDatetime1"    column="extend_datetime1" />
		<result property="extendDatetime2"    column="extend_datetime2" />
	</resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>
	
    <resultMap id="RoleResult" type="SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
    </resultMap>

	<resultMap type="UserResp" id="SysUserDeptResult">
		<id     property="userId"       column="user_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="status"       column="status"       />
		<result property="certificateType"       column="certificate_type"       />
		<result property="certificateNo"       column="certificate_no"       />
		<association property="dept"    javaType="DeptResp"         resultMap="deptRespResult" />
	</resultMap>

	<resultMap id="deptRespResult" type="DeptResp">
		<id     property="deptId"    column="dept_id"     />
		<result property="parentId"  column="parent_id"   />
		<result property="deptName"  column="dept_name"   />
		<result property="ancestors" column="ancestors"   />
		<result property="orderNum"  column="order_num"   />
		<result property="leader"    column="leader"      />
	</resultMap>

	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.user_type, u.email, u.avatar, u.phonenumber
               , u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time
               , u.remark, u.certificate_type,u.certificate_no,u.ext_properties,u.update_pwd_flag,u.last_login_org_id,u.last_login_role,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="SysUserPageReq" resultMap="SysUserExtendResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber
		     , u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time
		     , u.remark,u.certificate_type,u.certificate_no,u.ext_properties
			 , d.parent_id, d.ancestors, d.order_num, d.dept_name, d.leader, d.phone
			 , e.extend_str1, e.extend_str2, e.extend_str3, e.extend_str4, e.extend_str5, e.extend_str6
			 , e.extend_str7, e.extend_str8, e.extend_str9, e.extend_str10, e.extend_int1, e.extend_int2
		     , e.extend_int3, e.extend_double1, e.extend_double2, e.extend_double3, e.extend_datetime1, e.extend_datetime2
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_user_extend e on u.user_id = e.id
		where 1=1
		<if test="delFlag != null and delFlag != ''">
			AND u.del_flag = #{delFlag}
		</if>
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userIdList != null and userIdList.size > 0">
			AND u.user_id in
			<foreach collection="userIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="userIdListNo != null and userIdListNo.size > 0">
			AND u.user_id not in
			<foreach collection="userIdListNo" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="beginTimeDate != null"><!-- 开始时间检索 -->
			AND u.create_time &gt;= #{beginTimeDate}
		</if>
		<if test="endTimeDate != null"><!-- 结束时间检索 -->
			AND u.create_time &lt;= #{endTimeDate}
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId}
			OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE  ancestors like CONCAT('%,',#{deptId},'%') ))
		</if>
		<if test="roleId != null and roleId != 0">
			AND u.user_id in (SELECT ur.user_id FROM sys_user_role ur WHERE ur.role_id = #{roleId})
		</if>
		<if test="extendStr1 != null and extendStr1 != ''">
			AND e.extend_str1 = #{extendStr1}
		</if>
		<if test="extendStr2 != null and extendStr2 != ''">
			AND e.extend_str2 = #{extendStr2}
		</if>
		<if test="extendStr3 != null and extendStr3 != ''">
			AND e.extend_str3 = #{extendStr3}
		</if>
		<if test="extendStr4 != null and extendStr4 != ''">
			AND e.extend_str4 like concat('%', #{extendStr4}, '%')
		</if>
		<if test="extendStr5 != null and extendStr5 != ''">
			AND e.extend_str5 like concat('%', #{extendStr5}, '%')
		</if>
		<if test="extendStr6 != null and extendStr6 != ''">
			AND e.extend_str6 like concat('%', #{extendStr6}, '%')
		</if>
		<if test="extendStr7s != null and extendStr7s.length > 0">
			AND (
			<foreach collection="extendStr7s" index="index" item="item" open="" separator="or" close="">
				e.extend_str7 like concat('%', #{item}, '%')
			</foreach>
			)
		</if>
		<if test="extendInt1 != null">
			AND e.extend_int1 = #{extendInt1}
		</if>
		<if test="extendInt2 != null">
			AND e.extend_int2 = #{extendInt2}
		</if>
		<if test="extendInt3 != null">
			AND e.extend_int3 = #{extendInt3}
		</if>
		<if test="extendDouble1 != null">
			AND e.extend_double1 = #{extendDouble1}
		</if>
		<if test="extendDouble2 != null">
			AND e.extend_double2 = #{extendDouble2}
		</if>
		<if test="extendDouble3 != null">
			AND e.extend_double3 = #{extendDouble3}
		</if>

		<if test="orgIds != null and orgIds.length > 0">
			and u.user_id in (select user_id from sys_user_org where org_id in
			<foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<if test="extendDatetime1Begin != null and extendDatetime1End != null and extendDatetime1Begin != '' and extendDatetime1End != ''">
			AND e.extend_datetime1 between #{extendDatetime1Begin} and #{extendDatetime1End}
		</if>
		<if test="extendDatetime1 != null and extendDatetime1 != ''">
			AND e.extend_datetime1 between concat(#{extendDatetime1},' 00:00:00') and concat(#{extendDatetime1},' 23:59:59')
		</if>
		<if test="guCode != null and guCode != ''">
			and u.user_id in (select user_id from sys_user_org where org_id in
				(select id from sys_org where gu_pcode_path like concat('%', #{guCode}, '%'))
			)
		</if>
	</select>
	
	<select id="selectAllocatedList" parameterType="SysUserPageReq" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber
	                  , u.status, u.create_time
	    from sys_user u
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
	</select>
	
	<select id="selectUnallocatedList" parameterType="SysUserPageReq" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status,u.certificate_type,u.certificate_no,u.ext_properties, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>

	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>

	<select id="listByRoleCodeAndGuCode" resultType="SysUserResp">
		select a.*, c.gu_region_level  from sys_user a left join sys_user_org b on a.user_id = b.user_id
													   left join sys_org c on b.org_id = c.id
													   left join sys_user_org d on a.user_id = d.user_id
													   left join sys_user_role e on a.user_id = e.user_id
		<where>
			and a.del_flag='0'
			<if test="pcodeList != null and pcodeList.size > 0">
				and c.gu_code in
				<foreach collection="pcodeList" item="code" open="(" separator="," close=")">
					#{code}
				</foreach>
			</if>
			<if test="roleId != null">
				and e.role_id = #{roleId}
			</if>
		</where>
	</select>

	<select id="listUserDept" parameterType="String" resultMap="SysUserDeptResult">
		select u.user_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber
		, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time
		, u.remark,u.certificate_type,u.certificate_no,u.ext_properties
		, d.dept_id, d.parent_id, d.ancestors, d.order_num, d.dept_name, d.leader, d.phone, d.email
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null">
			AND u.user_id = #{userId}
		</if>
		<if test="updateDate != null"><!-- 结束时间检索 -->
			AND u.update_time &gt;= #{updateDate}
		</if>
	</select>
</mapper> 