<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.platform</groupId>
        <artifactId>platform-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>platform-system</artifactId>
<!--    <packaging>jar</packaging>-->
    <description>
        platform-system系统模块
    </description>
	
    <dependencies>
    	
    	<!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    	<!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!--验证码 -->
        <dependency>
            <groupId>pro.fessional</groupId>
            <artifactId>kaptcha</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- mybatisPlus模块 -->
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-common-starter-mybatisPlus</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- platform Common DataSource -->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-datasource</artifactId>
        </dependency>

        <!-- platform Common DataScope -->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-datascope</artifactId>
        </dependency>

        <!-- platform Common Log -->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-log</artifactId>
        </dependency>

        <!-- platform Common desensitization -->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-desensitization</artifactId>
        </dependency>

        <!-- platform Common Swagger -->
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-common-starter-swagger</artifactId>
        </dependency>
        <!-- 接口模块 -->
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-common-starter-share</artifactId>
        </dependency>
        <!-- 工具模块 -->
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-common-starter-toolbox</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>message-center-service-api</artifactId>
            <version>${platform.message.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-common-starter-file</artifactId>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>${pinyin4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-api-syndata</artifactId>
        </dependency>
    </dependencies>

    <build>

        <finalName>${project.artifactId}</finalName>
        <plugins>
<!--            &lt;!&ndash; maven打包，可供其他系统直接引用，确保controller层接口直接使用，packaging修改为jar &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <source>${java.version}</source>-->
<!--                    <target>${java.version}</target>-->
<!--                    <encoding>${project.build.sourceEncoding}</encoding>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <!-- 如果不添加此节点mybatis的mapper.xml文件都会被漏掉。 -->
<!--        <resources>-->
<!--            <resource>-->
<!--                <directory>src/main/resources</directory>-->
<!--                <includes>-->
<!--                    <include>**/*.xml</include>-->
<!--                </includes>-->
<!--                <filtering>false</filtering>-->
<!--            </resource>-->
<!--        </resources>-->
    </build>
   
</project>