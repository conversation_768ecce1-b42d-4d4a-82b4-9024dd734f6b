package com.platform.gen.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlCreateTableStatement;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.common.starter.toolbox.sql.SqlUtil;
import com.platform.gen.domain.req.CreateTableReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ctdi.common.starter.toolbox.text.Convert;
import com.ctdi.base.biz.controller.BaseController;
import com.platform.common.log.annotation.Log;
import com.platform.common.log.enums.BusinessType;
import com.platform.common.security.annotation.RequiresPermissions;
import com.platform.gen.domain.GenTable;
import com.platform.gen.domain.GenTableColumn;
import com.platform.gen.service.IGenTableColumnService;
import com.platform.gen.service.IGenTableService;

/**
 * 代码生成 操作处理
 * 
 * <AUTHOR>
 */
@Api(tags = "代码生成控制器")
@RequestMapping("/gen")
@RestController
public class GenController extends BaseController
{
    @Autowired
    private IGenTableService genTableService;

    @Autowired
    private IGenTableColumnService genTableColumnService;

    /**
     * 查询代码生成列表
     */
    @ApiOperation(value = "查询代码生成列表",  notes = "代码生成")
    @RequiresPermissions("tool:gen:list")
    @GetMapping("/list")
    public ResponseResult<List<GenTable>> genList(GenTable genTable)
    {
        startPage();
        List<GenTable> list = genTableService.selectGenTableList(genTable);
        return ResponseResult.ok(list);
    }

    @Log(title = "创建表", businessType = BusinessType.OTHER)
    @PostMapping("/createTable")
    public ResponseResult createTableSave(@RequestBody CreateTableReq createTableReq) {
        try{
            String sql = createTableReq.getSql();
            SqlUtil.filterKeyword(sql);
            List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sql, DbType.mysql);
            List<String> tableNames = new ArrayList<>();
            for (SQLStatement sqlStatement : sqlStatements)
            {
                if (sqlStatement instanceof MySqlCreateTableStatement)
                {
                    MySqlCreateTableStatement createTableStatement = (MySqlCreateTableStatement) sqlStatement;
                    if (genTableService.createTable(createTableStatement.toString()))
                    {
                        String tableName = createTableStatement.getTableName().replaceAll("`", "");
                        tableNames.add(tableName);
                    }
                }
            }
            List<GenTable> tableList = genTableService.selectDbTableListByNames(tableNames.toArray(new String[tableNames.size()]));
            genTableService.importGenTable(tableList);
            return ResponseResult.ok();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return ResponseResult.fail("创建表结构异常");
        }
    }

    /**
     * 修改代码生成业务
     */
    @ApiOperation(value = "修改代码生成业务",  notes = "代码生成")
    @RequiresPermissions("tool:gen:query")
    @GetMapping(value = "/{tableId}")
    public ResponseResult getInfo(@PathVariable Long tableId)
    {
        GenTable table = genTableService.selectGenTableById(tableId);
        List<GenTable> tables = genTableService.selectGenTableAll();
        List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("info", table);
        map.put("rows", list);
        map.put("tables", tables);
        return ResponseResult.ok(map);
    }

    /**
     * 查询数据库列表
     */
    @ApiOperation(value = "查询数据库列表",  notes = "代码生成")
    @RequiresPermissions("tool:gen:list")
    @GetMapping("/db/list")
    public ResponseResult<List<GenTable>> dataList(GenTable genTable)
    {
        startPage();
        List<GenTable> list = genTableService.selectDbTableList(genTable);
        return ResponseResult.ok(list);
    }

    /**
     * 查询数据表字段列表
     */
    @ApiOperation(value = "查询数据表字段列表",  notes = "代码生成")
    @GetMapping(value = "/column/{tableId}")
    public ResponseResult<List<GenTableColumn>> columnList(Long tableId)
    {
        List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
        return ResponseResult.ok(list);
    }

    /**
     * 导入表结构（保存）
     */
    @ApiOperation(value = "导入表结构（保存）",  notes = "代码生成")
    @RequiresPermissions("tool:gen:import")
    @Log(title = "代码生成", businessType = BusinessType.IMPORT)
    @PostMapping("/importTable")
    public ResponseResult importTableSave(String tables)
    {
        String[] tableNames = Convert.toStrArray(tables);
        // 查询表信息
        List<GenTable> tableList = genTableService.selectDbTableListByNames(tableNames);
        genTableService.importGenTable(tableList);
        return ResponseResult.ok();
    }

    /**
     * 修改保存代码生成业务
     */
    @ApiOperation(value = "修改保存代码生成业务",  notes = "代码生成")
    @RequiresPermissions("tool:gen:edit")
    @Log(title = "代码生成", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseResult editSave(@Validated @RequestBody GenTable genTable)
    {
        genTableService.validateEdit(genTable);
        genTableService.updateGenTable(genTable);
        return ResponseResult.ok();
    }

    /**
     * 删除代码生成
     */
    @ApiOperation(value = "删除代码生成",  notes = "代码生成")
    @RequiresPermissions("tool:gen:remove")
    @Log(title = "代码生成", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tableIds}")
    public ResponseResult remove(@PathVariable Long[] tableIds)
    {
        genTableService.deleteGenTableByIds(tableIds);
        return ResponseResult.ok();
    }

    /**
     * 预览代码
     */
    @ApiOperation(value = "预览代码",  notes = "代码生成")
    @RequiresPermissions("tool:gen:preview")
    @GetMapping("/preview/{tableId}")
    public ResponseResult preview(@PathVariable("tableId") Long tableId) throws IOException
    {
        Map<String, String> dataMap = genTableService.previewCode(tableId);
        return ResponseResult.ok(dataMap);
    }

    /**
     * 生成代码（下载方式）
     */
    @ApiOperation(value = "生成代码（下载方式）",  notes = "代码生成")
    @RequiresPermissions("tool:gen:code")
    @Log(title = "代码生成", businessType = BusinessType.GENCODE)
    @GetMapping("/download/{tableName}")
    public void download(HttpServletResponse response, @PathVariable("tableName") String tableName) throws IOException
    {
        byte[] data = genTableService.downloadCode(tableName);
        genCode(response, data);
    }

    /**
     * 生成代码（自定义路径）
     */
    @ApiOperation(value = "生成代码（自定义路径）",  notes = "代码生成")
    @RequiresPermissions("tool:gen:code")
    @Log(title = "代码生成", businessType = BusinessType.GENCODE)
    @GetMapping("/genCode/{tableName}")
    public ResponseResult genCode(@PathVariable("tableName") String tableName)
    {
        genTableService.generatorCode(tableName);
        return ResponseResult.ok();
    }

    /**
     * 同步数据库
     */
    @ApiOperation(value = "同步数据库",  notes = "代码生成")
    @RequiresPermissions("tool:gen:edit")
    @Log(title = "代码生成", businessType = BusinessType.UPDATE)
    @GetMapping("/synchDb/{tableName}")
    public ResponseResult synchDb(@PathVariable("tableName") String tableName)
    {
        genTableService.synchDb(tableName);
        return ResponseResult.ok();
    }

    /**
     * 批量生成代码
     */
    @ApiOperation(value = "批量生成代码",  notes = "代码生成")
    @RequiresPermissions("tool:gen:code")
    @Log(title = "代码生成", businessType = BusinessType.GENCODE)
    @GetMapping("/batchGenCode")
    public void batchGenCode(HttpServletResponse response, String tables) throws IOException
    {
        String[] tableNames = Convert.toStrArray(tables);
        byte[] data = genTableService.downloadCode(tableNames);
        genCode(response, data);
    }

    /**
     * 生成zip文件
     */
    private void genCode(HttpServletResponse response, byte[] data) throws IOException
    {
        response.reset();
        response.setHeader("Content-Disposition", "attachment; filename=\"platform.zip\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }
}
