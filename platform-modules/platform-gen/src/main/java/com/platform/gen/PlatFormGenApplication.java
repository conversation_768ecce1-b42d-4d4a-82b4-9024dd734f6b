package com.platform.gen;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.platform.common.security.annotation.EnableCustomConfig;
import com.platform.common.security.annotation.EnablePlatformFeignClients;
import com.ctdi.common.starter.swagger.annotation.EnableCustomSwagger2;

/**
 * 代码生成
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnablePlatformFeignClients
@SpringBootApplication
public class PlatFormGenApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(PlatFormGenApplication.class, args);
        System.out.println("代码服务启动成功");
    }
}
