# Spring
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: ${spring.profiles.active}
        group: ${nacos.group:DEFAULT_GROUP}
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 命名空间
        namespace: ${spring.profiles.active}
        group: ${nacos.group:DEFAULT_GROUP}
        # 共享配置
        shared-configs:
          - { dataId: common-application.yml,refresh: true }
          - { dataId: common-datasource.yml,refresh: true }
          - { dataId: common-cache.yml,refresh: true }
