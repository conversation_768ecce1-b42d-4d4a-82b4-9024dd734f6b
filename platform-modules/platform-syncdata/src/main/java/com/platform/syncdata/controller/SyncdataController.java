package com.platform.syncdata.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.syncdata.service.SyncdataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 同步数据日志控制器
 * @author: tr
 * @date: 2025年03月26日 19:21
 */
@RestController
@RequestMapping("/syncdata")
public class SyncdataController {

    @Autowired
    private SyncdataService syncdataService;

    /**
     * @Description: 接收同步数据消息，并下发至消息中间件中
     * @author: tr
     * @Date: 2025/3/26 19:23
     * @param: []
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult
     */
    @GetMapping("receiveMsg")
    public ResponseResult receiveMsg(String batchNumber){
        syncdataService.receiveMsg(batchNumber);
        return ResponseResult.ok();
    }
}
