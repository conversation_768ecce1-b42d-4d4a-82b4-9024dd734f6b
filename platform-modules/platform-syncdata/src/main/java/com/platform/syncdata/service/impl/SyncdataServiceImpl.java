package com.platform.syncdata.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.core.exception.ServiceException;
import com.platform.common.core.enums.DelFlagEnum;
import com.platform.common.core.enums.SyncStatusEnum;
import com.platform.common.core.enums.SyncTypeEnum;
import com.platform.syncdata.convert.SyncdataConvert;
import com.platform.syncdata.domain.dto.MsgResult;
import com.platform.syncdata.service.SyncdataService;
import com.platform.system.api.RemoteDeptService;
import com.platform.system.api.RemoteSyncdataLogService;
import com.platform.system.api.RemoteUserService;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.request.SysSyncdataLogReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.api.domain.response.SysSyncdataLogResp;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.api.domain.response.openapi.DeptV3Resp;
import com.platform.system.api.domain.response.openapi.UserV3Resp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 同步数据服务实现类
 * @author: tr
 * @date: 2025年03月26日 19:25
 */
@Service
public class SyncdataServiceImpl implements SyncdataService {

    @Autowired
    private RemoteSyncdataLogService remoteSyncdataLogService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteDeptService remoteDeptService;
    @Autowired
    private StreamBridge streamBridge;

    @Override
    @Async
    public void receiveMsg(String batchNumber) {
        ResponseResult<List<SysSyncdataLogResp>> responseResult = remoteSyncdataLogService.listStayByBatchNumber(batchNumber);
        if (responseResult.getCode() != ResponseResult.SUCCESS){
            throw new ServiceException("数据同步失败，请稍后再试！");
        }
        List<SysSyncdataLogResp> list = responseResult.getData();
        String data = null;
        String syncType = null;
        List<String> dataIdList = new ArrayList<>();
        for (SysSyncdataLogResp sysSyncdataLogResp : list){
            syncType = sysSyncdataLogResp.getSyncType();
            dataIdList.add(sysSyncdataLogResp.getDataId());
        }

        if (StrUtil.equals(syncType, SyncTypeEnum.USER.getCode())){
            data = syncUser(dataIdList);
        }else if (StrUtil.equals(syncType, SyncTypeEnum.DEPT.getCode())){
            data = syncDept(dataIdList);
        }
        if (data != null){
            boolean flag = streamBridge.send("platform-send-bizdata-output", data);
            if (flag){
                SysSyncdataLogReq sysSyncdataLogReq = new SysSyncdataLogReq();
                sysSyncdataLogReq.setBatchNumber(batchNumber);
                sysSyncdataLogReq.setSyncStatus(SyncStatusEnum.ISSUED.getCode());
                remoteSyncdataLogService.updateSyncStatusByBatchNumber(sysSyncdataLogReq);
            }
        }
    }

    /**
     * @Description: 同步用户数据，查询待同步的用户数据并返回JSON字符串
     * @author: tr
     * @Date: 2025/3/26 20:46
     * @param: [userIdList]
     * @returnValue: java.lang.String
     */
    private String syncUser(List<String> userIdList){
        List<Long> longList = userIdList.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        ResponseResult<List<SysUserResp>> responseResult = remoteUserService.listByIdList(longList.toArray(new Long[0]));
        if (responseResult.getCode() != ResponseResult.SUCCESS){
            throw new ServiceException("查询用户信息，请稍后再试！");
        }
        List<SysUserResp> list = responseResult.getData();
        List<UserV3Resp> userV3RespList = SyncdataConvert.INSTANCE.respToUserRespOpenApi(list);
        MsgResult<List<UserV3Resp>> msgResult = new MsgResult();
        msgResult.setSyncType(SyncTypeEnum.USER.getCode());
        msgResult.setData(userV3RespList);
        return JSON.toJSONString(msgResult);
    }

    /**
     * @Description: 同步部门数据，查询待同步的部门数据并返回JSON字符串
     * @author: tr
     * @Date: 2025/3/26 20:47
     * @param: [deptIdList]
     * @returnValue: java.lang.String
     */
    private String syncDept(List<String> deptIdList){
        List<Long> longList = deptIdList.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        SysDeptQueryReq sysDeptQueryReq = new SysDeptQueryReq();
        //设置标识，查询时不带上删除标识条件
        sysDeptQueryReq.setDelFlag(DelFlagEnum.NO_DELETED.getCode());
        sysDeptQueryReq.setDeptIdList(longList);
        ResponseResult<List<SysDeptResp>> responseResult = remoteDeptService.list(sysDeptQueryReq);
        if (responseResult.getCode() != ResponseResult.SUCCESS){
            throw new ServiceException("查询部门信息，请稍后再试！");
        }
        List<SysDeptResp> list = responseResult.getData();
        List<DeptV3Resp> deptV3RespList = SyncdataConvert.INSTANCE.respToDeptRespOpenApi(list);
        MsgResult<List<DeptV3Resp>> msgResult = new MsgResult();
        msgResult.setSyncType(SyncTypeEnum.DEPT.getCode());
        msgResult.setData(deptV3RespList);
        return JSON.toJSONString(msgResult);
    }
}
