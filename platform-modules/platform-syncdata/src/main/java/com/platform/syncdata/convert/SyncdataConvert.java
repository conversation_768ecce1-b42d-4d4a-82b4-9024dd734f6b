package com.platform.syncdata.convert;

import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.api.domain.response.SysUserResp;
import com.platform.system.api.domain.response.openapi.DeptV3Resp;
import com.platform.system.api.domain.response.openapi.UserV3Resp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @author: tr
 * @date: 2025年03月27日 9:03
 */
@Mapper
public interface SyncdataConvert {

    SyncdataConvert INSTANCE = Mappers.getMapper(SyncdataConvert.class);

    List<UserV3Resp> respToUserRespOpenApi(List<SysUserResp> sysUserRespList);

    List<DeptV3Resp> respToDeptRespOpenApi(List<SysDeptResp> sysUserRespList);
}
