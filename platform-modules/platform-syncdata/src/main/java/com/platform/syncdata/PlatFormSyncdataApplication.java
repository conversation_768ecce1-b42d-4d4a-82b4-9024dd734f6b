package com.platform.syncdata;

import com.platform.common.security.annotation.EnableCustomConfig;
import com.platform.common.security.annotation.EnablePlatformFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 同步数据模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnablePlatformFeignClients(basePackages = {"com.platform"})
@SpringBootApplication
public class PlatFormSyncdataApplication
{

    public static void main(String[] args)
    {
        SpringApplication.run(PlatFormSyncdataApplication.class, args);
        System.out.println("同步数据模块启动成功");
    }
}
