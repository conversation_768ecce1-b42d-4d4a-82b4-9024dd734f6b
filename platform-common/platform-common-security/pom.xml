<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.platform</groupId>
        <artifactId>platform-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>platform-common-security</artifactId>

    <description>
        platform-common-security安全模块
    </description>

    <dependencies>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- 缓存引用，根据配置文件的方式灵活引用redis或ctgcache -->
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-common-starter-cache</artifactId>
        </dependency>

        <!-- platform Api System -->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-toolbox</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>

    </dependencies>

</project>
