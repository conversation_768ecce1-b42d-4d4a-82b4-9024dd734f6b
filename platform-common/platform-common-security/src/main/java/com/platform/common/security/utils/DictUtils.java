package com.platform.common.security.utils;

import java.util.Collection;
import java.util.List;
import com.common.middleware.cache.CacheClient;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.system.api.domain.SysDictData;
import com.platform.system.api.model.SysDictDataDTO;

/**
 * 字典工具类
 * 
 * <AUTHOR>
 */
public class DictUtils
{
    /**
     * 设置字典缓存
     * 
     * @param key 参数键
     * @param dictDatas 字典数据列表
     */
    public static void setDictCache(String key, List<SysDictDataDTO> dictDatas)
    {
        SpringUtils.getBean(CacheClient.class).setCacheObject(getCacheKey(key), dictDatas);
    }

    /**
     * 获取字典缓存
     * 
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<SysDictDataDTO> getDictCache(String key)
    {
        List<SysDictDataDTO> list = SpringUtils.getBean(CacheClient.class).getCacheObject(getCacheKey(key));
        if (StringUtils.isNotNull(list))
        {
            return list;
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     * 
     * @param key 字典键
     */
    public static void removeDictCache(String key)
    {
        SpringUtils.getBean(CacheClient.class).deleteObject(getCacheKey(key));
    }

    /**
     * 清空字典缓存
     */
    public static void clearDictCache()
    {
        Collection<String> keys = SpringUtils.getBean(CacheClient.class).keys(CacheConstants.SYS_DICT_KEY + "*");
        SpringUtils.getBean(CacheClient.class).deleteObject(keys);
    }

    /**
     * 设置cache key
     * 
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey)
    {
        return CacheConstants.SYS_DICT_KEY + configKey;
    }
}
