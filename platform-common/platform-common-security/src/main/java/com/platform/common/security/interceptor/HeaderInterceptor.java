package com.platform.common.security.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.utils.JwtUtils;
import com.platform.common.security.annotation.RequiresNoLogin;
import com.platform.common.security.config.properties.IgnoreWhiteProperties;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.domain.response.SysApiResp;
import com.platform.system.api.model.SysApiDTO;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.PatternMatchUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import com.platform.common.security.context.SecurityContextHolder;
import com.ctdi.common.starter.toolbox.utils.ServletUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.model.LoginUser;

import java.lang.reflect.Method;
import java.util.List;


/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HeaderInterceptor implements AsyncHandlerInterceptor
{

    /** 开启权限拦截标识，默认为开启状态-true，微服务版本不需要开启，通过网关进行权限拦截 **/
    @Value("${openPermissionsInterceptFlag:true}")
    private boolean openPermissionsInterceptFlag;

    public CacheClient redisService = SpringUtils.getBean(CacheClient.class);

    public IgnoreWhiteProperties ignoreWhite = SpringUtils.getBean(IgnoreWhiteProperties.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        String url = ServletUtils.getRequest().getRequestURI();
        if (!(handler instanceof HandlerMethod))
        {
            return true;
        }
        HandlerMethod hm = (HandlerMethod) handler;
        Method method = hm.getMethod();
        //该方法是否添加 允许未登录访问注解
        if (method !=null && method.isAnnotationPresent(RequiresNoLogin.class)) {
            // 不拦截
            return true;
        }
        //gateway在请求头中封装了USER_ID,USER_NAME,USER_KEY
        SecurityContextHolder.setUserId(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID));
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));

        String token = SecurityUtils.getToken();

        if (StrUtil.isBlank(token)){
            String source = ServletUtils.getRequest().getHeader(SecurityConstants.FROM_SOURCE);

            // 内部请求验证
            if (StrUtil.equals(SecurityConstants.INNER, source) && StringUtils.matches(url, ignoreWhite.getWhites()))
            {
                //内部请求，并且符合路径，则直接跳过
                return true;
            }else{
                throw new ServiceException(ExceptionEnum.TOKEN_NULL.getMsg(), ExceptionEnum.TOKEN_NULL.getCode());
            }
        }
        LoginUser loginUser = TokenUtils.checkToken(token);

        Claims claims = JwtUtils.parseToken(token);
        String userkey = JwtUtils.getUserKey(claims);
        String userId = JwtUtils.getUserId(claims);
        String source = ServletUtils.getRequest().getHeader(SecurityConstants.FROM_SOURCE);
        if (!StrUtil.equals(source, SecurityConstants.INNER) && StringUtils.isNotNull(loginUser))
        {
            if (openPermissionsInterceptFlag){
                //校验用户是否拥有该路径的访问权限
                String apiStr = redisService.getCacheObject(CacheConstants.SYSTEM_API);
                List<SysApiResp> apiList = JSONUtil.toList(apiStr, SysApiResp.class);
                //判断当前请求路径是否配置在API权限中，如配置了，则进行权限过滤校验
                boolean flagApi = apiList.stream()
                        .anyMatch(x -> PatternMatchUtils.simpleMatch(x.getPerms(), url));
                if (flagApi){
                    //配置了权限校验
                    //获取当前用户信息
                    List<SysApiDTO> permissionList = loginUser.getPermissions();
                    String requestMethod = ServletUtils.getRequest().getMethod();
                    if (!hasPermi(permissionList, url, requestMethod)){
                        log.info("API接口地址[{}]无访问权限", url);
                        //当前用户没有访问权限
                        throw new ServiceException(ExceptionEnum.NO_PERMISSION.getMsg(), ExceptionEnum.NO_PERMISSION.getCode());
                    }
                }
            }

            SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);

            if (ObjectUtil.isEmpty(SecurityContextHolder.getUserId()) || SecurityContextHolder.getUserId() == 0L){
                SecurityContextHolder.setUserId(userId);
            }
            if (StrUtil.isBlank(SecurityContextHolder.getUserName())){
                SecurityContextHolder.setUserName(loginUser.getUserName());
            }
            if (StrUtil.isBlank(SecurityContextHolder.getUserKey())){
                SecurityContextHolder.setUserKey(userkey);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
        SecurityContextHolder.remove();
    }

    /**
     * @Description: 判断是否拥有此权限
     * @author: tr
     * @Date: 2024/4/7 18:23
     * @param: [authorities, permission]
     * @returnValue: boolean
     */
    private boolean hasPermi(List<SysApiDTO> authorities, String url, String requestMethod)
    {
        //判断接口的请求方式是否一致，同时，判断接口的url是否一致
        return authorities.stream()
                .anyMatch(x -> StrUtil.equals(x.getRequestMethod(), requestMethod) &&
                        PatternMatchUtils.simpleMatch(x.getPerms(), url));
    }
}
