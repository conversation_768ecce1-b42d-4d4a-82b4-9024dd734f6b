package com.platform.common.security.config;

import java.util.TimeZone;

import com.platform.common.security.feign.FeignRequestInterceptor;
import com.platform.common.security.filter.RequestBodyCacheFilter;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
public class ApplicationConfig
{
    /**
     * 时区配置
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization()
    {
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder.timeZone(TimeZone.getDefault());
    }

    @Bean
    public FeignRequestInterceptor getFeignRequestInterceptor(){
        return new FeignRequestInterceptor();
    }

    @Bean
    public FilterRegistrationBean webAuthFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RequestBodyCacheFilter());
        registration.setName("requestBodyCacheFilter");
        registration.addUrlPatterns("/openapi/*");
        return registration;
    }

}
