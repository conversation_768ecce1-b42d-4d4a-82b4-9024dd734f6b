package com.platform.common.security.config;

import com.platform.common.security.interceptor.OpenApiCheckSignInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.platform.common.security.interceptor.HeaderInterceptor;

/**
 * 拦截器配置
 *
 * <AUTHOR>
 */
public class WebMvcConfig implements WebMvcConfigurer
{

    @Value("${jwt.antMatchers}")
    private String excludePath;

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        String[] excludeUrls = excludePath.split(",");
        registry.addInterceptor(getHeaderInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(excludeUrls)
                .order(-10);
        registry.addInterceptor(getOpenApiCheckSignInterceptor())
                .addPathPatterns("/openapi/**")
                .order(-10);
    }

    /**
     * 自定义请求头拦截器
     */
    public HeaderInterceptor getHeaderInterceptor()
    {
        return new HeaderInterceptor();
    }

    public OpenApiCheckSignInterceptor getOpenApiCheckSignInterceptor()
    {
        return new OpenApiCheckSignInterceptor();
    }

}
