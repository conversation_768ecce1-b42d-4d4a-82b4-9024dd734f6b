package com.platform.common.security.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.security.filter.RequestBodyCacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Component
@Slf4j
public class OpenApiCheckSignInterceptor implements AsyncHandlerInterceptor {

    public CacheClient redisService = SpringUtils.getBean(CacheClient.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String code = request.getHeader("code");
        String messageChannelId = request.getHeader("messageChannelId");
        String appKey = request.getHeader("appKey");
        if (StrUtil.isNotBlank(code)){
            appKey = code;
        }
        if (StrUtil.isNotBlank(messageChannelId)){
            appKey = messageChannelId;
        }

        if(StringUtils.isEmpty(appKey)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_APP_KEY_NULL.getMsg(), ExceptionEnum.CHECK_SIGN_APP_KEY_NULL.getCode());
        }
        String nonceStr = request.getHeader("nonceStr");
        if(StringUtils.isEmpty(nonceStr)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_NONCE_STR_NULL.getMsg(), ExceptionEnum.CHECK_SIGN_NONCE_STR_NULL.getCode());
        }
        String timestamp = request.getHeader("timestamp");
        if(!StringUtils.isNumeric(timestamp)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_TIMESTAMP_NULL.getMsg(), ExceptionEnum.CHECK_SIGN_TIMESTAMP_NULL.getCode());
        }
        String sign = request.getHeader("sign");
        if(StringUtils.isEmpty(sign)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_SIGN_NULL.getMsg(), ExceptionEnum.CHECK_SIGN_SIGN_NULL.getCode());
        }

        Map<String, String> appKeyPair = redisService.getCacheMap(CacheConstants.APP_KEY_PAIR_MAP);
        if(!appKeyPair.containsKey(appKey)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_APP_KEY_NOT_EXIST.getMsg(), ExceptionEnum.CHECK_SIGN_APP_KEY_NOT_EXIST.getCode());
        }
        String appSecret = appKeyPair.get(appKey);


        if(System.currentTimeMillis() - Long.parseLong(timestamp) > SecurityConstants.CHECK_SIGN_TIMESTAMP_EXPIRES){
            //五分钟过期
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_TIMESTAMP_EXPIRES.getMsg(), ExceptionEnum.CHECK_SIGN_TIMESTAMP_EXPIRES.getCode());
        }

        Map<String, Object> parameterMap = new HashMap<>();
        RequestBodyCacheWrapper wrapper = new RequestBodyCacheWrapper(request);
        if (HttpMethod.GET.matches(wrapper.getMethod())) {
            final Map<String, Object> paramData = new HashMap<>(16);
            request.getParameterMap().forEach((k, v) -> {
                if (v.length == 1) {
                    paramData.put(k, v[0]);
                } else {
                    paramData.put(k, v);
                }
            });
            parameterMap.putAll(paramData);
        }else{
            //获取请求参数
            parameterMap = JSON.parseObject(wrapper.getRequestBody());
        }


        String checkSign = genericPreSign(Long.parseLong(timestamp),nonceStr,parameterMap,appSecret);

        if(!sign.equals(checkSign)){
            throw new ServiceException(ExceptionEnum.CHECK_SIGN_FAILED.getMsg(), ExceptionEnum.CHECK_SIGN_FAILED.getCode());
        }

        return true;
    }


    public String genericPreSign(Long timestamp, String nonceStr, Map<String, Object> body, String appSecret) throws UnsupportedEncodingException {
        SortedMap<String, String> hash = new TreeMap<>();
        // 时间戳
        hash.put("timestamp", timestamp.toString());
        // 随机字符串
        hash.put("nonceStr", nonceStr);
        // 参数
        hash.put("body", JSON.toJSONString(sortMap(body)));
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : hash.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
                sb.append(k).append("=").append(URLEncoder.encode(v, "UTF-8")).append("&");
            }
        }
        sb.append("security=").append(appSecret);
        log.info("签名串：{}", StrUtil.replace(sb, "\r", "").replace("\n", ""));
        return getSignString(sb.toString());
    }
    public static String getSignString(String preString) {
        return DigestUtils.md5DigestAsHex(preString.getBytes()).toUpperCase();
    }
    private TreeMap<String, Object> sortMap(Map<String, Object> body) {
        TreeMap<String, Object> treeMap = new TreeMap<>(Optional.ofNullable(body).orElse(new HashMap<>(0)));
        treeMap.forEach((k, v) -> {
            if (v instanceof Map) {
                TreeMap value = sortMap(JSON.parseObject(JSON.toJSONString(v), Map.class));
                treeMap.put(k, value);
            }
        });
        return treeMap;
    }

    public static void main(String[] args) {
        String s = "1 2\"+\"\n\"+\"3 4 5\"+\"\n\"+\"2133131\"+\"\n\"+\"31";
        System.out.println(s);
        System.out.println();
        System.out.println(StrUtil.replace(s, "\r", "").replace("\n", ""));
    }
}
