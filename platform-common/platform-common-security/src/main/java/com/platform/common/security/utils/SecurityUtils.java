package com.platform.common.security.utils;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.common.core.enums.UserTypeEnum;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.model.SysUserDTO;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import com.platform.common.security.context.SecurityContextHolder;
import com.ctdi.common.starter.toolbox.utils.ServletUtils;
import com.platform.system.api.model.LoginUser;

/**
 * 权限获取工具类
 * 
 * <AUTHOR>
 */
public class SecurityUtils
{
    /**
     * 获取用户ID
     */
    public static Long getUserId()
    {
        Long userId = SecurityContextHolder.getUserId();
        if (ObjectUtil.isEmpty(userId)){
            userId = Long.valueOf(ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_USER_ID));
        }
        return userId;
    }

    /**
     * 获取用户名称
     */
    public static String getUsername()
    {
        return SecurityContextHolder.getUserName();
    }

    /**
     * 获取用户key
     */
    public static String getUserKey()
    {
        return SecurityContextHolder.getUserKey();
    }

    /** 获取用户类型 **/
    public static String getUserType(){
        LoginUser loginUser = getLoginUser();
        SysUserDTO sysUserDTO = loginUser.getSysUser();
        return sysUserDTO.getUserType();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginUser getLoginUser()
    {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        if (loginUser == null){
            loginUser = TokenUtils.getLoginUser(getToken());
            SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
        }

        return loginUser;
    }

    /**
     * 获取请求token
     */
    public static String getToken()
    {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request)
    {
        return TokenUtils.getToken(request);
    }

    /**
     * 是否为管理员
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(String userType)
    {
        return StrUtil.equals(userType, UserTypeEnum.SYSTEM_USER.getCode());
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
