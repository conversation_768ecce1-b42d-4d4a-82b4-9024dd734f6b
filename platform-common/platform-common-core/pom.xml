<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.platform</groupId>
        <artifactId>platform-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>platform-common-core</artifactId>
    
    <description>
        platform-common-core核心模块
    </description>

    <dependencies>
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-base-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctdi</groupId>
            <artifactId>ctdi-base-biz</artifactId>
        </dependency>

        <!--离线IP地址包 -->
        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
        </dependency>

    </dependencies>

</project>
