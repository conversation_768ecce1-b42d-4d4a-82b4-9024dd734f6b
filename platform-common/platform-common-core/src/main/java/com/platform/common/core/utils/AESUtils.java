package com.platform.common.core.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Description:
 * @author: tr
 * @date: 2024年03月27日 10:17
 */
@Slf4j
public class AESUtils {

    /** 加密模式之 ECB，算法/模式/补码方式 */
    private static final String AES_ECB = "AES/ECB/PKCS5Padding";

    /**
     * @Description: AES_ECB加密方式，安全性低，速度快
     * @author: tr
     * @Date: 2024/3/27 10:25
     * @param text 待加密的内容
     * @param key 密钥
     * @returnValue: java.lang.String
     */
    public static String encrypt(String text, String key){
        if (StrUtil.isBlank(text) || StrUtil.isBlank(key)) {
            return null;
        }

        try {
            // 创建AES加密器
            Cipher cipher = Cipher.getInstance(AES_ECB);

            SecretKeySpec secretKeySpec = getSecretKeySpec(key);

            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);

            // 加密字节数组
            byte[] encryptedBytes = cipher.doFinal(getBytes(text));

            // 将密文转换为 Base64 编码字符串
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("AES-ECB加密报错：{}", e.toString());
        }
        return text;
    }

    /**
     * @Description: AES_ECB解密方式，安全性低，速度快
     * @author: tr
     * @Date: 2024/3/27 10:26
     * @param text 密文
     * @param key 密钥
     * @returnValue: java.lang.String
     */
    public static String decrypt(String text, String key){
        if (StrUtil.isBlank(text) || StrUtil.isBlank(key)) {
            return null;
        }

        // 将密文转换为16字节的字节数组
        byte[] textBytes = Base64.getDecoder().decode(text);

        try {
            // 创建AES加密器
            Cipher cipher = Cipher.getInstance(AES_ECB);

            SecretKeySpec secretKeySpec = getSecretKeySpec(key);

            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);

            // 解密字节数组
            byte[] decryptedBytes = cipher.doFinal(textBytes);

            // 将明文转换为字符串
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES-ECB解密报错：{}", e.toString());
        }
        return text;
    }

    /**
     * @Description: 将密钥字符串转换为SecretKeySpec对象
     * @author: tr
     * @Date: 2024/3/27 10:27
     * @param key 密钥字符串
     * @returnValue: javax.crypto.spec.SecretKeySpec
     */
    public static SecretKeySpec getSecretKeySpec(String key){
        SecretKeySpec secretKeySpec = new SecretKeySpec(getBytes(key), "AES");
        return secretKeySpec;
    }

    /**
     * @Description: 字符串转换为byte[]
     * @author: tr
     * @Date: 2024/3/27 10:27
     * @param str 字符串
     * @returnValue: byte[]
     */
    public static byte[] getBytes(String str){
        if (StrUtil.isBlank(str)) {
            return null;
        }
        try {
            return str.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
