package com.platform.common.core.enums;

import lombok.Getter;

/**
 * @Description: 同步状态枚举
 * @author: tr
 * @Date: 2025/3/26 19:13
 */
@Getter
public enum SyncStatusEnum {

    STAY(0, "待同步"),
    ISSUED(1, "已下发"),
    SYN_SUCCESS(9, "同步成功"),
    ISSUED_FAIL(8, "下发失败");

    private final Integer code;
    private final String info;

    SyncStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }
}
