package com.platform.common.core.enums;

/**
 * 消息状态
 * 
 * <AUTHOR>
 */
public enum MessageStatusEnum
{
    UNREAD("0", "未读"), READ("1", "已读");

    private final String code;
    private final String info;

    MessageStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
