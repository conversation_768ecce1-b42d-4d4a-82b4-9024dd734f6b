package com.platform.common.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 核心属性配置类
 * @author: tr
 * @date: 2024年08月20日 15:19
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "core.info")
public class CoreProperties {

    /** key **/
    private String key;

    /** 登录过期时间（分钟），默认60分钟 **/
    private Long expireTime = 60L;

    /** 登录缓存刷新时间（分钟） **/
    private Long refreshTime = 10L;

    /** 本地系统标识，默认true，适用于当前为本系统，则不会调用接口获取当前用户信息 **/
    private boolean localSystemFlag = true;

    /** 应用支撑平台同一账户唯一登录标识，默认false，表示不支持唯一登录 **/
    private boolean uniqueLoginFlag = false;

    /** 业务数据下发标识，将业务数据下发至消息中间件中，true-下发，false-不下发 **/
    private boolean bizdataIssuedFlag = false;

    private String pwdEncrypKey;
}
