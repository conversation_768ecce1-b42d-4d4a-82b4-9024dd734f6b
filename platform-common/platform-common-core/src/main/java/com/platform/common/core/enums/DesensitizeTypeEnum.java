package com.platform.common.core.enums;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;

public enum DesensitizeTypeEnum {

    ALL {
        @Override
        public String Desensitize(String source, Integer num) {
            StringBuilder builder = new StringBuilder();
            if (ObjectUtil.isNotEmpty(source)){
                for (int i = 0; i < source.length(); i++) {
                    builder.append('*');
                }
                return builder.toString();
            }
            return source;
        }
    },
    LEFT {
        @Override
        public String Desensitize(String source, Integer num) {
            StringBuilder builder = new StringBuilder();
            if (ObjectUtil.isNotEmpty(source)){

                if (source.length() < num){
                    return source;
                }

                for (int i = 0; i < source.length() - num; i++) {
                    builder.append('*');
                }
                return builder + source.substring(source.length() - num);
            }
            return source;
        }
    },
    RIGHT {
        @Override
        public String Desensitize(String source, Integer num) {
            StringBuilder builder = new StringBuilder();
            if (ObjectUtil.isNotEmpty(source)){

                if (source.length() <= num){
                    return source;
                }

                for (int i = 0; i < source.length() - num; i++) {
                    builder.append('*');
                }
                return source.substring(0, num) + builder;
            }
            return source;
        }
    };

    public abstract String Desensitize(String source, Integer num);
}
