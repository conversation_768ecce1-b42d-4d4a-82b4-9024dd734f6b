package com.platform.common.core.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstants
{

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 短信验证码 redis key
     */
    public static final String SMS_CODE_KEY = "sms_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 高并发接口参数前缀
     */
    public static final String RECEIVE_PARAMS_KEY = "receive_params:";

    /**
     * redis key 多个业务单词之间的分隔符
     */
    public static final String SEPARATOR_KEY = "_";

    /** api的redis缓存前缀 **/
    public static final String SYSTEM_API = "sys_api";

    /** 菜单的redis缓存前缀 **/
    public static final String SYSTEM_MENU = "sys_menu";

    /** 验签密钥对 Map<String,String> 缓存key **/
    public static final String APP_KEY_PAIR_MAP = "app:key_pair";

    /** 应用编码与应用对象对应的缓存key **/
    public static final String APP_CODE_OBJECT_MAP = "app:code:object";

    /** 应用编码与公钥对 缓存key **/
    public static final String APP_CODE_PUBLIC_KEY_MAP = "app:code:public_key";

    /**
     * 登录时加密的盐 redis key
     */
    public static final String LOGIN_PASSWORD_SALT = "security_salt:login:";

    /** 忘记密码加密的盐 redis key **/
    public static final String FORGET_PASSWORD_SALT = "security_salt:forget:";

    /**
     * 身份证和UUID redis key
     */
    public static final String CARD_UUID_KEY = "card:";

    /**
     * 手机号码和UUID redis key
     */
    public static final String PHONENUMBER_UUID_KEY = "phone:";

    /** 地址码父级下所有子级的前缀 **/
    public static final String COUNTRY_ADDRESS_PCODES = "COUNTRY_ADDRESS:PCODES:%s";


    /** 应用当日访问次数 **/
    public static final String APP_VISIT_COUNT = "APP_VISIT_COUNT:";

    /** 应用当日访问次数 **/
    public static final String SYS_ENC_RULE = "SYS_ENC_RULE:";
}
