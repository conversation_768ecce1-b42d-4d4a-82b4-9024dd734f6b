package com.platform.common.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @author: tr
 * @date: 2024年08月27日 16:37
 */
@Data
@Component
@Order(value = 1)
@ConfigurationProperties(prefix = "jwt")
public class JwtProperties {


    //HeaderKey
    public static String tokenHeader = "Authorization";

    //过期时间
    public static Integer expiration;

    public static Long wechatExpiration;

    //在缓存中最长时长
    public static Integer redisSurvivalDays;

    //jtw的过期时长单位天
    public static Long jwtSurvivalDays;

    //登录互斥方式
    public static String modelLimited;

    //配置白名单
    public static String[] antMatchers;

    //初始化密码
    public static String resetPasswords;

    //白名单+appid
    public static String[] whiteList;

    public static void setTokenHeader(String tokenHeader) {
        JwtProperties.tokenHeader = tokenHeader;
    }

    public static void setExpiration(Integer expiration) {
        JwtProperties.expiration = expiration;
    }

    public static void setWechatExpiration(Long wechatExpiration) {
        JwtProperties.wechatExpiration = wechatExpiration;
    }

    public static void setRedisSurvivalDays(Integer redisSurvivalDays) {
        JwtProperties.redisSurvivalDays = redisSurvivalDays;
    }

    public static void setJwtSurvivalDays(Long jwtSurvivalDays) {
        JwtProperties.jwtSurvivalDays = jwtSurvivalDays;
    }

    public static void setModelLimited(String modelLimited) {
        JwtProperties.modelLimited = modelLimited;
    }

    public static void setResetPasswords(String resetPasswords) {
        JwtProperties.resetPasswords = resetPasswords;
    }

    public static void setWhiteList(String whiteList) {
        JwtProperties.whiteList = whiteList.split(",");
    }

    public void setAntMatchers(String antMatchers) {
        JwtProperties.antMatchers = antMatchers.split(",");
    }
}
