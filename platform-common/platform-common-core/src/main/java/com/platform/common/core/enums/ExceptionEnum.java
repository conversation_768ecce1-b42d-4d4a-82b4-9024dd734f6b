package com.platform.common.core.enums;

import com.ctdi.common.starter.share.constant.HttpStatus;

/**
 * @Description: 应用支撑平台异常枚举类
 * @author: tr
 * @Date: 2024/2/6 14:36
 */
public enum ExceptionEnum {

    LOGIN_SUCCESS(HttpStatus.SUCCESS, "登录成功"),
    TOKEN_NULL(HttpStatus.UNAUTHORIZED, "令牌不能为空"),
    TOKEN_EXPIRE(HttpStatus.UNAUTHORIZED, "令牌已过期或验证不正确"),

    TOKEN_ERROR(HttpStatus.UNAUTHORIZED, "令牌解析失败"),
    LOGIN_EXPIRE(HttpStatus.UNAUTHORIZED, "登录状态已过期"),
    TOKEN_VERIFY_FAIL(HttpStatus.UNAUTHORIZED, "令牌验证失败"),
    CHECK_SIGN_APP_KEY_NULL(HttpStatus.UNAUTHORIZED, "密钥不能为空"),
    CHECK_SIGN_APP_KEY_NOT_EXIST(HttpStatus.UNAUTHORIZED, "密钥不存在"),
    CHECK_SIGN_NONCE_STR_NULL(HttpStatus.UNAUTHORIZED, "唯一标识不能为空"),
    CHECK_SIGN_TIMESTAMP_NULL(HttpStatus.UNAUTHORIZED, "时间戳不能为空"),
    CHECK_SIGN_TIMESTAMP_EXPIRES(HttpStatus.UNAUTHORIZED, "时间戳已过期"),
    CHECK_SIGN_SIGN_NULL(HttpStatus.UNAUTHORIZED, "签名不能为空"),
    CHECK_SIGN_FAILED(HttpStatus.UNAUTHORIZED, "验签失败"),
    NO_PERMISSION(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权"),

    USER_PWD_NOT_NULL(401001, "用户/密码必须填写"),
    USER_NOT_SCOPE(401002, "用户名不在指定范围"),
    PWD_NOT_SCOPE(401003, "密码不在指定范围"),
    IP_BLACK_LIST(401004, "很遗憾，访问IP已被列入系统黑名单"),
    PWD_ERROR_COUNT_LOCK(401005, "密码输入错误%s次，帐户锁定%s分钟"),
    USER_PWD_EROOR(401006, "用户不存在/密码错误"),
    PWD_ERROR_COUNT(401007, "密码输入错误%s次"),
    ACCOUNT_BLOCK_UP(401008, "对不起，您的账号：已停用")
    ;
    private int code;

    private String msg;

    ExceptionEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
