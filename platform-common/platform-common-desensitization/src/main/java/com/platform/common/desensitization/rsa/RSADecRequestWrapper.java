package com.platform.common.desensitization.rsa;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Vector;

public class RSADecRequestWrapper extends HttpServletRequestWrapper {

    private RSAProp props;
    private boolean isEncrypted = false;
    private final Map<String, String[]> decryptedParameterMap;
    private final byte[] decryptedBody;

    public boolean isEncrypted() {
        return isEncrypted;
    }

    public RSADecRequestWrapper(HttpServletRequest request, RSAProp props) {
        super(request);
        this.props = props;

        //1.url参数解密
        Map<String, String[]> parameterMap = getDecryptedParameterMap(request);
        this.decryptedParameterMap = parameterMap;

        //2.body 解密
        String body = getDecryptedBody(request);
        this.decryptedBody = body.getBytes(Charset.forName("UTF-8"));
    }

    /**
     * 获取所有参数名
     *
     * @return 返回所有参数名
     */
    @Override
    public Enumeration<String> getParameterNames() {
        Vector<String> vector = new Vector<String>(decryptedParameterMap.keySet());
        return vector.elements();
    }

    /**
     * 获取指定参数名的值，如果有重复的参数名，则返回第一个的值 接收一般变量 ，如text类型
     *
     * @param name 指定参数名
     * @return 指定参数名的值
     */
    @Override
    public String getParameter(String name) {
        String[] results = decryptedParameterMap.get(name);
        return results != null ? results[0] : "";
    }


    /**
     * 获取指定参数名的所有值的数组，如：checkbox的所有数据
     * 接收数组变量 ，如checkobx类型
     */
    @Override
    public String[] getParameterValues(String name) {
        return decryptedParameterMap.get(name);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return decryptedParameterMap;
    }

    /**
     * 重写getReader
     *
     * @return
     * @throws IOException
     */
    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    /**
     * 重写getInputStream
     *
     * @return
     * @throws IOException
     */
    @Override
    public ServletInputStream getInputStream() throws IOException {

        final ByteArrayInputStream bais = new ByteArrayInputStream(decryptedBody);

        return new ServletInputStream() {

            @Override
            public int read() throws IOException {
                return bais.read();
            }

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
            }
        };
    }

    /**
     * 解密Parameter
     *
     * @param request
     * @return
     */
    @SneakyThrows
    private Map<String, String[]> getDecryptedParameterMap(HttpServletRequest request) {

        Map<String, String[]> parameterMap = request.getParameterMap();

        if (parameterMap.containsKey("encryptedData")) {
            isEncrypted = true;
            Map<String, String[]> newParameterMap = new HashMap<>();
            //1.取出原始urlStr
            String encryptedData = request.getParameter("encryptedData");
            encryptedData = encryptedData.replaceAll(" ", "+");

            //2.解密后Parameter
            String encryptedUrlStr = RSAUtils.decryptPrivateKey(encryptedData, props.privateKeyServer);
            // url decode
            String str = URLDecoder.decode(encryptedUrlStr, StandardCharsets.UTF_8.toString());
            //
            if (!"{}".equalsIgnoreCase(str)) {

                String[] params = str.split("&");
                for (int i = 0; i < params.length; i++) {
                    String paramStr = params[i];
                    String[] p = paramStr.split("\\=");
                    if (p.length > 1) {
                        String paramKey = p[0];
                        String paramVal = p[1];
                        if (p.length == 2) {
                            String[] pSub = paramVal.split("\\,");
                            if (pSub.length > 1) {
                                // key1=value1,value2,value3
                                newParameterMap.put(p[0], pSub);
                            } else {
                                // key1=value1
                                newParameterMap.put(p[0], new String[]{paramVal});
                            }
                            //TODO key1=value1&key1=value2&key1=value3 暂未处理
                        }
                    }
                }
            }

            return newParameterMap;
        } else {
            return parameterMap;
        }
    }

    /**
     * 解密Body
     *
     * @param request
     * @return
     */
    @SneakyThrows
    private String getDecryptedBody(HttpServletRequest request) {

        //1.取出原始body
        String originBodyString = getOriginBodyString(request);
        //2.解密后body
        String encryptedBodyString = RSAUtils.decryptPrivateKey(originBodyString, props.privateKeyServer);
        //3. 兼容未加密的请求
        if (StringUtils.isBlank(encryptedBodyString)) {
            //如果 没有解密出内容，直接取原始数据
            return originBodyString;
        } else {
            isEncrypted = true;
            // url decode
            String str = URLDecoder.decode(encryptedBodyString, StandardCharsets.UTF_8.toString());
            //如果 有解密出内容，直接返回解密后
            return str;
        }
    }

    /**
     * 获取请求BodyString
     */
    private String getOriginBodyString(final ServletRequest request) {
        StringBuilder sb = new StringBuilder();
//        InputStream inputStream = null;
//        BufferedReader reader = null;
        try (
                InputStream inputStream = cloneInputStream(request.getInputStream());
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));
        ) {
//            inputStream = ;
//            reader = ;
            String line = "";
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    /**
     * 复制输入流
     */
    private InputStream cloneInputStream(ServletInputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        InputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        return byteArrayInputStream;
    }


}