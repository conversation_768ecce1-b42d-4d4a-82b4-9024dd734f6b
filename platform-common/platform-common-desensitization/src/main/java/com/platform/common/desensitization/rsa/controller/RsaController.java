package com.platform.common.desensitization.rsa.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.desensitization.rsa.RSAProp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 文件服务前端控制器
 * @author: tr
 * @date: 2024年03月28日 14:47
 */
@Api(tags = "文件服务前端控制器")
@RestController
@RequestMapping("/rsa")
public class RsaController {

    @Autowired
    private RSAProp rsaProp;

    @ApiOperation(value = "查询前端加密key",  notes = "rsa加密")
    @GetMapping("/publicKey")
    public ResponseResult<String> rsaPublicKey(){
        return ResponseResult.ok(rsaProp.publicKeyServer);
    }

    @ApiOperation(value = "查询前端解密key",  notes = "rsa解密")
    @GetMapping("/privateKey")
    public ResponseResult<String> rsaPrivateKey(){
        return ResponseResult.ok(rsaProp.privateKeyClient);
    }

}
