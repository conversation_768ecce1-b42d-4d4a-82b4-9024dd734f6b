package com.platform.common.desensitization.aspect;

import com.platform.common.desensitization.enums.EncryptType;
import com.platform.common.desensitization.enums.impl.Sm4Encrypt;
import com.platform.system.api.RemoteEncRuleService;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月08日 18:05
 */
@Configuration
@ConditionalOnClass(value = {SqlSessionFactory.class})
public class MybatisPlusConfig implements CommandLineRunner {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private RemoteEncRuleService remoteEncRuleService;

    @Override
    public void run(String... args) {
        org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
        configuration.addInterceptor(new ParamInterceptor(remoteEncRuleService));
        configuration.addInterceptor(new ResultInterceptor(remoteEncRuleService));
    }

    @Bean("SM4")
    public EncryptType Sm4Encrypt() {
        return new Sm4Encrypt();
    }

}
