package com.platform.common.desensitization.rsa;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 *
 */
@Slf4j
public class RSAFilter implements Filter {

    private RSAProp props;


    public RSAFilter(RSAProp props) {
        this.props = props;
    }

    /**
     * 加密，解密
     *
     * @param request
     * @param response
     * @param chain
     * @throws IOException
     * @throws ServletException
     */
    @SneakyThrows
    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse rsp = (HttpServletResponse) response;

        // ---- 请求 ----
        log.debug(" ====== rsaFilter req.getRequestURI() ======: {}", req.getRequestURI());
        String origin4Req = req.getHeader(org.springframework.http.HttpHeaders.ORIGIN);
        String originUrl = "";
        if (origin4Req != null) {
            UriComponents uriComponents = UriComponentsBuilder.fromOriginHeader(origin4Req).build();
            originUrl = uriComponents.getHost();

            log.debug(" ====== originUrl ======: {}", originUrl);
        }

        RSADecRequestWrapper reqWrapper = new RSADecRequestWrapper(req, props);
        RSAEncResponseWrapper rspWrapper = new RSAEncResponseWrapper(rsp);


        //
        chain.doFilter(reqWrapper, rspWrapper);

        byte[] bytes;

        //如果请求有加密，则加密响应
        if (reqWrapper.isEncrypted()) {
            // 取出响应
            String rspData = new String(rspWrapper.getResponseData(), StandardCharsets.UTF_8);
            bytes = encrypteResponse(rspData);
            //            "application/json;charset=utf-8"
            rsp.setHeader(HttpHeaders.CONTENT_TYPE,"application/json;charset=utf-8");
        }
        //否则原样返回
        else {
            bytes = rspWrapper.getResponseData();
        }

        response.setContentLength(-1);
        response.getOutputStream().write(bytes);
        response.getOutputStream().flush();

    }

    private byte[] encrypteResponse(String rspData) throws Exception {

        //urlEncode后在加密，防止乱码
        String rspDataUrlEncode = URLEncoder.encode(rspData, StandardCharsets.UTF_8.toString());
        //处理空格全部都变成了加号
        rspDataUrlEncode = rspDataUrlEncode.replaceAll("\\+", "%20");
        String encryptedRspData = RSAUtils.encryptPublicKey(rspDataUrlEncode, props.publicKeyClient);
//        log.debug("*******向ServletResponse写入内容*******: " + encryptedRspData);


        return encryptedRspData.getBytes(StandardCharsets.UTF_8);
    }
}