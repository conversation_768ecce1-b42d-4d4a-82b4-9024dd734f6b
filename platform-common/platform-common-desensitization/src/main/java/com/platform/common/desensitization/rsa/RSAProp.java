package com.platform.common.desensitization.rsa;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;


/**
 *
 */
@Component
@Data
@Configuration
@ConfigurationProperties(prefix = "desensitization.rsa", ignoreUnknownFields = true)
public class RSAProp {
    /**
     * 服务端 公钥（请求加密）私钥（请求解密）
     */
    public String publicKeyServer;
    public String privateKeyServer;
    /**
     * 客户端 公钥（响应加密）私钥（响应解密）
     */
    public String publicKeyClient;
    public String privateKeyClient;
}