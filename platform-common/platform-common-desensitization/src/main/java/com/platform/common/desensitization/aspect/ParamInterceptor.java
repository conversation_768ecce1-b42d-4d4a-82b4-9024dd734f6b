package com.platform.common.desensitization.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.desensitization.enums.EncryptType;
import com.platform.common.desensitization.enums.EncryptTypeEnum;
import com.platform.system.api.RemoteEncRuleService;
import com.platform.system.api.domain.request.SysEncRuleReq;
import com.platform.system.api.domain.response.SysEncRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Intercepts({

        @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class),
})
@Slf4j
public class ParamInterceptor implements Interceptor {

    private RemoteEncRuleService remoteEncRuleService;

    public ParamInterceptor(RemoteEncRuleService remoteEncRuleService) {
        this.remoteEncRuleService = remoteEncRuleService;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.debug("拦截器ParamInterceptor");
//拦截 ParameterHandler 的 setParameters 方法 动态设置参数
        CacheClient redisService = SpringUtils.getBean(CacheClient.class);
        if (redisService != null) {
            String redisKey = CacheConstants.SYS_CONFIG_KEY + "sys.enc.rule.switch";
            String redisValue = redisService.get(redisKey);
            if ("true".equals(redisValue)) {
                try {
                    if (invocation.getTarget() instanceof ParameterHandler) {

                        ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
                        PreparedStatement ps = (PreparedStatement) invocation.getArgs()[0];
                        //加密规则下的表一律不走该逻辑
                        Field mappedStatementField =
                                parameterHandler.getClass().getDeclaredField("mappedStatement");
                        mappedStatementField.setAccessible(true);
                        MappedStatement mappedStatement = (MappedStatement) mappedStatementField.get(parameterHandler);
                        if (mappedStatement.getId().contains("SysEncRuleMapper")){
                            return invocation.proceed();
                        }

// 反射获取 BoundSql 对象，此对象包含生成的sql和sql的参数map映射
                        Field boundSqlField = parameterHandler.getClass().getDeclaredField("boundSql");
                        boundSqlField.setAccessible(true);
                        BoundSql boundSql = (BoundSql) boundSqlField.get(parameterHandler);
// 反射获取 参数对像
                        Field parameterField =
                                parameterHandler.getClass().getDeclaredField("parameterObject");
                        parameterField.setAccessible(true);
                        Object parameterObject = parameterField.get(parameterHandler);

                        List<SysEncRuleResp> sysEncRuleResps = remoteEncRuleService.list(new SysEncRuleReq()).getData();
                        Map<String, String> encRuleMap = sysEncRuleResps.stream().collect(Collectors.toMap(SysEncRuleResp::getColumnName, SysEncRuleResp::getEncType));
                        if (CollUtil.isNotEmpty(sysEncRuleResps)){
                            if (parameterObject instanceof Map){
                                Map<String, Object> objectMap = (Map<String, Object>) parameterObject;
                                objectMap.forEach((key, value) -> {
                                    if (encRuleMap.containsKey(key)) {
                                        String encType = encRuleMap.get(key);
                                        EncryptType type = EncryptTypeEnum.getByName(encType);
                                        if (ObjectUtil.isNotEmpty(type)) {
                                            if (value instanceof String) {
                                                objectMap.put(key, type.encrypt(value.toString()));
                                            }
                                        }
                                    }
                                });
                            }else if (parameterObject instanceof Collection){
                                Collection<Object> collection = (Collection<Object>) parameterObject;
                                collection.forEach(item -> {
                                    encObject(item, encRuleMap);
                                });
                            }else {
                                encObject(parameterObject, encRuleMap);
                            }

                        }
                    }
                }catch (Exception e){
                    log.debug(e.getMessage());
                }
            }
        }
        return invocation.proceed();
    }

    private void encObject(Object parameterObject, Map<String, String> encRuleMap) {
        if (ObjectUtil.isNotEmpty(parameterObject)){
            if (ObjectUtil.isBasicType(parameterObject)){
                return;
            }
            Class<?> parameterObjectClass = parameterObject.getClass();
            Field[] declaredFields = parameterObjectClass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                if (encRuleMap.containsKey(declaredField.getName())) {
                    String encType = encRuleMap.get(declaredField.getName());
                    EncryptType type = EncryptTypeEnum.getByName(encType);
                    try {
                        if (ObjectUtil.isNotEmpty(type)) {
                            declaredField.setAccessible(true);
                            Object value = declaredField.get(parameterObject);
                            if (value instanceof String) {
                                declaredField.set(parameterObject, type.encrypt(value.toString()));
                            }
                        }
                    }catch (Exception e){
                        log.error("错误字段：" + declaredField.getName(), e);
                        throw new ServiceException("加密数据库字段失败，请确认后重试！");
                    }
                }
            }
        }
    }
}
