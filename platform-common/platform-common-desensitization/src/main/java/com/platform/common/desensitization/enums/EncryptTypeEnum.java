package com.platform.common.desensitization.enums;

import cn.hutool.crypto.digest.DigestUtil;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.platform.common.core.utils.AESUtils;

public enum EncryptTypeEnum implements EncryptType {
    /**
     * 简单字符ascii偏移加密
     */
    SIMPLE{
        @Override
        public String encrypt(String source) {
            if (source == null || source.isEmpty()) {
                return source;
            }
            char[] chars = source.toCharArray();
            for (int i = 0; i < chars.length; i++) {
                chars[i] = (char) (chars[i] + 17);
            }
            return new String(chars);
        }

        @Override
        public String decrypt(String source) {
            if (source == null || source.isEmpty()) {
                return source;
            }
            char[] chars = source.toCharArray();
            for (int i = 0; i < chars.length; i++) {
                chars[i] = (char) (chars[i] - 17);
            }
            return new String(chars);
        }
    },
    /**
     * aes可逆加密
     */
    AES{
        @Override
        public String encrypt(String source) {
            if (source == null || source.isEmpty()) {
                return source;
            }
            return AESUtils.encrypt(source, "platformplatform");
        }

        @Override
        public String decrypt(String source) {
            if (source == null || source.isEmpty()) {
                return source;
            }
            return AESUtils.decrypt(source,"platformplatform");
        }
    },
    /**
     * md5不可逆加密
     */
    MD5{
        @Override
        public String encrypt(String source) {
            if (source == null || source.isEmpty()) {
                return source;
            }
            return DigestUtil.md5Hex(source);
        }
    };

    public static EncryptType getByName(String name) {
        for (EncryptTypeEnum encryptTypeEnum : EncryptTypeEnum.values()) {
            if (encryptTypeEnum.name().equals(name)) {
                return encryptTypeEnum;
            }
        }
        //自定义加密类型支持
        Object encBean = SpringUtils.getBean(name);
        if (encBean instanceof EncryptType) {
            return (EncryptType) encBean;
        }
        return null;
    }

}
