package com.platform.common.desensitization.rsa;

import com.platform.common.desensitization.rsa.controller.RsaController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月08日 18:05
 */
@Configuration
public class RsaEncConfig {

    @Autowired
    private RSAProp props;

    @Bean("RSAFilterReg")
    public FilterRegistrationBean<RSAFilter> registrationIpBean(){
        FilterRegistrationBean<RSAFilter> bean = new FilterRegistrationBean<RSAFilter>();
        bean.setFilter(new RSAFilter(props));
        bean.setName("RSAFilter");
        bean.addUrlPatterns("/*");
        bean.setOrder(Ordered.LOWEST_PRECEDENCE);
        return bean;
    }

    @Bean
    public RsaController rsaController(){
        return new RsaController();
    }
}
