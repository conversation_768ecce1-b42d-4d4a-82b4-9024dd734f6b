package com.platform.common.desensitization.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.desensitization.enums.EncryptType;
import com.platform.common.desensitization.enums.EncryptTypeEnum;
import com.platform.system.api.RemoteEncRuleService;
import com.platform.system.api.domain.request.SysEncRuleReq;
import com.platform.system.api.domain.response.SysEncRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = { Statement.class })
})
@Slf4j
public class ResultInterceptor implements Interceptor {

    private RemoteEncRuleService remoteEncRuleService;

    public ResultInterceptor(RemoteEncRuleService remoteEncRuleService) {
        this.remoteEncRuleService = remoteEncRuleService;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.debug("拦截器ResultInterceptor");
        Object result = invocation.proceed();

        CacheClient redisService = SpringUtils.getBean(CacheClient.class);
        if (redisService != null) {
            String redisKey = CacheConstants.SYS_CONFIG_KEY + "sys.enc.rule.switch";
            String redisValue = redisService.get(redisKey);
            if ("true".equals(redisValue)) {

                //加密规则下的表一律不走该逻辑
                Field mappedStatementField =
                        invocation.getTarget().getClass().getDeclaredField("mappedStatement");
                mappedStatementField.setAccessible(true);
                MappedStatement mappedStatement = (MappedStatement) mappedStatementField.get(invocation.getTarget());
                if (mappedStatement.getId().contains("SysEncRuleMapper")){
                    return result;
                }

                try {
                    if (Objects.isNull(result)) {
                        return null;
                    }
                    List<SysEncRuleResp> sysEncRuleResps = remoteEncRuleService.list(new SysEncRuleReq()).getData();
                    Map<String, String> encRuleMap = sysEncRuleResps.stream().collect(Collectors.toMap(SysEncRuleResp::getColumnName, SysEncRuleResp::getEncType));
                    if (result instanceof Map){
                        Map<String, Object> objectMap = (Map<String, Object>) result;
                        objectMap.forEach((key, value) -> {
                            if (encRuleMap.containsKey(key)) {
                                String encType = encRuleMap.get(key);
                                EncryptType type = EncryptTypeEnum.getByName(encType);
                                if (ObjectUtil.isNotEmpty(type)) {
                                    if (value instanceof String) {
                                        objectMap.put(key, type.decrypt(value.toString()));
                                    }
                                }
                            }
                        });
                    }else if (result instanceof Collection){
                        Collection<Object> collection = (Collection<Object>) result;
                        collection.forEach(item -> {
                            decObject(item, encRuleMap);
                        });
                    }else {
                        decObject(result, encRuleMap);
                    }
                }catch (Exception e){
                    log.debug(e.getMessage());
                }
            }
        }
     return result;
    }

    private void decObject(Object parameterObject, Map<String, String> encRuleMap) {
        if (ObjectUtil.isNotEmpty(parameterObject)){

            if (ObjectUtil.isBasicType(parameterObject)){
                return;
            }

            Class<?> parameterObjectClass = parameterObject.getClass();
            Field[] declaredFields = parameterObjectClass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                if (encRuleMap.containsKey(declaredField.getName())) {
                    String encType = encRuleMap.get(declaredField.getName());
                    EncryptType type = EncryptTypeEnum.getByName(encType);
                    try {
                        if (ObjectUtil.isNotEmpty(type)) {
                            declaredField.setAccessible(true);
                            Object value = declaredField.get(parameterObject);
                            if (value instanceof String) {
                                declaredField.set(parameterObject, type.decrypt(value.toString()));
                            }
                        }
                    }catch (Exception e){
                        log.error("错误字段：" + declaredField.getName(), e);
                        throw new ServiceException("解密数据库字段失败，请确认后重试！");
                    }
                }
            }
        }
    }
}
