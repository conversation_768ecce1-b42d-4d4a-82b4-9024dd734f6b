package com.platform.common.log.service;

import cn.hutool.json.JSONUtil;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.domain.request.SysOperLogReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.platform.system.api.RemoteLogService;

/**
 * 异步调用日志服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncLogService
{
    @Autowired
    private RemoteLogService remoteLogService;

    /**
     * 保存系统日志记录
     */
    @Async
    public void saveSysLog(SysOperLogReq sysOperLogReq) throws Exception
    {
        ResponseResult<Boolean> responseResult = remoteLogService.saveLog(sysOperLogReq);
        log.info("调用远程日志服务结果:" + JSONUtil.toJsonStr(responseResult));
    }
}
