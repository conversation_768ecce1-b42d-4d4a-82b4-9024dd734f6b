package com.platform.common.toolbox.logic;

import cn.hutool.core.util.StrUtil;
import com.ctdi.base.core.exception.auth.NotLoginException;
import com.ctdi.base.core.exception.auth.NotPermissionException;
import com.ctdi.base.core.exception.auth.NotRoleException;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.constant.SystemConstants;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.common.toolbox.service.TokenService;
import com.platform.system.api.model.LoginUser;
import com.platform.system.api.model.SysApiDTO;
import org.springframework.util.PatternMatchUtils;

import java.util.*;

/**
 * Token 权限验证，逻辑实现类
 * 
 * <AUTHOR>
 */
public class AuthLogic
{

    /** 管理员角色权限标识 */
    private static final String SUPER_ADMIN = "admin";

    public TokenService tokenService = SpringUtils.getBean(TokenService.class);

    /**
     * 会话注销
     */
    public void logout()
    {
        String token = TokenUtils.getToken();
        if (token == null)
        {
            return;
        }
        logoutByToken(token);
    }

    /**
     * 会话注销，根据指定Token
     */
    public void logoutByToken(String token)
    {
        tokenService.delLoginUser(token);
    }

    /**
     * 检验用户是否已经登录，如未登录，则抛出异常
     */
    public void checkLogin()
    {
        getLoginUser();
    }

    /**
     * 获取当前用户缓存信息, 如果未登录，则抛出异常
     * 
     * @return 用户缓存信息
     */
    public LoginUser getLoginUser()
    {
        String token = TokenUtils.getToken();
        if (token == null)
        {
            throw new NotLoginException("未提供token");
        }
        LoginUser loginUser = getLoginUser(token);
        if (loginUser == null)
        {
            throw new NotLoginException("无效的token");
        }
        return loginUser;
    }

    /**
     * 获取当前用户缓存信息, 如果未登录，则抛出异常
     * 
     * @param token 前端传递的认证信息
     * @return 用户缓存信息
     */
    public LoginUser getLoginUser(String token)
    {
        return tokenService.getLoginUser(token);
    }

    /**
     * 验证用户是否具备某权限
     * 
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public boolean hasPermi(String permission, String requestMethod)
    {
        return hasPermi(getPermiList(), permission, requestMethod);
    }

    /**
     * 验证用户是否具备某权限, 如果验证未通过，则抛出异常: NotPermissionException
     * 
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public void checkPermi(String permission, String requestMethod)
    {
        if (!hasPermi(getPermiList(), permission, requestMethod))
        {
            throw new NotPermissionException(permission);
        }
    }

    /**
     * 判断用户是否拥有某个角色
     * 
     * @param role 角色标识
     * @return 用户是否具备某角色
     */
    public boolean hasRole(String role)
    {
        return hasRole(getRoleList(), role);
    }

    /**
     * 判断用户是否拥有某个角色, 如果验证未通过，则抛出异常: NotRoleException
     * 
     * @param role 角色标识
     */
    public void checkRole(String role)
    {
        if (!hasRole(role))
        {
            throw new NotRoleException(role);
        }
    }

    /**
     * 验证用户是否含有指定角色，必须全部拥有
     * 
     * @param roles 角色标识数组
     */
    public void checkRoleAnd(String... roles)
    {
        Set<String> roleList = getRoleList();
        for (String role : roles)
        {
            if (!hasRole(roleList, role))
            {
                throw new NotRoleException(role);
            }
        }
    }

    /**
     * 验证用户是否含有指定角色，只需包含其中一个
     * 
     * @param roles 角色标识数组
     */
    public void checkRoleOr(String... roles)
    {
        Set<String> roleList = getRoleList();
        for (String role : roles)
        {
            if (hasRole(roleList, role))
            {
                return;
            }
        }
        if (roles.length > 0)
        {
            throw new NotRoleException(roles);
        }
    }

    /**
     * 获取当前账号的角色列表
     * 
     * @return 角色列表
     */
    public Set<String> getRoleList()
    {
        try
        {
            LoginUser loginUser = getLoginUser();
            return loginUser.getRoles();
        }
        catch (Exception e)
        {
            return new HashSet<>();
        }
    }

    /**
     * 获取当前账号的权限列表
     * 
     * @return 权限列表
     */
    public List<SysApiDTO> getPermiList()
    {
        try
        {
            LoginUser loginUser = getLoginUser();
            return loginUser.getPermissions();
        }
        catch (Exception e)
        {
            return new ArrayList<>();
        }
    }

    /**
     * 判断是否包含权限
     * 
     * @param authorities 权限列表
     * @param url 权限字符串
     * @return 用户是否具备某权限
     */
    public boolean hasPermi(List<SysApiDTO> authorities, String url, String requestMethod)
    {
        //判断接口的请求方式是否一致，同时，判断接口的url是否一致
        return authorities.stream()
                .anyMatch(x -> StrUtil.equals(x.getRequestMethod(), requestMethod) &&
                        PatternMatchUtils.simpleMatch(x.getPerms(), url));
    }

    /**
     * 判断是否包含角色
     * 
     * @param roles 角色列表
     * @param role 角色
     * @return 用户是否具备某角色权限
     */
    public boolean hasRole(Collection<String> roles, String role)
    {
        return roles.stream().filter(StringUtils::hasText)
                .anyMatch(x -> SUPER_ADMIN.contains(x) || PatternMatchUtils.simpleMatch(x, role));
    }
}
