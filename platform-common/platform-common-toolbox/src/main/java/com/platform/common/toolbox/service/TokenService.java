package com.platform.common.toolbox.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.common.starter.toolbox.ip.IpUtils;
import com.ctdi.common.starter.toolbox.utils.ServletUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.ctdi.common.starter.toolbox.uuid.IdUtils;
import com.platform.common.core.config.properties.CoreProperties;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.common.core.utils.AESUtils;
import com.platform.common.core.utils.JwtUtils;
import com.platform.common.toolbox.utils.TokenUtils;
import com.platform.system.api.RemoteUserService;
import com.platform.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 * 
 * <AUTHOR>
 */
@Component
public class TokenService {
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    @Autowired
    private CacheClient redisService;

    @Autowired(required = false)
    private RemoteUserService remoteUserService;

    @Autowired
    private CoreProperties coreProperties;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final static String SEPARATOR_KEY = CacheConstants.SEPARATOR_KEY;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginUser loginUser)
    {
        long expireTime = coreProperties.getExpireTime();

        String token = IdUtils.fastUUID();
        Long userId = loginUser.getSysUser().getUserId();
        String userName = loginUser.getSysUser().getUserName();
        loginUser.setToken(token);
        loginUser.setUserId(userId);
        loginUser.setUserName(userName);
        loginUser.setIpaddr(IpUtils.getIpAddr());
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, AESUtils.encrypt(token, JwtUtils.KEY));
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, AESUtils.encrypt(userId.toString(), JwtUtils.KEY));
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, AESUtils.encrypt(userName, JwtUtils.KEY));

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        rspMap.put("username", userName);
        return rspMap;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser()
    {
        return getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request)
    {
        // 获取请求携带的令牌
        String token = TokenUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(String token)
    {
        LoginUser user = null;
        try
        {
            if (StringUtils.isNotEmpty(token))
            {
                token = TokenUtils.replaceTokenPrefix(token);
                String userkey = JwtUtils.getUserKey(token);
                String userId = JwtUtils.getUserId(token);
                String userStr = redisService.getCacheObject(getTokenKey(userkey,Long.valueOf(userId)));
                if (!coreProperties.isLocalSystemFlag() && StrUtil.isEmpty(userStr)){
                    //其他系统引用security包，redis库不同，获取不到用户信息，需要调用接口获取
                    ResponseResult<LoginUser> responseResult = remoteUserService.getCurrUserInfo(token);
                    if (responseResult.getCode() == HttpStatus.HTTP_OK){
                        LoginUser loginUser = responseResult.getData();
                        refreshToken(loginUser);
                        return loginUser;
                    }
                }
                if (StrUtil.isEmpty(userStr)){
                    return null;
                }
                user = JSONUtil.toBean(userStr, LoginUser.class);
                refreshToken(user);
                return user;
            }
        }
        catch (Exception e){
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return user;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser)
    {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken()))
        {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginUser(String token)
    {
        if (StringUtils.isNotEmpty(token))
        {
            String userkey = JwtUtils.getUserKey(token);
            String userId = JwtUtils.getUserId(token);
            redisService.deleteObject(getTokenKey(userkey,Long.valueOf(userId)));
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser)
    {
        long expireTime = coreProperties.getExpireTime();
        Long userId = loginUser.getUserId();
        if (coreProperties.isUniqueLoginFlag()){
            //先删除该用户之前登录的token
            Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + userId + SEPARATOR_KEY + "*");
            if(!keys.isEmpty()){
                redisService.deleteObject(keys);
            }
        }

        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        //修改 将缓存用户token的redis key 由"login_tokens:+token 修改为 "login_tokens: + userId + token
        String userTokenKey = getTokenKey(loginUser.getToken(),userId);
        //序列化用户信息对象
        String loginUserStr = JSONUtil.toJsonStr(loginUser);
        redisService.setCacheObject(userTokenKey, loginUserStr, expireTime, TimeUnit.MINUTES);
    }

    private String getTokenKey(String token,Long userId)
    {
        return ACCESS_TOKEN + userId + SEPARATOR_KEY + token;
    }
}