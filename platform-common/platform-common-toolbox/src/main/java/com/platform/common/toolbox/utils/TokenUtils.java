package com.platform.common.toolbox.utils;

import cn.hutool.json.JSONUtil;
import com.common.middleware.cache.CacheClient;
import com.ctdi.base.core.exception.ServiceException;
import com.ctdi.common.starter.toolbox.utils.ServletUtils;
import com.ctdi.common.starter.toolbox.utils.SpringUtils;
import com.ctdi.common.starter.toolbox.utils.StringUtils;
import com.platform.common.core.config.properties.CoreProperties;
import com.platform.common.core.config.properties.JwtProperties;
import com.platform.common.core.constant.CacheConstants;
import com.platform.common.core.constant.TokenConstants;
import com.platform.common.core.enums.ExceptionEnum;
import com.platform.common.core.utils.JwtUtils;
import com.platform.system.api.model.LoginUser;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * @Description: Token工具类
 * @author: tr
 * @date: 2024年03月25日 10:58
 */
@Slf4j
public class TokenUtils {

    public static CacheClient redisService = SpringUtils.getBean(CacheClient.class);

    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final static String SEPARATOR_KEY = CacheConstants.SEPARATOR_KEY;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    @Autowired
    private static CoreProperties coreProperties = SpringUtils.getBean(CoreProperties.class);;

    @Value("${platform.login.flag:false}")
    private static boolean uniqueLoginFlag;

    /**
     * @Description: Token检查工具类
     * @author: tr
     * @Date: 2024/3/25 10:59
     * @param: [token]
     * @returnValue: void
     */
    public static LoginUser checkToken(String token){
        Claims claims = null;
        try{
            claims = JwtUtils.parseToken(token);
            if (claims == null)
            {
                throw new ServiceException(ExceptionEnum.TOKEN_EXPIRE.getMsg(), ExceptionEnum.TOKEN_EXPIRE.getCode());
            }
        }catch (Exception e){
            log.error(e.toString());
            throw new ServiceException(ExceptionEnum.TOKEN_ERROR.getMsg(), ExceptionEnum.TOKEN_ERROR.getCode());
        }
        //获取当前登录用户信息，会判断从redis中没有获取的用户信息，则调用接口获取并刷新token
        LoginUser loginUser = getLoginUser(token);
        if (loginUser == null){
            throw new ServiceException(ExceptionEnum.LOGIN_EXPIRE.getMsg(), ExceptionEnum.LOGIN_EXPIRE.getCode());
        }

        String userkey = JwtUtils.getUserKey(claims);
        String userId = JwtUtils.getUserId(claims);
        boolean islogin = redisService.hasKey(CacheConstants.LOGIN_TOKEN_KEY + userId + CacheConstants.SEPARATOR_KEY + userkey);
        if (!islogin)
        {
            throw new ServiceException(ExceptionEnum.LOGIN_EXPIRE.getMsg(), ExceptionEnum.LOGIN_EXPIRE.getCode());
        }
        String username = JwtUtils.getUserName(claims);
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(username))
        {
            throw new ServiceException(ExceptionEnum.TOKEN_VERIFY_FAIL.getMsg(), ExceptionEnum.TOKEN_VERIFY_FAIL.getCode());
        }
        return loginUser;
    }

    /**
     * @Description: 根据Token获取当前登录用户信息
     * @author: tr
     * @Date: 2024/12/16 17:10
     * @param: [token]
     * @returnValue: com.platform.system.api.model.LoginUser
     */
    public static LoginUser getLoginUser(String token){
        LoginUser loginUser = AuthUtils.getLoginUser(token);
        return loginUser;
    }

    /**
     * @Description: 刷新Token的有效期
     * @author: tr
     * @Date: 2024/12/16 17:10
     * @param: [loginUser]
     * @returnValue: void
     */
    public static void refreshToken(LoginUser loginUser)
    {
        long expireTime = coreProperties.getExpireTime();
        Long userId = loginUser.getUserId();
        if (uniqueLoginFlag){
            //先删除该用户之前登录的token
            Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + userId + SEPARATOR_KEY + "*");
            if(!keys.isEmpty()){
                redisService.deleteObject(keys);
            }
        }

        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        //修改 将缓存用户token的redis key 由"login_tokens:+token 修改为 "login_tokens: + userId + token
        String userTokenKey = getTokenKey(loginUser.getToken(),userId);
        //序列化用户信息对象
        String loginUserStr = JSONUtil.toJsonStr(loginUser);
        redisService.setCacheObject(userTokenKey, loginUserStr, expireTime, TimeUnit.MINUTES);
    }

    private static String getTokenKey(String token,Long userId)
    {
        return ACCESS_TOKEN + userId + SEPARATOR_KEY + token;
    }

    public static String getToken()
    {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request)
    {
        // 从header获取token标识
        String token = request.getHeader(JwtProperties.tokenHeader);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token)
    {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }
}
