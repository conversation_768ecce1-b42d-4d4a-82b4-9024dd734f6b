package com.platform.common.datascope.handler;

import cn.hutool.core.util.StrUtil;
import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.enums.DataScopeTypeEnum;
import com.platform.common.core.enums.UserTypeEnum;
import com.platform.common.datascope.annotation.DataScope;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.RemoteCountryAddressService;
import com.platform.system.api.domain.response.SysCountryAddressResp;
import com.platform.system.api.model.LoginUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Invocation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 数据权限拦截切面处理器，实现数据权限控制
 * @author: tr
 * @date: 2024年06月19日 11:18
 */

@Slf4j
@Aspect
@Data
@Component
public class DataScopePermissionHandler {

    /**
     * 通过ThreadLocal记录权限相关的属性值
     */
    private static final ThreadLocal<DataScopeParam> threadLocal = new ThreadLocal<>();

    @Value("${country.code:430000000000}")
    private String countryCode;

    @Autowired
    private RemoteCountryAddressService remoteCountryAddressService;

    /**
     * @Description: 方法之前拦截，将注解DataScope参数值并在请求到来时赋值给ThreadLocal对象中
     * @author: tr
     * @Date: 2024/6/21 15:38
     * @param: [point, dataScope]
     * @returnValue: void
     */
    @Before("@annotation(dataScope)")
    public void doBefore(JoinPoint point, DataScope dataScope) {
        LoginUser currentUser = SecurityUtils.getLoginUser();
        if (currentUser != null) {
            DataScopeParam dataScopeParam = new DataScopeParam(
                    dataScope.joinTableName(), dataScope.joinField(), dataScope.permissionField()
                    , dataScope.joinSelectField(), dataScope.permissionType(), dataScope.dataScopeType()
                    , dataScope.dataType(), currentUser);
            threadLocal.set(dataScopeParam);
        }
    }

    /**
     * @Description: 方法调用结束，移除ThreadLocal存储的值
     * @author: tr
     * @Date: 2024/6/21 15:39
     * @param dataScope
     * @returnValue: void
     */
    @After("@annotation(dataScope)")
    public void clearThreadLocal(DataScope dataScope) {
        threadLocal.remove();
    }

    /**
     * @Description: 构建数据权限参数
     * @author: tr
     * @Date: 2024/7/8 20:08
     * @param: [invocation]
     * @returnValue: void
     */
    public void buildDataPermission(Invocation invocation) throws JSQLParserException {
        Long currentTimeMillis = System.currentTimeMillis();
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];

        DataScopeParam dataScopeParam = threadLocal.get();
        if (dataScopeParam == null) {
            return;
        }
        final Object[] queryArgs = invocation.getArgs();
        Object parameter = invocation.getArgs()[1];
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);

        LoginUser currentUser = dataScopeParam.getCurrentUser();
        if (StrUtil.equals(currentUser.getSysUser().getUserType(), UserTypeEnum.SYSTEM_USER.getCode())){
            //系统管理员不进行数据权限过滤
            return ;
        }

        //没有查询语句的标记
        boolean flag = false;
        for (int i =0;i<queryArgs.length;i++){
            if (queryArgs[i] instanceof BoundSql){
                flag = true;
            }
        }
        //过滤的值
        List<String> codeList = null;
        String dataScopeType = dataScopeParam.getDataScopeType();
        //sql条件
        String sql = "";
        if (StrUtil.equals(dataScopeType, DataScopeTypeEnum.ORG.getCode())){
            //根据当前用户所属的组织机构进行过滤
            codeList = currentUser.getOrgs().stream().map(o->o.getOrgCode()).collect(Collectors.toList());
        }else {
            //根据当前用户所属组织机构的管辖范围进行过滤，上级关联下级
            List<String> orgGuCodeList =
                    currentUser.getOrgs().stream().map(o->o.getGuCode()).collect(Collectors.toList());
            if (orgGuCodeList.contains(countryCode)){
                //省级地址，不进行过滤，有查看所有的权限，不需要添加过滤条件
                flag = true;
            }else{
                ResponseResult<List<SysCountryAddressResp>> responseResult =
                        remoteCountryAddressService.listByPCodes(orgGuCodeList.toArray(new String[orgGuCodeList.size()]));
                if (responseResult.getCode() == 200){
                    codeList = responseResult.getData().stream().map(c -> c.getCode()).collect(Collectors.toList());
                }else{
                    //接口调用报错，则数据过滤不能出数据
                    sql = "1=2";
                }
            }
        }

        String oldsql = boundSql.getSql();
        log.info("{}-数据权限的原始SQL：{}", currentTimeMillis, oldsql);

        if (!flag){
            if (StrUtil.isBlank(sql)){
                //拼接数据权限过滤，查询条件的SQL语句，有多个组织机构或多个区域，使用OR拼接
                String sqlTm = " {} like '{}%'";
                StringBuffer sbSql = new StringBuffer();
                //SQL模板
                for (int i = 0; i<codeList.size(); i++){
                    String code = codeList.get(i);
                    sbSql.append(StrUtil.format(sqlTm, dataScopeParam.getJoinField(), code));
                    if (i < (codeList.size()-1)){
                        sbSql.append(" OR ");
                    }
                }
                if (codeList == null || codeList.size() == 0 ){
                    //过滤的条件值为空，则默认查询不出数据
                    sql = " 1 = 2 ";
                }else if (dataScopeParam.getPermissionType() == 2){
                    sql = sbSql.toString();
                }else{
                    String dataType = dataScopeParam.getDataType();
                    if (StrUtil.isNotEmpty(dataType)){
                        //公共表sys_data_scope需根据data_type字段进行数据权限过滤，区分源数据的类型，如角色和角色组这些
                        //SQL模板
                        sql = " {} IN (SELECT {} FROM {} WHERE DEL_FLAG = '0' AND DATA_TYPE = {} AND ({}))";
                        sql = StrUtil.format(sql, dataScopeParam.getPermissionField(), dataScopeParam.getJoinSelectField()
                                , dataScopeParam.getJoinTableName(), dataType
                                , sbSql.toString());
                    }else{
                        //SQL模板
                        sql = " {} IN (SELECT {} FROM {} WHERE DEL_FLAG = '0' AND ({}))";
                        sql = StrUtil.format(sql, dataScopeParam.getPermissionField(), dataScopeParam.getJoinSelectField()
                                , dataScopeParam.getJoinTableName(), sbSql.toString());
                    }
                }
            }
            log.info("{}-数据权限的SQL条件：{}", currentTimeMillis, sql);
            Select select = (Select)CCJSqlParserUtil.parse(oldsql);
            PlainSelect plainSelect = (PlainSelect)select.getSelectBody();
            final Expression expression = plainSelect.getWhere();
            final Expression envCondition = CCJSqlParserUtil.parseCondExpression(sql);
            if (expression == null) {
                plainSelect.setWhere(envCondition);
            } else {
                AndExpression andExpression = new AndExpression(expression, envCondition);
                plainSelect.setWhere(andExpression);
            }
            oldsql = plainSelect.toString();
        }else{

        }
        log.info("{}-数据权限的过滤后SQL：{}", currentTimeMillis, oldsql);
        BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), oldsql, boundSql.getParameterMappings(), boundSql.getParameterObject());
        MappedStatement newMs = copyFromMappedStatement(mappedStatement, new BoundSqlSqlSource(newBoundSql));
        for (ParameterMapping mapping : boundSql.getParameterMappings()) {
            String prop = mapping.getProperty();
            if (boundSql.hasAdditionalParameter(prop)) {
                newBoundSql.setAdditionalParameter(prop, boundSql.getAdditionalParameter(prop));
            }
        }
        queryArgs[0] = newMs;
    }
    @Data
    @AllArgsConstructor
    static class DataScopeParam {
        /**
         * 关联表名
         */
        private String joinTableName;
        /**
         * 关联表名的查询条件字段名，默认org_id
         */
        private String joinField;
        /**
         * 权限字段名，默认为user_id
         */
        private String permissionField;
        /**
         * 关联查询的字段名，默认为user_id
         */
        private String joinSelectField;

        /**
         * 权限类型，1-自定义SQL权限，即根据SQL进行动态权限控制，2-根据字段进行权限控制，默认1
         */
        private Integer permissionType;

        /** 权限过滤类型（1-组织机构过滤，2-行政区划（地区）过滤，默认组织机构过滤） **/
        private String dataScopeType;

        /** 数据类型（1-角色、2-角色组），针对写入到sys_data_scope的数据类型判断 **/
        private String dataType;

        /** 当前用户信息 **/
        private LoginUser currentUser;

    }

    /**
     * @Description: 重新生成MappedStatement
     * @author: tr
     * @Date: 2024/7/8 20:07
     * @param: [ms, newSqlSource]
     * @returnValue: org.apache.ibatis.mapping.MappedStatement
     */
    private MappedStatement copyFromMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder = new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length > 0) {
            builder.keyProperty(ms.getKeyProperties()[0]);
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());
        return builder.build();
    }

    /**
     * @Description: SQL源
     * @author: tr
     * @Date: 2024/7/8 20:07
     * @param: 
     * @returnValue: 
     */
    public static class BoundSqlSqlSource implements SqlSource {
        private BoundSql boundSql;
        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }
        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}
