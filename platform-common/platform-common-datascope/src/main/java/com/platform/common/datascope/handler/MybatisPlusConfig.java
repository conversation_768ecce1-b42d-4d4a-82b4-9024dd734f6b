package com.platform.common.datascope.handler;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月08日 18:05
 */
@AutoConfigureAfter({PageHelperAutoConfiguration.class})
@Configuration
public class MybatisPlusConfig implements CommandLineRunner {

    @Autowired
    private DataScopePermissionHandler dataScopePermissionHandler;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void run(String... args) {
        org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
        DataScopePermissionInterceptor dataScopePermissionInterceptor = new DataScopePermissionInterceptor();
        dataScopePermissionInterceptor.setDataScopePermissionHandler(dataScopePermissionHandler);
        configuration.addInterceptor(dataScopePermissionInterceptor);
    }
}
