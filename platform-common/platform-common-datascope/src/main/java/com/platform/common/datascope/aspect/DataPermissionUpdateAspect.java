package com.platform.common.datascope.aspect;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.platform.common.core.constant.SystemConstants;
import com.platform.common.datascope.annotation.DataPermission;
import com.platform.common.datascope.config.ThreadLocalKeyConfig;
import com.platform.common.security.utils.SecurityUtils;
import com.platform.system.api.RemoteDataScopeService;
import com.platform.system.api.domain.request.SysDataScopeReq;
import com.platform.system.api.model.LoginUser;
import com.platform.system.api.model.SysOrgDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 数据权限更新切面处理类
 * @author: tr
 * @date: 2024年06月24日 10:29
 */
@Slf4j
@Aspect
@Component
public class DataPermissionUpdateAspect {

    @Resource
    private ThreadLocalKeyConfig threadLocalKeyConfig;

    @Resource
    private RemoteDataScopeService remoteDataScopeService;

    @After("@annotation(serviceDataPermission)")
    public void doBefore(JoinPoint point, DataPermission serviceDataPermission) throws Throwable
    {
        String dataType = serviceDataPermission.dataType();
        String operType = serviceDataPermission.operType();

        //获取主键ID
        Long id = threadLocalKeyConfig.get();
        Long[] ids = threadLocalKeyConfig.getLocals();
        if (ObjectUtil.isNotEmpty(id)){
            //主键ID不为空，则说明业务数据新增或删除成功，将当前业务数据id与用户所属的组织机构ID进行关联
            ids = new Long[]{id};
        }
        if (StrUtil.equals(operType, SystemConstants.OPER_TYPE_INSERT)){
            //新增操作
            LoginUser currentUser = SecurityUtils.getLoginUser();
            List<SysOrgDTO> orgList = currentUser.getOrgs();

            if (ObjectUtil.isNotEmpty(ids)){
                List<SysDataScopeReq> sysDataScopeReqList = new ArrayList<>();
                for (Long idKey : ids){
                    for (SysOrgDTO org : orgList){
                        SysDataScopeReq sysDataScopeReq = new SysDataScopeReq();
                        sysDataScopeReq.setDataId(idKey);
                        sysDataScopeReq.setOrgId(org.getId());
                        sysDataScopeReq.setOrgCode(org.getOrgCode());
                        sysDataScopeReq.setDataType(dataType);
                        sysDataScopeReqList.add(sysDataScopeReq);
                    }
                }
                remoteDataScopeService.saveBatch(sysDataScopeReqList);
            }
        }else if (StrUtil.equals(operType, SystemConstants.OPER_TYPE_DELETE)){
            //删除操作
            if (ObjectUtil.isNotEmpty(ids)){
                remoteDataScopeService.deleteByDataId(ids, dataType);
            }

        }
        //移除当前线程变量数据，防止内存溢出
        threadLocalKeyConfig.remove();
        threadLocalKeyConfig.removeLocals();
    }
}
