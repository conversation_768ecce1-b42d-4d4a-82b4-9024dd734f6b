package com.platform.common.datascope.config;

import org.springframework.stereotype.Component;

/**
 * @Description: 线程变量主键配置类
 * @author: tr
 * @date: 2024年06月24日 10:10
 */
@Component
public class ThreadLocalKeyConfig {

    private static ThreadLocal<Long> threadLocal = new ThreadLocal<>();

    private static ThreadLocal<Long[]> threadLocals = new ThreadLocal<>();

    public void set(Long value) {
        threadLocal.set(value);
    }

    public Long get() {
        return threadLocal.get();
    }

    public void remove() {
        threadLocal.remove();
    }

    public void setLocals(Long[] value) {
        threadLocals.set(value);
    }

    public Long[] getLocals() {
        return threadLocals.get();
    }

    public void removeLocals() {
        threadLocals.remove();
    }
}
