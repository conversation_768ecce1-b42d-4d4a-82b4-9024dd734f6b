package com.platform.common.datascope.handler;

import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * @Description:
 * @author: tr
 * @date: 2024年07月08日 16:49
 */
@Slf4j
@Component
@Intercepts({@Signature(method = "query", type = Executor.class
        , args = { MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class })
        , @Signature(method = "query", type = Executor.class
        , args = { MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})})
public class DataScopePermissionInterceptor extends JsqlParserSupport implements Interceptor {

    private DataScopePermissionHandler dataScopePermissionHandler;

    public void setDataScopePermissionHandler(DataScopePermissionHandler dataScopePermissionHandler) {
        this.dataScopePermissionHandler = dataScopePermissionHandler;
    }

    @Override
    public Object plugin(Object target) {
        return Interceptor.super.plugin(target);
    }

    @Override
    public void setProperties(Properties properties) {
        Interceptor.super.setProperties(properties);
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (dataScopePermissionHandler != null){
            dataScopePermissionHandler.buildDataPermission(invocation);
        }
        return invocation.proceed();
    }
}
