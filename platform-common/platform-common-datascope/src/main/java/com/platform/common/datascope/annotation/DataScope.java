package com.platform.common.datascope.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限过滤注解
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope
{

    /**
     * 关联表名
     */
    String joinTableName() default "sys_data_scope";
    /**
     * 关联表名的查询条件字段名，默认org_id
     */
    String joinField() default "org_code";
    /**
     * 权限字段名，默认为user_id
     */
    String permissionField() default "user_id";
    /**
     * 关联查询的字段名，默认为user_id
     */
    String joinSelectField() default "data_id";

    /**
     * 权限类型，1-自定义SQL权限，即根据SQL进行动态权限控制，2-根据字段进行权限控制，默认1
     */
    int permissionType() default 1;

    /** 权限过滤类型（1-组织机构过滤，2-行政区划（地区）过滤，默认组织机构过滤） **/
    String dataScopeType() default "1";

    /** 数据类型（1-角色、2-角色组），针对写入到sys_data_scope的数据类型判断 **/
    String dataType() default "";
}
