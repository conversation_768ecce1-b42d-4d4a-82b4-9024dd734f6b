package com.platform.syncdata.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.syncdata.api.RemoteSyncdataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @author: tr
 * @date: 2025年03月26日 19:53
 */
@Slf4j
@Component
public class RemoteSyncdataServiceFallbackFactory implements FallbackFactory<RemoteSyncdataService> {

    @Override
    public RemoteSyncdataService create(Throwable throwable) {
        log.error("同步数据服务调用失败:{}", throwable.getMessage());
        return new RemoteSyncdataService() {
            @Override
            public ResponseResult receiveMsg(String batchNumber) {
                return ResponseResult.fail("发送同步消息失败:" + throwable.getMessage());
            }
        };
    }
}
