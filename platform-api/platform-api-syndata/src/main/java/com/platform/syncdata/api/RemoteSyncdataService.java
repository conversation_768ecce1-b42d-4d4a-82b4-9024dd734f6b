package com.platform.syncdata.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.syncdata.api.factory.RemoteSyncdataServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 远程调用数据同步服务
 * @author: tr
 * @date: 2025年03月26日 19:52
 */
@FeignClient(contextId = "remoteSyncdataService", value = "${platform.syncdata.name}"
        , url = "${platform.syncdata.url}"
        , fallbackFactory = RemoteSyncdataServiceFallbackFactory.class)
public interface RemoteSyncdataService {

    @GetMapping("/syncdata/receiveMsg")
    ResponseResult receiveMsg(@RequestParam("batchNumber") String batchNumber);
}
