<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ctdi</groupId>
        <artifactId>ctdi-base</artifactId>
        <version>1.1.1</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.platform</groupId>
    <artifactId>platform</artifactId>
    <version>${revision}</version>

    <name>platform</name>

    <description>应用支撑平台微服务系统</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <revision>1.3.0</revision>
        <platform.message.version>1.0.0.RELEASE</platform.message.version>
        <ctdi-common-starter.version>1.2.2</ctdi-common-starter.version>
        <ctdi-ctgpaas.version>1.4.2</ctdi-ctgpaas.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- 接口模块 -->
            <dependency>
                <groupId>com.ctdi</groupId>
                <artifactId>ctdi-common-starter-swagger</artifactId>
                <version>${ctdi-common-starter.version}</version>
            </dependency>
            <!-- 接口模块 -->
            <dependency>
                <groupId>com.ctdi</groupId>
                <artifactId>ctdi-common-starter-share</artifactId>
                <version>${ctdi-common-starter.version}</version>
            </dependency>
            <!-- 工具模块 -->
            <dependency>
                <groupId>com.ctdi</groupId>
                <artifactId>ctdi-common-starter-toolbox</artifactId>
                <version>${ctdi-common-starter.version}</version>
            </dependency>
            <!-- mybatisPlus模块 -->
            <dependency>
                <groupId>com.ctdi</groupId>
                <artifactId>ctdi-common-starter-mybatisPlus</artifactId>
                <version>${ctdi-common-starter.version}</version>
            </dependency>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-datascope</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-datasource</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-seata</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏加密 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-desensitization</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-api-system</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- 同步数据接口 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-api-syndata</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- 业务工具 -->
            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>platform-common-toolbox</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.ctdi</groupId>
                <artifactId>ctdi-common-starter-cache</artifactId>
                <version>${ctdi-common-starter.version}</version>
            </dependency>
            <!-- 文件存储服务 -->
            <dependency>
                <groupId>com.ctdi</groupId>
                <artifactId>ctdi-common-starter-file</artifactId>
                <version>${ctdi-common-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>platform-gateway</module>
        <module>platform-modules</module>
        <module>platform-api</module>
        <module>platform-common</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <!-- 打包发布到二方库上的配置 -->
    <distributionManagement>
        <repository>
            <id>nexus-public</id>
            <name>artifactory-releases</name>
            <url>https://artifact.srdcloud.cn/artifactory/cthunan_hnsz-lshare-maven-mc</url>
        </repository>
    </distributionManagement>
</project>