## 平台简介

应用支撑平台致力于打造一套私有云、公有云可拔插的微服务架构体系，融合了服务架构、低代码、零代码等前沿技术，通过其灵活的架构思想和云服务组件，帮助用户构建、部署和管理各类云应用；有助于项目快速迭代业务场景，更高效地管理和利用云计算资源，提升运维效率和灵活性。

## 系统模块

~~~
com.platform     
├── platform-gateway                                     // 网关模块 [8080]
├── platform-auth                                        // 认证中心 [9200]
├── platform-api                                         // 接口模块
│       └── platform-api-system                          // 系统接口
├── platform-common          // 通用模块
│       └── platform-common-core                         // 核心模块
│       └── platform-common-datascope                    // 权限范围
│       └── platform-common-datasource                   // 多数据源
│       └── platform-common-log                          // 日志记录
│       └── platform-common-seata                        // 分布式事务
│       └── platform-common-security                     // 安全模块
├── platform-modules                                     // 业务模块
│       └── platform-system                              // 系统模块 [9211]
│       └── platform-gen                                 // 代码生成 [9202]
│       └── platform-job                                 // 定时任务 [9203]
│       └── platform-flow                                // 流程引擎 [9221]
├──pom.xml                                               // 公共依赖
~~~

## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限。
3.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
4.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
5.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
6.  参数管理：对系统动态配置常用参数。
7.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
8. 登录日志：系统登录日志记录查询包含登录异常。
9. 在线用户：当前系统中活跃用户状态监控。
10. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
11. 代码生成：前后端代码的生成（java、html、xml、sql）支持CRUD下载 。
12. 系统接口：根据业务代码自动生成相关的api接口文档。

## 版本迭代记录

1.  1.1.0版本 (2024-01-11)发布
2.  1.1.3版本 (2024-09-06)发布
       └── 从分支V1.1.0-20240111拉取
       └── 日志表新增描述（desc）字段，记录每个操作的描述
       └── 日志表新增应用code（app_code）字段，记录哪个应用系统的操作日志
3.  1.1.2版本 (2024-08-28)发布，主要变更内容有以下几点：  
    └── 数据表的主键字段由自增修改为雪花算法，主键字段返回给前端的数据类型由Long也修改为String。  
    └── sys_config表的config_id字段长度修改为20
4. 1.1.4版本 (2024-10-16)发布，主要变更内容有以下几点：  
   └── 组织机构表新增上级机构编码、机构层级路径和机构层级三个字段。  
   └── 组织机构新增导入功能
5.  1.2.0版本 (2024-08-22)发布，主要变更内容有以下几点：  
    └── 登录接口升级，采用Spring Security标准的认证方式实现；废除auth模块。
    └── 修改EnableRyFeignClients的名称为EnablePlatformFeignClients。
    └── 废弃platform-flow和platform-job模块
6.  1.2.1版本 (2024-11-29)发布，主要变更内容有以下几点：  
    └── OpenAPI接口，用户信息和部门信息接口升级为V2版本，增加分页查询功能。
    └── 修复验证码失效情况下，登录提示错误的问题。
    └── 修复网关刷新Token时效的问题。
7.  1.2.3版本 (2025-02-13)发布，主要变更内容有以下几点：  
    └── 新增短信登录功能。
    └── 新增同步数据下发模块，将用户和部门数据下发至kafka中。

