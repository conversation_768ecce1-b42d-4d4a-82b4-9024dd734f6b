spring:
  redis:
    host: ${cache.redis.host}
    port: ${cache.redis.port}
    password: ${cache.redis.password}
    database: ${cache.redis.database}
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: platform-auth
          uri: lb://platform-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - StripPrefix=1
        # 代码生成
        - id: platform-gen
          uri: lb://platform-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: platform-job
          uri: lb://platform-job
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: platform-system
          uri: lb://platform-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: platform-file
          uri: lb://platform-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
        # 监控服务
        - id: platform-monitor
          uri: lb://platform-monitor
          predicates:
            - Path=/monitor/**
          filters:
            - StripPrefix=1
        # 消息中心
        - id: message-center
          uri: lb://message-center-service
          predicates:
            - Path=/message-center/**
          filters:
            - StripPrefix=1
        # 登录登出
        - id: platform-login
          #uri: lb://platform-auth
          uri: lb://platform-system
          predicates:
            - Path=/login,/logout
        # 路由和个人信息
        - id: platform-info
          uri: lb://platform-system
          predicates:
            - Path=/getInfo,/getRouters,/captchaImage
        # 小程序低码
        - id: hndx-app
          uri: lb://hndx-app
          predicates:
            - Path=/app/**
        # 流程引擎
        - id: platform-flow
          uri: lb://platform-flow
          predicates:
            - Path=/flowable/**

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
      - /flowable/definition/save
  # 不校验白名单
  ignore:
    whites:
      - /logout
      - /login
      - /captchaImage
      - /auth/register
      - /*/v2/api-docs
      - /csrf
      - /*/openapi/**