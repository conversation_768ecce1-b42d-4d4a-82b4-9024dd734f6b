cache:
  redis: 
    host: 127.0.0.1
    port: 6379
    password:
    database: 0
    enabled: true  # true-使用redis作为缓存，false-不使用；与ctg.cache.enabled互斥

ctg:
  cache:
    # true-使用ctg-cache作为缓存，false-不使用；与cache.redis.enabled互斥
    enabled: false
    #接入机地址
    hostAndPort: 127.0.0.1:31086
    #最大连接数（空闲+使用中）
    maxTotal: 100
    #最大空闲连接数
    maxIdle: 10
    #保持的最小空闲连接数
    minIdle: 3
    #借出连接时最大的等待时间
    maxWaitMillis: 3000
    #分组对应的桶位
    database:
    #鉴权信息
    auth:
    #后台监控执行周期，毫秒
    period: 3000
    #后台监控ping命令超时时间,毫秒
    monitorTimeout: 200