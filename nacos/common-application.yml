spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
      min-request-size: 30000
    response:
      enabled: true

# 暴露监控端点
management:
  endpoints:
    web:
      exposure:
        include: '*'

mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.platform.system,com.common.demo.mapper
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:/mapper/system/*.xml,classpath*:/mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mybatis-plus:
  mapper-locations: classpath:/mapper/system/*.xml,classpath*:/mapper/*.xml
  type-aliases-package: com.platform.system,com.common.demo.mapper
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# swagger配置
swagger:
  enabled: true #是否开启swagger，生产环境一般关闭，true-开启，false-关闭
# 认证模块调用系统模块的接口配置，name为服务名，url为接口地址
platform:
  system:
    name: platform-system
    url:
  # 统一消息中心的配置，短信发送功能，name为服务名，url为接口地址
  message: 
    name: message-center-service
    url:
  syncdata:
    name: platform-syncdata
    url:

security: 
  # 验证码，true-开启，false-关闭
  captcha:
    enabled: false
    # 验证码类型，number-数字，char-字符，默认使用数字计算方式
    type: number
# 文件存储的配置，minio对象存储，ctyunoss-天翼云OSS，minio和ctyunoss互斥，二选一
file: 
  minio:
    # true-启用minio，false-关闭；与file.ctyunoss.enabled互斥
    enabled: false
    serverUrl: http://127.0.0.1:9000
    accessKey: accessKey
    secretKey: secretKey
    bucketName: bucketName
    deadline: 1
    timeUnit: minute
  ctyunoss:
    # true-启用ctyunoss，false-关闭；与file.minio.enabled互斥
    enabled: true
    endpoint: https://127.0.0.1
    accessKey: accessKey
    secretKey: secretKey
    bucketName: bucketName
    domainApp: https://127.0.0.1
    roleArn: roleArn
# 短信发送的配置，appKey和secretKey由统一消息中心提供
message:
  sms:
    appKey:
    secretKey:
    # 验证码的模板
    validateContent: 【应用支撑平台】你的验证码为%s，有效期5分钟
    # 忘记密码的模板
    forgetPwdContent: 【应用支撑平台】你的验证码为%s，有效期5分钟
    type: 2
core: 
  info:
    key: platformauth8888
#JWT的白名单配置，不需要验证的路径
jwt: 
  antMatchers: /captchaImage,/operlog/save,/logininfor/save,/webSocket/**, /refresh,/swagger-resources/**,/swagger-ui/**,/**/api-docs,/openapi/**,/doc.html,/favicon.ico