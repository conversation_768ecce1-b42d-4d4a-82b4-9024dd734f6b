# spring配置
spring:
  redis:
    host: ${cache.redis.host}
    port: ${cache.redis.port}
    password: ${cache.redis.password}
    database: ${cache.redis.database}
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    initial-size: 5
    min-idle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,slf4j
    connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
    driver-class-name: ${datasource.platform.driver}
    url: ${datasource.platform.url}
    username: ${datasource.platform.username}
    password: ${datasource.platform.password}

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.platform.gen
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml