# spring配置
spring:
  redis:
    host: ${cache.redis.host}
    port: ${cache.redis.port}
    password: ${cache.redis.password}
    database: ${cache.redis.database}
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          # 主库数据源
          master:
            driver-class-name: ${datasource.platform.driver}
            url: ${datasource.platform.url}
            username: ${datasource.platform.username}
            password: ${datasource.platform.password}
          # 从库数据源
          slave:
            driver-class-name: ${datasource.platform.driver}
            url: ${datasource.platform.url}
            username: ${datasource.platform.username}
            password: ${datasource.platform.password}

security: 
  ignore: 
    whites: 
      - /user/info/*
      - /operlog/save
      - /logininfor/save
      - /receive/receiveMsg
