# spring配置
spring:
  redis:
    host: ${cache.redis.host}
    port: ${cache.redis.port}
    password: ${cache.redis.password}
  datasource:
    #数据库驱动
    driver-class-name: ${datasource.platform.driver}
    #数据库连接
    url: ${datasource.platform.url}
    #数据库连接池
    type: com.zaxxer.hikari.HikariDataSource
    #数据库用户名
    username: ${datasource.platform.username}
    password: ${datasource.platform.password}
    hikari:
      #      最小空闲链接数
      minimumIdle: 5
      #      最大链接数
      maximumPoolSize: 50
      #      最大生命周期
      maxLifetime: 180000
      #      最长闲置时间
      idleTimeout: 60000
      #      等待连接池的最大毫秒数
      connectionTimeout: 20000
    #是否自定义配置
    cachePrepStmts: true
    #连接池大小
    prepStmtCacheSize: 250
    #单条语句最大长度
    prepStmtCacheSqlLimit: 2048
    #新版本MySQL支持服务器端准备，是否开启
    useServerPrepStmts: true